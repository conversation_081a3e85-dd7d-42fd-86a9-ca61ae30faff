module.exports = {
  indexPageTemplate: `<template>
  <div class="container">
    <PageWrapper
      ref="myRef"
      title="人物名单"
      createText="创建人物"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      @loadData="loadData"
      @handleCreate="handleCreate"
    >
      <!-- filter插槽 -->
      <template #identity="{ item }">
        <a-input v-model="filterOptions.params[item.field]" placeholder="AutoFilter插槽" />
      </template>
      <!-- table插槽 -->
      <template #operate="{ row }">
        <span class="operate-button" @click="onClickEdit(row)">编辑</span>
      </template>
      <!-- 编辑弹窗 -->
      <Modal :visible="visible" :preview="preview" :detail="detail" @handleCancel="handleCancel" />
    </PageWrapper>
  </div>
</template>


<script>
import Modal from './components/Modal.vue';
// 接口地址，默认路径：@/api/
// 以下是默认接口名：
// 查询接口：queryList
// 删除接口：deleteItem
import { queryList, deleteItem } from '@/api/';
import { setConfigOptions } from '@bangdao/pro-components';
// TODO：请在这个位置继续生成配置项代码
export default {
  components: { Modal },
  data() {
    return {
      loading: false,
      filterOptions,
      // 分页器配置
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      // 表头
      tableColumn: tableColumn(),
      tableData: [],
      visible: false,
      preview: false,
      detail: {},
    };
  },
  created() {
    this.loadData();
    // 快捷设置options
    setConfigOptions(this.filterOptions.config, 'type', [
      { value: '', label: '全部' },
      { value: '1', label: '工地搬砖' },
      { value: '2', label: '公司搬砖' },
    ]);
  },
  methods: {
    // 请求接口数据
    async loadData() {
      this.loading = true;
      const params = this.filterOptions.params;
      const [res, err] = await queryList({
        limit: this.tablePage.pageSize,
        page: this.tablePage.currentPage,
        ...params,
      });
      this.loading = false;
      if (err) return;
      // 设置数据
      this.tablePage.total = res.count;
      this.tableData = res.data;
    },
    handleCreate() {
      this.visible = true;
    },
    handleCancel(update) {
      if (update) {
        this.loadData();
      }
      this.visible = false;
      this.preview = false;
      this.detail = {};
    },
    onClickDetail() {
      this.detail = {};
      this.preview = true;
      this.visible = true;
    },
    onClickEdit(row) {
      console.log('编辑', row);
      this.detail = { ...row };
      this.visible = true;
    },
    async onClickDelete(row) {
      const [result, error] = await deleteItem(row.id);
      if (error) {
        this.$message.error('删除失败');
      }
      if (result) {
        this.$message.success('删除成功');
        this.loadData();
      }
    }
  },
};
</script>
<style lang="less" scoped>
.container {
  padding: 24px;
  background-color: #f4f4f4;
}
// table操作按钮
.operate-button {
  display: inline-block;
  margin-right: 16px;
  padding: 8px 0;
  color: #1677ff;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  cursor: pointer;
}
</style>
`,
  modalComponentTemplate: `<template>
  <a-modal
    width="800px"
    title="编辑"
    :visible="visible"
    cancelText="取消"
    @ok="handleCreate"
    @cancel="handleCancel()"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <DynamicForm
        ref="myRef"
        layout="vertical"
        :preview="preview"
        :params="params"
        :config="config"
      >
      </DynamicForm>
    </a-spin>
  </a-modal>
</template>

<script>
// TODO：请继续在此位置生成配置项代码
// 接口地址，默认路径：@/api/
// 以下是默认接口名：
// 新增接口：addItem
// 编辑接口：editItem
import { addItem, editItem } from '@/api/';
export default {
  props: ['visible', 'preview', 'detail'],
  watch: {
    visible(val) {
      if (val && this.detail && this.detail.name) {
        this.params = { ...this.detail };
      } else {
        this.params = { name: '', type: '', startTime: '', endTime: '', desc: '' };
      }
    },
  },
  data() {
    return {
      loading: false,
      params: { name: '', type: '', startTime: '', endTime: '', desc: '' },
      config: formConfig
    };
  },
  methods: {
    async handleCreate() {
      this.$refs.myRef.validate(async (valid) => {
        if (valid) {
          if (this.detail && this.detail.name) {
            const [result, error] = await editItem(this.params);
            if (error) {
              this.$message.error('编辑失败');
            }
            if (result) {
              this.$message.success('编辑成功');
              this.handleCancel(true);
            }
          } else {
            const [result, error] = await addItem(this.params);
            if (error) {
              this.$message.error('新增失败');
            }
            if (result) {
              this.$message.success('新增成功');
              this.handleCancel(true);
            }
          }
        }
      });
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
  },
};
</script>

<style lang="less" scoped></style>
`,
};
