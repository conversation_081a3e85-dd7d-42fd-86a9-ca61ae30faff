# 工单系统技术栈说明

## 项目概述

本项目是一个基于Vue.js的工单管理系统，采用现代化的前端技术栈构建，提供完整的工单处理、短信管理、访客管理等功能。

## 主要技术栈

### 前端框架
- **Vue.js**: 2.7.14
  - 采用Vue 2.x版本，支持Composition API
  - 提供响应式数据绑定和组件化开发

### UI组件库
- **Ant Design Vue**: 1.7.8
  - 企业级UI设计语言和React组件库的Vue实现
  - 提供丰富的组件和设计规范
  - 支持主题定制和国际化

### 路由管理
- **Vue Router**: 3.x
  - Vue.js官方路由管理器
  - 支持嵌套路由、路由守卫、懒加载等功能

### 状态管理
- **Vuex**: 3.6.2
  - Vue.js官方状态管理模式
  - 集中式存储管理应用的所有组件状态
  - 模块化设计：base、dict、setting等模块

### 表格组件
- **VXE Table**: 3.6.6
  - 功能强大的Vue表格组件
  - 支持虚拟滚动、树形结构、编辑等高级功能
- **VXE Table Plugin Antd**: 1.11.3
  - VXE Table与Ant Design Vue的集成插件

### 自定义组件库
- **@bangdao/pro-components**: 1.2.7
  - 邦道科技专业组件库
  - 提供业务相关的高级组件
- **@bangdao/buse-components**: 0.2.9
  - 邦道科技基础组件库
  - 包含BuseCrud、BuseRangePicker、AutoFilters等核心组件

### 构建工具
- **Vue CLI**: 5.0.0
  - Vue.js官方脚手架工具
  - 基于Webpack的构建系统
  - 支持热重载、代码分割、优化等功能

### 样式预处理器
- **Less**: 3.13.1
  - CSS预处理器
  - 支持变量、嵌套、混合等功能
- **Less Loader**: 6.2.0
  - Webpack的Less加载器

### 网络请求
- **Axios**: 1.3.4
  - 基于Promise的HTTP客户端
  - 支持请求/响应拦截器、请求取消、自动转换JSON等

### 工具库
- **Moment.js**: 2.29.4
  - JavaScript日期处理库
  - 支持日期解析、验证、操作和显示
- **Lodash**: 内置多个工具函数
  - JavaScript实用工具库
- **ECharts**: 5.4.3
  - 百度开源的数据可视化库
- **Crypto-js**: 4.1.1
  - JavaScript加密库
- **JSEncrypt**: 3.2.1
  - RSA加密库

### 编辑器
- **Vue2 Editor**: 2.10.3
  - 基于Quill.js的富文本编辑器
- **Quill相关插件**:
  - quill-image-drop-module: 图片拖拽上传
  - quill-image-resize-module: 图片大小调整

### 开发工具
- **ESLint**: 7.32.0
  - JavaScript代码检查工具
  - 配合Prettier进行代码格式化
- **Prettier**: 2.8.7
  - 代码格式化工具
- **Lint Staged**: 11.2.6
  - Git提交前代码检查

## 项目结构

```
src/
├── api/                    # API接口定义
│   ├── common.js          # 通用API
│   ├── sms/               # 短信相关API
│   └── system/            # 系统相关API
├── assets/                # 静态资源
│   ├── icons/             # 图标文件
│   └── images/            # 图片文件
├── components/            # 公共组件
│   ├── Charts/            # 图表组件
│   ├── DictData/          # 字典数据组件
│   ├── Editor/            # 编辑器组件
│   ├── Upload/            # 上传组件
│   └── ...
├── global/                # 全局配置
│   ├── component/         # 全局组件注册
│   ├── directive/         # 全局指令
│   └── style/             # 全局样式
├── layouts/               # 布局组件
│   ├── AdminLayout.vue    # 管理后台布局
│   ├── menu/              # 菜单组件
│   └── navHeader/         # 导航头部
├── router/                # 路由配置
│   ├── config.js          # 路由配置
│   ├── guards.js          # 路由守卫
│   └── index.js           # 路由入口
├── store/                 # Vuex状态管理
│   ├── modules/           # 状态模块
│   └── index.js           # Store入口
├── utils/                 # 工具函数
│   ├── buse.js            # 业务工具
│   └── system/            # 系统工具
├── views/                 # 页面组件
│   ├── home/              # 首页相关
│   ├── sms/               # 短信管理
│   ├── workbench/         # 工作台
│   └── workorder/         # 工单管理
├── App.vue                # 根组件
├── bootstrap.js           # 启动配置
└── main.js                # 应用入口
```

## 开发环境配置要求

### Node.js版本
- 推荐使用Node.js 16.x或更高版本
- npm版本 8.x或更高

### 开发工具
- **IDE**: 推荐使用VS Code
- **浏览器**: Chrome 90+或Firefox 88+
- **Git**: 版本控制工具

### 环境变量配置
项目支持多环境配置，通过环境变量控制：
- `VUE_APP_BASE_API`: API基础地址
- `VUE_APP_SYSTEM_API`: 系统API地址
- `VUE_APP_USE_BUILD_TYPE`: 构建类型标识

### 安装和启动
```bash
# 安装依赖
npm install
# 或
yarn install

# 启动开发服务器
npm run serve
# 或
yarn serve

# 构建生产版本
npm run build
# 或
yarn build

# 代码检查
npm run lint
# 或
yarn lint
```

## 特色功能

### 1. 组件化开发
- 基于Vue组件化思想，提供可复用的业务组件
- BuseCrud组件提供完整的CRUD操作界面
- 支持插槽和事件自定义

### 2. 主题定制
- 基于Ant Design Vue的主题系统
- 支持Less变量覆盖
- 全局样式统一管理

### 3. 权限控制
- 基于指令的权限控制系统
- 支持按钮级别的权限管理
- 路由级别的访问控制

### 4. 国际化支持
- 内置中文语言包
- 支持多语言扩展

### 5. 响应式设计
- 适配不同屏幕尺寸
- 移动端友好的交互体验

## 性能优化

### 1. 代码分割
- 路由级别的懒加载
- 第三方库分离打包
- Ant Design Vue单独打包

### 2. 资源优化
- 图片压缩和懒加载
- CSS和JS文件压缩
- Gzip压缩支持

### 3. 缓存策略
- 浏览器缓存配置
- API响应缓存
- 静态资源长期缓存

## 部署说明

### 构建配置
- 生产环境关闭source map
- 静态资源CDN部署
- 支持Docker容器化部署

### 环境要求
- Web服务器：Nginx、Apache等
- 支持HTML5 History模式
- HTTPS协议支持（推荐）
