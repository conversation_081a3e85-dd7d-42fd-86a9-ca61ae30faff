# 客户列表页面

## 功能概述

这是一个基于 BuseCrud 组件的客户管理页面，提供完整的客户信息增删改查功能。

## 主要特性

### 1. 列表展示

- 显示客户信息列表，包含：客户姓名、联系电话、客户类型、是否 KP、岗位名称、意向程度、公司行业、公司名称、所在省市、当前跟进人、创建人、创建时间字段
- 支持分页显示
- 支持筛选和搜索功能

### 2. 数据唯一性约束

- **重要**：客户表以手机号为唯一标识，同一手机号只能存在一条记录
- 新增时：联系电话输入框失焦时检查手机号是否已存在，如存在则提示"该手机号已存在！"
- 编辑时：修改手机号后失焦时检查新手机号是否已存在，如存在则提示"该手机号已存在！"

### 3. 表单字段配置

#### 必填字段

1. **客户姓名**：

   - 必填
   - 支持少数民族姓名中的"·"符号
   - 最大长度：100 个字符
   - 验证规则：只能包含中文字符和"·"符号

2. **联系电话**：
   - 必填
   - 11 位手机号格式
   - 暂不做手机号有效性校验（按需求要求）

#### 非必填字段（支持自动完成的下拉选择）

3. **客户类型**：非必填，单选，最大长度：100 个字符
4. **公司/行业**：非必填，单选，最大长度：100 个字符
5. **意向程度**：非必填，单选，最大长度：100 个字符
6. **客户等级**：非必填，单选，最大长度：100 个字符
7. **公司名称**：非必填，单选，最大长度：100 个字符
8. **跟进人**：非必填，单选，数据源：数据库存储的跟进人 + 当前租户下的账号名称（去重）

#### 其他字段

9. **微信**：非必填，最大长度：100 个字符
10. **邮箱**：非必填，最大长度：100 个字符，邮箱格式验证
11. **所在省市**：非必填，使用级联选择器，允许只选择省级
12. **是否 KP**：默认值：否，选择是/否后都显示"岗位名称"输入框
13. **客户备注信息**：非必填，最大长度：1000 个字符，使用 textarea
14. **客户标签**：非必填，多选，支持自动完成的下拉选择，多个标签用逗号分隔

## 文件结构

```
src/
├── api/
│   └── customer/
│       └── index.js          # 客户相关API接口
├── views/
│   └── customer/
│       └── list/
│           ├── index.vue     # 客户列表页面组件
│           └── README.md     # 本文档
└── router/
    └── config.js             # 路由配置（已添加客户列表路由）
```

## API 接口

### 基础 CRUD 接口

- `customerListAPI` - 客户列表查询
- `customerAddAPI` - 客户新增
- `customerUpdateAPI` - 客户更新
- `customerDetailAPI` - 客户详情
- `customerDeleteAPI` - 客户删除
- `customerExportAPI` - 客户导出

### 唯一性验证接口

- `checkPhoneExistAPI` - 检查手机号是否已存在

### 自动完成选项接口

- `getCustomerTypeOptionsAPI` - 获取客户类型选项
- `getCompanyIndustryOptionsAPI` - 获取公司行业选项
- `getIntentionLevelOptionsAPI` - 获取意向程度选项
- `getCustomerLevelOptionsAPI` - 获取客户等级选项
- `getCompanyNameOptionsAPI` - 获取公司名称选项
- `getFollowUserOptionsAPI` - 获取跟进人选项
- `getPositionOptionsAPI` - 获取岗位名称选项
- `getCustomerTagOptionsAPI` - 获取客户标签选项

## 路由配置

路由路径：`/customerlist-w`
组件路径：`@/views/customer/list`

## 权限控制

- `system:customer:add` - 新增客户权限
- `system:customer:edit` - 编辑客户权限
- `system:customer:delete` - 删除客户权限
- `system:customer:export` - 导出客户权限

## 使用说明

1. 页面加载时自动获取客户列表数据
2. 支持通过筛选条件查询客户
3. 点击"新增"按钮可添加新客户
4. 点击表格行的"编辑"按钮可修改客户信息
5. 点击表格行的"删除"按钮可删除客户
6. 点击"导出"按钮可导出客户列表

## 注意事项

1. 手机号作为唯一标识，系统会自动检查重复
2. 所有自动完成字段都支持三种交互方式：手动输入新值、模糊搜索、下拉选择
3. 省市数据使用国家统计局标准数据
4. 客户标签支持多选，用逗号分隔存储
5. 是否 KP 字段选择后都会显示岗位名称输入框
