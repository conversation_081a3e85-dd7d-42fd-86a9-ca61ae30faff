# 网络接口封装使用说明

## 概述

本项目基于Axios封装了完整的网络请求解决方案，提供了统一的API调用方式、请求/响应拦截器、错误处理机制、Mock支持等功能。

## 核心架构

### 文件结构
```
src/utils/system/request/
├── index.js              # 主入口文件
├── request.js            # 主要请求封装
├── requestMock.js        # Mock请求处理
├── requestJson.js        # JSON文件请求
├── mockList.js           # Mock接口列表
└── handles/              # 处理器目录
    ├── index.js          # 处理器入口
    ├── response.js       # 响应处理
    ├── reLogin.js        # 重新登录处理
    └── encryptionAndDecrypt.js # 加密解密处理
```

## 基础配置

### 1. Axios实例创建
```javascript
import axios from 'axios';

// 获取基础URL
const BASE_URL = process.env.VUE_APP_USE_BUILD_TYPE 
  ? getBaseUrl() 
  : process.env.VUE_APP_BASE_API;

// 创建axios实例
export const myAxios = axios.create({
  baseURL: BASE_URL,
  timeout: 30000, // 30秒超时
});

// 设置默认请求头
myAxios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8';
```

### 2. 环境变量配置
项目支持以下环境变量：
- `VUE_APP_BASE_API`: 开发环境API基础地址
- `VUE_APP_SYSTEM_API`: 系统API地址
- `VUE_APP_MOCK_API`: Mock API地址
- `VUE_APP_USE_BUILD_TYPE`: 是否使用构建类型

## 请求拦截器

### 功能特性
1. **自动添加认证Token**
2. **生产环境参数加密**
3. **统一错误处理**

### 实现代码
```javascript
myAxios.interceptors.request.use(
  (config) => {
    // 添加认证Token
    if (getToken()) {
      config.headers.Authorization = getToken();
    }
    
    // 非开发环境请求参数加密
    if (process.env.NODE_ENV !== 'development') {
      config.params = paramsEncryption(config.params);
      config.data = paramsEncryption(config.data);
    }
    
    return config;
  },
  () => {
    return Promise.reject({
      message: '系统异常，请重试'
    });
  }
);
```

## 响应拦截器

### 基础响应处理
```javascript
myAxios.interceptors.response.use(
  (response) => {
    if (response.status !== 200) {
      return Promise.reject({
        message: '系统异常，请重试'
      });
    }
    return response;
  },
  () => {
    return Promise.reject({
      message: '系统异常，请重试'
    });
  }
);
```

### 高级响应处理
项目实现了统一的响应处理机制：

```javascript
export const responseHandle = (response, extend = { errorCustom: false }) => {
  const { errorCustom } = extend;
  const { data = {}, config = {}, status, msg } = response;

  // HTTP状态码检查
  if (status !== 200) {
    commonErrorHandle(errorCustom, msg);
    return [undefined, { code: status, message: msg }];
  }

  const { code, subCode } = data;
  
  // 登录状态检查
  if (subCode === 'auth-check-error' && config.url !== '/api/authority/admin/logout') {
    throttledConfirmModal(); // 弹出重新登录提示
    return [undefined, { code: subCode, message: '登录过期或者不合法' }];
  }
  
  // 业务状态码检查
  if (code !== '10000') {
    commonErrorHandle(errorCustom, data.subMsg || data.msg);
    return [undefined, { code, message: data.subMsg || data.msg, subCode: data.subCode }];
  }
  
  return [data, undefined];
};
```

## 错误处理机制

### 1. 通用错误处理
```javascript
function commonErrorHandle(errorCustom, message) {
  if (!errorCustom) {
    notification.error({
      message,
    });
  }
}
```

### 2. 登录过期处理
```javascript
import { throttle } from 'lodash';

export const throttledConfirmModal = throttle(
  () => {
    Modal.confirm({
      title: '系统提示',
      content: '登录状态已过期或者不合法，您可以继续留在该页面，或者重新登录',
      okText: '重新登录',
      cancelText: '取消',
      onOk() {
        store.dispatch('base/FedLogOut').finally(() => {
          router.push(redirectLogin());
        });
      },
    });
  },
  3000,
  { trailing: false }
);
```

## API函数定义规范

### 1. 基础API函数结构
```javascript
import request from '@/utils/system/request';

// 获取列表数据
export function getList(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/api/list',
    method: 'post',
    data: params,
  });
}

// 文件上传
export function upload(file) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/api/upload',
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 文件下载
export function download(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/api/download',
    method: 'post',
    data: params,
    responseType: 'blob', // 重要：下载文件需要设置responseType
  });
}
```

### 2. API函数命名规范
- **查询列表**: `getXxxList`
- **获取详情**: `getXxxDetail` 或 `getXxx`
- **新增数据**: `addXxx` 或 `createXxx`
- **更新数据**: `updateXxx` 或 `editXxx`
- **删除数据**: `deleteXxx` 或 `removeXxx`
- **导出数据**: `exportXxx`
- **上传文件**: `uploadXxx`

### 3. 参数传递规范
```javascript
// GET请求使用params
export function getDetail(id) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/api/detail',
    method: 'get',
    params: { id },
  });
}

// POST请求使用data
export function createItem(data) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/api/create',
    method: 'post',
    data,
  });
}

// 路径参数直接拼接
export function getItemById(id) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + `/api/item/${id}`,
    method: 'get',
  });
}
```

## Mock支持

### 1. Mock配置
```javascript
// mockList.js
export const mockList = [
  '/api/mock/test',
  '/api/mock/user',
];
```

### 2. Mock请求处理
```javascript
export const requestMock = async (config) => {
  try {
    let res = await myAxios(config);
    return [res.data, undefined];
  } catch (error) {
    return [undefined, { status: 400, msg: error.message }];
  }
};
```

## 加密解密支持

### 1. 参数加密
```javascript
// 生产环境自动加密请求参数
export const paramsEncryption = (params) => {
  // 实际项目中可以在这里实现加密逻辑
  return params;
};
```

### 2. 响应解密
```javascript
// 生产环境自动解密响应数据
export const dataDecrypt = (data) => {
  // 实际项目中可以在这里实现解密逻辑
  return data;
};
```

## 使用示例

### 1. 在组件中使用API
```javascript
import { getOrderList, createOrder } from '@/api/system/workorder';

export default {
  data() {
    return {
      loading: false,
      dataList: [],
    };
  },
  async mounted() {
    await this.loadData();
  },
  methods: {
    // 加载数据
    async loadData() {
      this.loading = true;
      const [res, err] = await getOrderList({ page: 1, size: 10 });
      this.loading = false;
      
      if (err) {
        // 错误已经在拦截器中处理，这里可以做额外处理
        console.error('加载数据失败:', err);
        return;
      }
      
      this.dataList = res.data || [];
    },
    
    // 创建数据
    async handleCreate(formData) {
      const [res, err] = await createOrder(formData);
      
      if (err) {
        return;
      }
      
      this.$message.success('创建成功');
      await this.loadData(); // 重新加载数据
    },
  },
};
```

### 2. 自定义错误处理
```javascript
// 使用errorCustom参数禁用默认错误提示
const [res, err] = await getOrderList(params, { errorCustom: true });

if (err) {
  // 自定义错误处理
  this.$message.error(`自定义错误提示: ${err.message}`);
}
```

## 最佳实践

### 1. 统一返回格式
所有API函数都返回 `[data, error]` 格式：
- 成功时：`[responseData, undefined]`
- 失败时：`[undefined, errorObject]`

### 2. 错误处理策略
- 网络错误和HTTP错误：自动显示通知
- 业务错误：根据业务需要选择是否自动显示
- 登录过期：自动弹出重新登录确认框

### 3. 请求优化
- 合理设置超时时间（30秒）
- 生产环境启用参数加密
- 支持请求取消（可扩展）

### 4. 开发调试
- 开发环境支持Mock数据
- 详细的错误日志输出
- 支持请求/响应拦截器调试

## 注意事项

1. **Token管理**: 确保Token的获取和存储安全
2. **环境变量**: 不同环境使用不同的API地址
3. **错误处理**: 合理使用errorCustom参数控制错误提示
4. **文件上传**: 注意设置正确的Content-Type
5. **文件下载**: 必须设置responseType为'blob'
6. **Mock数据**: 生产环境必须清空mockList配置
