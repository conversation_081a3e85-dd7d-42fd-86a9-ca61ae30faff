# 数据流和状态管理使用说明

## 概述

本项目采用Vuex作为状态管理解决方案，实现了模块化的状态管理架构。通过集中式存储管理应用的所有组件状态，确保数据流的可预测性和可维护性。

## Vuex架构设计

### 模块结构
```
src/store/
├── index.js              # Store主入口
└── modules/              # 状态模块
    ├── base.js           # 基础模块（用户、权限、路由等）
    ├── dict.js           # 字典数据模块
    └── setting.js        # 设置模块（菜单、布局等）
```

### Store配置
```javascript
import Vue from 'vue';
import Vuex from 'vuex';
import base from './modules/base';
import dict from './modules/dict';
import setting from './modules/setting';

Vue.use(Vuex);

const store = new Vuex.Store({
  modules: {
    base,      // 基础数据模块
    setting,   // 设置模块
    dict,      // 字典模块
  },
});

export default store;
```

## 模块详细说明

### 1. Base模块 (base.js)

#### State状态
```javascript
state: {
  // 用户认证相关
  token: getToken(),                    // 用户Token
  name: getUserProperty('userName'),    // 用户姓名
  username: getUserProperty('userName'), // 用户名
  avatar: getUserProperty('avatar'),    // 用户头像
  email: getUserProperty('email'),      // 用户邮箱
  user: getUser(),                      // 用户完整信息
  merchant: getUserProperty('merchant'), // 商户信息
  
  // 权限和路由相关
  permissions: [],                      // 用户权限列表
  routes: [],                          // 完整路由
  addRoutes: [],                       // 动态路由
  sysApps: [],                         // 系统应用列表
  
  // 业务数据
  orderSort: [],                       // 工单分类
  userList: [],                        // 用户列表
  
  // 页面状态管理
  orderId: [],                         // 工单ID列表
  arrStatus: [],                       // 工单状态列表
  arrTime: [],                         // 工单时间列表
  cumId: '',                           // 通话记录ID
  mainUniqueId: '',                    // 主要唯一ID
  isCall: false,                       // 通话状态
  customerNumber: '',                  // 客户号码
  tabIndex: '',                        // 工作台Tab索引
  callNumber: '',                      // 呼出号码
}
```

#### Getters计算属性
```javascript
getters: {
  permissions: (state) => {
    return state.permissions;
  },
}
```

#### Mutations同步操作
```javascript
mutations: {
  // 设置用户基础信息
  SET_BASE_INFO: (state, data) => {
    const { merchant = {}, user = {}, sysApps = [], permissions = [] } = data;
    state.name = user.userName || '';
    state.username = user.userName || '';
    state.avatar = require('@/assets/images/profile.jpg');
    state.permissions = permissions;
    state.user = user;
    state.sysApps = sysApps;
    state.merchant = merchant;
    setUser({ ...user, merchant: merchant });
  },
  
  // 设置Token
  SET_TOKEN: (state, token) => {
    state.token = token;
    setToken(token);
  },
  
  // 重置状态
  RESET_STATE: (state) => {
    removeToken();
    removeTokenWithDomain();
    state.token = '';
    state.name = '';
    state.avatar = '';
    state.user = {};
    removeUser();
    state.sysApps = [];
    state.permissions = [];
    state.routes = [];
    state.addRoutes = [];
  },
}
```

#### Actions异步操作
```javascript
actions: {
  // 获取用户信息
  async GetInfo({ commit }) {
    const token = getToken();
    const [res, err] = await getInfo(token);
    if (err) throw new Error(err);
    const { data } = res;
    commit('SET_BASE_INFO', data);
    return data;
  },
  
  // 用户登录
  async Login({ commit }, { username, password, code, requestNo }) {
    commit('RESET_STATE');
    const [res, err] = await login(username, password, code, requestNo);
    if (err) return [undefined, err];
    const token = res?.data?.token;
    commit('SET_TOKEN', token);
    return ['success', undefined];
  },
  
  // 生成动态路由
  async GenerateRoutes({ commit }, { appId = '' }) {
    if (!appId) return;
    const [systemRes, err] = await getRouters({ appId });
    if (err) throw new Error(err);
    let systemRoutes = filterAsyncRouter(systemRes.data);
    const dynamicRoutes = systemRoutes;
    commit('SET_ROUTES', dynamicRoutes);
    return dynamicRoutes;
  },
}
```

### 2. Dict模块 (dict.js)

#### 字典数据管理
```javascript
state: {
  dict: new Array(), // 字典数据数组
},

mutations: {
  // 设置字典
  SET_DICT: (state, { key, value }) => {
    if (key !== null && key !== '') {
      state.dict.push({ key: key, value: value });
    }
  },
  
  // 删除字典
  REMOVE_DICT: (state, key) => {
    for (let i = 0; i < state.dict.length; i++) {
      if (state.dict[i].key == key) {
        state.dict.splice(i, i);
        return true;
      }
    }
  },
  
  // 清空字典
  CLEAN_DICT: (state) => {
    state.dict = new Array();
  },
},

actions: {
  // 获取字典
  getDict({ state }, dictKeys) {
    if (dictKeys == null && dictKeys == '') {
      return null;
    }
    for (let i = 0; i < state.dict.length; i++) {
      if (state.dict[i].key == dictKeys) {
        return state.dict[i].value;
      }
    }
  },
  
  // 批量获取字典
  getDicts({ dispatch }, dictKeys) {
    if (!dictKeys) return;
    let dictKeysArr = Array.isArray(dictKeys) ? dictKeys : [dictKeys];
    return Promise.all(
      dictKeysArr.map((dictKey) => {
        return dispatch('getDict', dictKey);
      })
    );
  },
}
```

### 3. Setting模块 (setting.js)

#### 界面设置管理
```javascript
state: {
  menuData: [],           // 菜单数据
  menuMessageData: [],    // 消息菜单数据
  sysFirstMenuPath: '',   // 系统首页路径
  collapsed: false,       // 侧边栏折叠状态
},

mutations: {
  // 设置菜单数据
  setMenuData(state, routerData = []) {
    const newRouter = [...constantRoutes].concat(routerData).filter((item) => {
      return !item.hidden;
    });
    const menuData = formatMenuData(newRouter);
    state.menuData = menuData;
    state.sysFirstMenuPath = getFirstSiteMenuPath(
      formatMenuData(
        routerData.filter((item) => {
          return !item.hidden;
        })
      )
    );
  },
  
  // 设置侧边栏折叠状态
  setSideMenuCollapsed(state, collapsed) {
    state.collapsed = collapsed;
  },
}
```

## 组件中使用状态管理

### 1. Options API中使用

#### 使用mapState
```javascript
import { mapState } from 'vuex';

export default {
  computed: {
    // 映射单个状态
    ...mapState({
      username: (state) => state.base.username,
      merchant: (state) => state.base.merchant || {},
      permissions: (state) => state.base.permissions,
    }),
    
    // 映射模块状态
    ...mapState('setting', ['collapsed', 'menuData']),
  },
}
```

#### 使用mapGetters
```javascript
import { mapGetters } from 'vuex';

export default {
  computed: {
    ...mapGetters('base', ['permissions']),
  },
}
```

#### 使用mapMutations
```javascript
import { mapMutations } from 'vuex';

export default {
  methods: {
    ...mapMutations('base', ['SET_TOKEN', 'RESET_STATE']),
    
    // 调用mutation
    handleLogin() {
      this.SET_TOKEN('new-token');
    },
  },
}
```

#### 使用mapActions
```javascript
import { mapActions } from 'vuex';

export default {
  methods: {
    ...mapActions('base', ['Login', 'GetInfo']),
    
    // 调用action
    async handleLogin() {
      const [res, err] = await this.Login({
        username: 'admin',
        password: '123456',
      });
      if (!err) {
        await this.GetInfo();
      }
    },
  },
}
```

### 2. Composition API中使用

#### 使用useStore Hook
```javascript
import { computed } from 'vue';
import { useStore } from '@/utils/system/hooks/vueApi';

export default {
  setup() {
    const store = useStore();
    
    // 获取状态
    const username = computed(() => store.state.base.username);
    const merchant = computed(() => store.state.base.merchant);
    
    // 调用mutations
    const setToken = (token) => {
      store.commit('base/SET_TOKEN', token);
    };
    
    // 调用actions
    const login = async (credentials) => {
      return await store.dispatch('base/Login', credentials);
    };
    
    return {
      username,
      merchant,
      setToken,
      login,
    };
  },
}
```

#### 字典数据使用
```javascript
import { ref, computed } from 'vue';
import { useStore } from '@/utils/system/hooks/vueApi';
import { getDict } from '@/api/system/dict';

export default function useDict(...args) {
  const res = ref({});
  const store = useStore();
  const dictStore = computed(() => store.state.dict);
  
  const changeData = () => {
    args.forEach((dictType) => {
      res.value[dictType] = ref([]);
      const dicts = getDictFromStore(dictStore.value, dictType);
      if (dicts) {
        res.value[dictType] = dicts;
      } else {
        getDict(dictType).then(([resp]) => {
          res.value[dictType].value = resp?.data?.map((p) => ({
            label: p.dictLabel,
            value: p.dictValue,
            elTagType: p.listClass,
            elTagClass: p.cssClass,
          }));
          store.dispatch('dict/setDict', {
            key: dictType,
            value: res.value[dictType],
          });
        });
      }
    });
  };
  
  return { ...toRefs(res.value), changeData };
}
```

## 数据流向

### 1. 用户登录流程
```
用户输入 → Login Action → API请求 → SET_TOKEN Mutation → 更新State → 组件响应
```

### 2. 权限验证流程
```
路由守卫 → GetInfo Action → API请求 → SET_BASE_INFO Mutation → 更新权限State → 组件权限控制
```

### 3. 字典数据流程
```
组件需要字典 → getDict Action → 检查缓存 → API请求 → SET_DICT Mutation → 更新字典State → 组件使用
```

## 最佳实践

### 1. 状态设计原则
- **单一数据源**: 所有状态集中在Store中管理
- **状态只读**: 只能通过Mutation修改状态
- **纯函数**: Mutation必须是同步的纯函数
- **异步操作**: 在Action中处理异步逻辑

### 2. 模块化管理
- **按功能分模块**: base、dict、setting等功能模块
- **命名空间**: 使用namespaced避免命名冲突
- **模块职责单一**: 每个模块只负责特定功能

### 3. 组件使用规范
- **计算属性**: 使用computed获取状态，保证响应式
- **辅助函数**: 使用mapState、mapGetters等简化代码
- **避免直接修改**: 不要直接修改state，必须通过mutation

### 4. 性能优化
- **按需引入**: 只引入需要的状态和方法
- **缓存机制**: 字典数据等使用缓存避免重复请求
- **状态持久化**: 重要状态（如用户信息）持久化到localStorage

## 注意事项

1. **命名规范**: 
   - State使用小驼峰命名
   - Mutation使用大写下划线命名
   - Action使用小驼峰命名

2. **错误处理**: 
   - Action中统一处理API错误
   - 使用try-catch包装异步操作

3. **类型安全**: 
   - 使用TypeScript时定义State接口
   - 确保Mutation参数类型正确

4. **调试工具**: 
   - 开发环境启用Vue DevTools
   - 使用Vuex的时间旅行调试功能
