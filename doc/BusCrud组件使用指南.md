# BusCrud 组件使用指南

## 概述

BusCrud 是项目中的核心业务组件，来自`@bangdao/buse-components`组件库。它集成了数据表格、筛选器、分页、弹窗表单等功能，提供完整的 CRUD（增删改查）操作界面，是构建管理后台页面的重要工具。

## 组件引入

### 全局注册

```javascript
// main.js
import { BuseCrud } from '@bangdao/buse-components';
Vue.use(BuseCrud);
```

### 组件使用

```vue
<template>
  <BuseCrud
    ref="crud"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @loadData="loadData"
    @handleSubmit="handleSubmit"
  >
  </BuseCrud>
</template>
```

## 完整属性列表

### Props 属性

| 属性名        | 类型    | 默认值 | 说明         |
| ------------- | ------- | ------ | ------------ |
| title         | String  | ''     | 组件标题     |
| loading       | Boolean | false  | 加载状态     |
| filterOptions | Object  | {}     | 筛选器配置   |
| tablePage     | Object  | {}     | 分页配置     |
| tableColumn   | Array   | []     | 表格列配置   |
| tableData     | Array   | []     | 表格数据     |
| tableProps    | Object  | {}     | 表格额外属性 |
| modalConfig   | Object  | {}     | 弹窗配置     |

### Events 事件

| 事件名       | 参数               | 说明             |
| ------------ | ------------------ | ---------------- |
| loadData     | (refresh: Boolean) | 加载数据事件     |
| handleSubmit | (params: Object)   | 筛选提交事件     |
| handleReset  | ()                 | 重置筛选事件     |
| handleCreate | ()                 | 新增按钮点击事件 |
| rowEdit      | (row: Object)      | 编辑行数据事件   |
| rowDel       | (row: Object)      | 删除行数据事件   |
| modalSubmit  | (formData: Object) | 弹窗表单提交事件 |
| modalConfirm | (formData: Object) | 弹窗确认事件     |
| modalCancel  | ()                 | 弹窗取消事件     |

### Slots 插槽

| 插槽名        | 作用域参数       | 说明                 |
| ------------- | ---------------- | -------------------- |
| defaultHeader | -                | 默认头部区域         |
| menu          | { row }          | 表格操作列自定义内容 |
| [fieldName]   | { item, params } | 筛选器字段自定义内容 |
| [slotName]    | { item, params } | 表单字段自定义内容   |

## 配置详解

### 1. filterOptions 筛选器配置

```javascript
const filterOptions = {
  // 栅格布局配置
  gridCol: { span: 8 },

  // 表单布局配置
  formCol: {
    labelCol: 4,
    wrapperCol: 16,
  },

  // 筛选项配置
  config: [
    {
      field: 'name', // 字段名
      title: '名称', // 显示标题
      element: 'a-input', // 组件类型，默认为a-input
      props: {
        // 组件属性
        placeholder: '请输入名称',
      },
    },
    {
      field: 'status',
      title: '状态',
      element: 'a-select',
      props: {
        options: [
          { value: '1', label: '启用' },
          { value: '0', label: '禁用' },
        ],
      },
    },
    {
      field: 'createTime',
      title: '创建时间',
      element: 'a-range-picker',
      props: {
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      field: 'customField',
      title: '自定义字段',
      element: 'slot', // 使用插槽
      slotName: 'customSlot', // 插槽名称
    },
  ],

  // 筛选参数
  params: {
    name: '',
    status: '',
    createTime: [],
    customField: '',
  },
};
```

### 2. tableColumn 表格列配置

```javascript
const tableColumn = [
  {
    field: 'id', // 数据字段名
    title: 'ID', // 列标题
    width: 80, // 固定宽度
    minWidth: 100, // 最小宽度
    maxWidth: 200, // 最大宽度
  },
  {
    field: 'name',
    title: '名称',
    minWidth: 120,
    // 自定义格式化
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue.toUpperCase() : '--';
    },
  },
  {
    field: 'status',
    title: '状态',
    width: 100,
    // 使用插槽自定义渲染
    slots: { default: 'status' },
  },
  {
    field: 'createTime',
    title: '创建时间',
    minWidth: 180,
    formatter: ({ cellValue }) => {
      return cellValue ? moment(cellValue).format('YYYY-MM-DD HH:mm:ss') : '--';
    },
  },
];
```

### 3. tablePage 分页配置

```javascript
const tablePage = {
  total: 0, // 总条数
  currentPage: 1, // 当前页码
  pageSize: 10, // 每页条数
};
```

### 4. modalConfig 弹窗配置

```javascript
const modalConfig = {
  // 基础配置
  title: '编辑信息', // 弹窗标题
  width: 800, // 弹窗宽度

  // 操作按钮配置
  menu: true, // 是否显示操作列
  menuWidth: 120, // 操作列宽度
  menuTitle: '操作', // 操作列标题
  addBtn: true, // 显示新增按钮
  editBtn: true, // 显示编辑按钮
  delBtn: true, // 显示删除按钮
  viewBtn: false, // 显示查看按钮

  // 表单配置
  formConfig: [
    {
      field: 'name', // 字段名
      title: '名称', // 标题
      element: 'a-input', // 组件类型
      props: {
        // 组件属性
        placeholder: '请输入名称',
      },
      rules: [
        // 验证规则
        { required: true, message: '请输入名称' },
      ],
    },
    {
      field: 'description',
      title: '描述',
      element: 'a-textarea',
      props: {
        rows: 4,
        placeholder: '请输入描述',
      },
    },
    {
      field: 'status',
      title: '状态',
      element: 'a-select',
      props: {
        options: [
          { value: '1', label: '启用' },
          { value: '0', label: '禁用' },
        ],
      },
    },
    {
      field: 'customField',
      title: '自定义字段',
      element: 'slot', // 使用插槽
      slotName: 'customForm', // 插槽名称
    },
  ],

  // 自定义操作按钮
  customOperationTypes: [
    {
      title: '详情', // 按钮文字
      typeName: 'detail', // 操作类型名
      event: (row) => {
        // 点击事件
        return new Promise((resolve) => {
          // 处理逻辑
          this.$router.push(`/detail/${row.id}`);
          resolve();
        });
      },
      condition: (row) => {
        // 显示条件
        return row.status === '1';
      },
    },
    {
      title: '审核',
      typeName: 'audit',
      event: (row) => {
        return new Promise((resolve) => {
          this.handleAudit(row);
          resolve();
        });
      },
      condition: (row) => {
        return row.status === '0' && this.hasPermission('audit');
      },
    },
  ],
};
```

## 使用示例

### 1. 基础列表页面

```vue
<template>
  <div>
    <h2>用户管理</h2>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :modalConfig="modalConfig"
      @loadData="loadData"
      @handleSubmit="handleSubmit"
      @handleCreate="handleCreate"
      @rowEdit="handleEdit"
      @rowDel="handleDelete"
      @modalSubmit="handleModalSubmit"
    >
      <!-- 自定义头部按钮 -->
      <template #defaultHeader>
        <a-button @click="handleExport" type="primary">导出</a-button>
      </template>

      <!-- 状态列自定义渲染 -->
      <template #status="{ row }">
        <a-tag :color="row.status === '1' ? 'green' : 'red'">
          {{ row.status === '1' ? '启用' : '禁用' }}
        </a-tag>
      </template>

      <!-- 操作列自定义内容 -->
      <template #menu="{ row }">
        <a-switch :checked="row.enabled === '1'" @change="handleToggle(row)" />
      </template>
    </BuseCrud>
  </div>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      tableData: [],
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
    };
  },

  computed: {
    // 筛选器配置
    filterOptions() {
      return {
        config: [
          {
            field: 'username',
            title: '用户名',
            props: { placeholder: '请输入用户名' },
          },
          {
            field: 'status',
            title: '状态',
            element: 'a-select',
            props: {
              options: [
                { value: '', label: '全部' },
                { value: '1', label: '启用' },
                { value: '0', label: '禁用' },
              ],
            },
          },
        ],
        params: {
          username: '',
          status: '',
        },
      };
    },

    // 表格列配置
    tableColumn() {
      return [
        { field: 'id', title: 'ID', width: 80 },
        { field: 'username', title: '用户名', minWidth: 120 },
        { field: 'nickname', title: '昵称', minWidth: 120 },
        {
          field: 'status',
          title: '状态',
          width: 100,
          slots: { default: 'status' },
        },
        {
          field: 'createTime',
          title: '创建时间',
          minWidth: 180,
          formatter: ({ cellValue }) => {
            return this.$moment(cellValue).format('YYYY-MM-DD HH:mm:ss');
          },
        },
      ];
    },

    // 弹窗配置
    modalConfig() {
      return {
        addBtn: true,
        editBtn: true,
        delBtn: true,
        formConfig: [
          {
            field: 'username',
            title: '用户名',
            rules: [{ required: true, message: '请输入用户名' }],
          },
          {
            field: 'nickname',
            title: '昵称',
            rules: [{ required: true, message: '请输入昵称' }],
          },
          {
            field: 'status',
            title: '状态',
            element: 'a-select',
            props: {
              options: [
                { value: '1', label: '启用' },
                { value: '0', label: '禁用' },
              ],
            },
          },
        ],
      };
    },
  },

  mounted() {
    this.loadData();
  },

  methods: {
    // 加载数据
    async loadData(refresh = false) {
      if (refresh) {
        this.tablePage.currentPage = 1;
      }

      this.loading = true;
      const params = {
        ...this.filterOptions.params,
        page: this.tablePage.currentPage,
        size: this.tablePage.pageSize,
      };

      const [res, err] = await getUserList(params);
      this.loading = false;

      if (err) return;

      this.tableData = res.data || [];
      this.tablePage.total = res.total || 0;
    },

    // 筛选提交
    handleSubmit() {
      this.loadData(true);
    },

    // 新增
    handleCreate() {
      // BuseCrud会自动打开新增弹窗
    },

    // 编辑
    handleEdit(row) {
      // BuseCrud会自动打开编辑弹窗并填充数据
    },

    // 删除
    async handleDelete(row) {
      const [res, err] = await deleteUser(row.id);
      if (err) return;

      this.$message.success('删除成功');
      this.loadData();
    },

    // 弹窗提交
    async handleModalSubmit(formData) {
      const isEdit = !!formData.id;
      const apiMethod = isEdit ? updateUser : createUser;

      const [res, err] = await apiMethod(formData);
      if (err) return;

      this.$message.success(isEdit ? '更新成功' : '创建成功');
      this.loadData();
    },

    // 导出
    async handleExport() {
      const params = this.filterOptions.params;
      const [res, err] = await exportUserList(params);
      if (err) return;

      // 处理文件下载
      this.downloadFile(res, 'users.xlsx');
    },

    // 切换状态
    async handleToggle(row) {
      const newStatus = row.enabled === '1' ? '0' : '1';
      const [res, err] = await updateUserStatus(row.id, newStatus);
      if (err) return;

      row.enabled = newStatus;
      this.$message.success('状态更新成功');
    },
  },
};
</script>
```

### 2. 带自定义筛选器的页面

```vue
<template>
  <BuseCrud
    ref="crud"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @loadData="loadData"
    @handleSubmit="handleSubmit"
  >
    <!-- 自定义时间筛选 -->
    <template #dateRange>
      <a-row>
        <a-col :span="6">
          <a-space>
            <a-button @click="setDateRange('week')">本周</a-button>
            <a-button @click="setDateRange('month')">本月</a-button>
          </a-space>
        </a-col>
        <a-col :span="18">
          <a-range-picker v-model="filterOptions.params.dateRange" @change="handleDateChange" />
        </a-col>
      </a-row>
    </template>
  </BuseCrud>
</template>

<script>
export default {
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'keyword',
            title: '关键词',
          },
          {
            field: 'dateRange',
            title: '时间范围',
            element: 'slot',
            slotName: 'dateRange',
            itemProps: {
              labelCol: { span: 4 },
              wrapperCol: { span: 20 },
            },
          },
        ],
        params: {
          keyword: '',
          dateRange: [],
        },
      };
    },
  },

  methods: {
    setDateRange(type) {
      const now = this.$moment();
      let start, end;

      if (type === 'week') {
        start = now.startOf('week');
        end = now.endOf('week');
      } else if (type === 'month') {
        start = now.startOf('month');
        end = now.endOf('month');
      }

      this.filterOptions.params.dateRange = [start, end];
    },

    handleDateChange(dates) {
      // 处理日期变化
    },
  },
};
</script>
```

### 3. 带自定义操作的复杂页面

```vue
<template>
  <BuseCrud
    ref="crud"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @loadData="loadData"
    @modalConfirm="handleModalConfirm"
  >
    <!-- 自定义表单字段 -->
    <template #customForm="{ item, params }">
      <a-upload v-model="params.fileList" :before-upload="beforeUpload" list-type="picture-card">
        <div>
          <a-icon type="plus" />
          <div>上传图片</div>
        </div>
      </a-upload>
    </template>
  </BuseCrud>
</template>

<script>
export default {
  computed: {
    modalConfig() {
      return {
        customOperationTypes: [
          {
            title: '审核',
            typeName: 'audit',
            event: (row) => {
              return new Promise((resolve) => {
                this.handleAudit(row);
                resolve();
              });
            },
            condition: (row) => {
              return row.status === 'pending' && this.hasPermission('audit');
            },
          },
          {
            title: '发布',
            typeName: 'publish',
            event: (row) => {
              return new Promise((resolve) => {
                this.handlePublish(row);
                resolve();
              });
            },
            condition: (row) => {
              return row.status === 'approved';
            },
          },
        ],
        formConfig: [
          {
            field: 'title',
            title: '标题',
            rules: [{ required: true, message: '请输入标题' }],
          },
          {
            field: 'content',
            title: '内容',
            element: 'a-textarea',
            props: { rows: 6 },
          },
          {
            field: 'images',
            title: '图片',
            element: 'slot',
            slotName: 'customForm',
          },
        ],
      };
    },
  },

  methods: {
    async handleAudit(row) {
      const [res, err] = await auditItem(row.id, { status: 'approved' });
      if (err) return;

      this.$message.success('审核成功');
      this.loadData();
    },

    async handlePublish(row) {
      const [res, err] = await publishItem(row.id);
      if (err) return;

      this.$message.success('发布成功');
      this.loadData();
    },

    beforeUpload(file) {
      // 文件上传前处理
      return false; // 阻止自动上传
    },
  },
};
</script>
```

## 不同业务场景的最佳实践

### 1. 工单管理场景

```javascript
// 工单列表配置
const workOrderConfig = {
  filterOptions: {
    config: [
      {
        field: 'orderId',
        title: '工单号',
        props: { placeholder: '请输入工单号' },
      },
      {
        field: 'orderStatus',
        title: '工单状态',
        element: 'a-select',
        props: {
          options: [
            { value: 'pending', label: '待处理' },
            { value: 'processing', label: '处理中' },
            { value: 'completed', label: '已完成' },
            { value: 'closed', label: '已关闭' },
          ],
          mode: 'multiple', // 支持多选
        },
      },
      {
        field: 'priority',
        title: '优先级',
        element: 'a-select',
        props: {
          options: [
            { value: 'low', label: '低' },
            { value: 'medium', label: '中' },
            { value: 'high', label: '高' },
            { value: 'urgent', label: '紧急' },
          ],
        },
      },
    ],
  },

  tableColumn: [
    { field: 'orderId', title: '工单号', width: 120 },
    { field: 'title', title: '标题', minWidth: 200 },
    {
      field: 'priority',
      title: '优先级',
      width: 100,
      slots: { default: 'priority' },
    },
    {
      field: 'status',
      title: '状态',
      width: 100,
      slots: { default: 'status' },
    },
    { field: 'assignee', title: '处理人', width: 120 },
    { field: 'createTime', title: '创建时间', minWidth: 180 },
  ],

  modalConfig: {
    customOperationTypes: [
      {
        title: '详情',
        typeName: 'detail',
        event: (row) => {
          return new Promise((resolve) => {
            this.$router.push(`/workorder/detail/${row.id}`);
            resolve();
          });
        },
      },
      {
        title: '分配',
        typeName: 'assign',
        event: (row) => {
          return new Promise((resolve) => {
            this.showAssignModal(row);
            resolve();
          });
        },
        condition: (row) => {
          return row.status === 'pending';
        },
      },
      {
        title: '关闭',
        typeName: 'close',
        event: (row) => {
          return new Promise((resolve) => {
            this.closeWorkOrder(row);
            resolve();
          });
        },
        condition: (row) => {
          return ['completed', 'processing'].includes(row.status);
        },
      },
    ],
  },
};
```

### 2. 用户管理场景

```javascript
// 用户管理配置
const userManageConfig = {
  filterOptions: {
    config: [
      {
        field: 'username',
        title: '用户名',
      },
      {
        field: 'department',
        title: '部门',
        element: 'a-tree-select',
        props: {
          treeData: [], // 部门树形数据
          showSearch: true,
          treeNodeFilterProp: 'title',
        },
      },
      {
        field: 'role',
        title: '角色',
        element: 'a-select',
        props: {
          options: [], // 角色选项
          mode: 'multiple',
        },
      },
      {
        field: 'status',
        title: '状态',
        element: 'a-select',
        props: {
          options: [
            { value: 'active', label: '激活' },
            { value: 'inactive', label: '禁用' },
            { value: 'locked', label: '锁定' },
          ],
        },
      },
    ],
  },

  modalConfig: {
    formConfig: [
      {
        field: 'username',
        title: '用户名',
        rules: [
          { required: true, message: '请输入用户名' },
          { min: 3, max: 20, message: '用户名长度为3-20个字符' },
        ],
      },
      {
        field: 'email',
        title: '邮箱',
        rules: [
          { required: true, message: '请输入邮箱' },
          { type: 'email', message: '请输入正确的邮箱格式' },
        ],
      },
      {
        field: 'phone',
        title: '手机号',
        rules: [
          { required: true, message: '请输入手机号' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
        ],
      },
      {
        field: 'roles',
        title: '角色',
        element: 'a-select',
        props: {
          options: [], // 角色选项
          mode: 'multiple',
        },
        rules: [{ required: true, message: '请选择角色' }],
      },
    ],

    customOperationTypes: [
      {
        title: '重置密码',
        typeName: 'resetPassword',
        event: (row) => {
          return new Promise((resolve) => {
            this.resetUserPassword(row);
            resolve();
          });
        },
      },
      {
        title: '权限设置',
        typeName: 'permission',
        event: (row) => {
          return new Promise((resolve) => {
            this.showPermissionModal(row);
            resolve();
          });
        },
      },
    ],
  },
};
```

### 3. 短信管理场景

```javascript
// 短信记录配置
const smsRecordConfig = {
  filterOptions: {
    config: [
      {
        field: 'phone',
        title: '手机号',
        props: { placeholder: '请输入手机号' },
      },
      {
        field: 'sendStatus',
        title: '发送状态',
        element: 'a-select',
        props: {
          options: [
            { value: 0, label: '待发送' },
            { value: 1, label: '提交成功' },
            { value: 2, label: '发送成功' },
            { value: 3, label: '发送失败' },
          ],
        },
      },
      {
        field: 'businessType',
        title: '业务类型',
        element: 'a-select',
        props: {
          options: [
            { value: 'verify', label: '验证码' },
            { value: 'notice', label: '通知' },
            { value: 'marketing', label: '营销' },
          ],
        },
      },
      {
        field: 'sendTime',
        title: '发送时间',
        element: 'a-range-picker',
        props: {
          valueFormat: 'YYYY-MM-DD',
          showTime: true,
        },
      },
    ],
  },

  tableColumn: [
    { field: 'phone', title: '手机号', width: 120 },
    { field: 'content', title: '短信内容', minWidth: 200 },
    {
      field: 'sendStatus',
      title: '发送状态',
      width: 100,
      slots: { default: 'sendStatus' },
    },
    { field: 'businessType', title: '业务类型', width: 100 },
    { field: 'sendTime', title: '发送时间', minWidth: 180 },
    { field: 'failReason', title: '失败原因', minWidth: 150 },
  ],

  modalConfig: {
    menu: false, // 不显示操作列
    addBtn: false, // 不显示新增按钮
    editBtn: false, // 不显示编辑按钮
    delBtn: false, // 不显示删除按钮

    customOperationTypes: [
      {
        title: '重发',
        typeName: 'resend',
        event: (row) => {
          return new Promise((resolve) => {
            this.resendSms(row);
            resolve();
          });
        },
        condition: (row) => {
          return row.sendStatus === 3; // 只有发送失败的才能重发
        },
      },
      {
        title: '查看详情',
        typeName: 'detail',
        event: (row) => {
          return new Promise((resolve) => {
            this.showSmsDetail(row);
            resolve();
          });
        },
      },
    ],
  },
};
```

## 常见问题和解决方案

### 1. 表格数据不更新

**问题**: 修改 tableData 后表格不刷新

**解决方案**:

```javascript
// 错误做法
this.tableData.push(newItem);

// 正确做法
this.tableData = [...this.tableData, newItem];
// 或者
this.$set(this.tableData, index, newItem);
```

### 2. 筛选器参数不生效

**问题**: 修改 filterOptions.params 后筛选不生效

**解决方案**:

```javascript
// 确保params是响应式的
this.$set(this.filterOptions.params, 'newField', 'newValue');

// 或者重新赋值整个params对象
this.filterOptions.params = {
  ...this.filterOptions.params,
  newField: 'newValue',
};
```

### 3. 自定义操作按钮权限控制

**问题**: 需要根据用户权限显示不同的操作按钮

**解决方案**:

```javascript
customOperationTypes: [
  {
    title: '删除',
    typeName: 'delete',
    event: (row) => this.handleDelete(row),
    condition: (row) => {
      // 根据用户权限和数据状态判断
      return this.hasPermission('delete') && row.status !== 'locked';
    },
  },
];
```

### 4. 表单验证不通过

**问题**: 弹窗表单提交时验证失败

**解决方案**:

```javascript
// 在formConfig中正确配置验证规则
formConfig: [
  {
    field: 'email',
    title: '邮箱',
    rules: [
      { required: true, message: '请输入邮箱' },
      { type: 'email', message: '邮箱格式不正确' },
    ],
  },
]

// 在modalSubmit事件中处理验证
async handleModalSubmit(formData) {
  // BuseCrud会自动进行表单验证
  // 只有验证通过才会触发此事件
  const [res, err] = await saveData(formData);
  if (err) return;

  this.$message.success('保存成功');
  this.loadData();
}
```

### 5. 分页数据加载

**问题**: 分页切换时数据加载逻辑

**解决方案**:

```javascript
// 在loadData方法中处理分页
async loadData(refresh = false) {
  if (refresh) {
    this.tablePage.currentPage = 1; // 重置到第一页
  }

  const params = {
    ...this.filterOptions.params,
    page: this.tablePage.currentPage,
    size: this.tablePage.pageSize,
  };

  const [res, err] = await getDataList(params);
  if (err) return;

  this.tableData = res.data || [];
  this.tablePage.total = res.total || 0;
}

// BuseCrud会自动处理分页切换事件
// 当用户点击分页时会自动调用loadData方法
```

## 与其他组件的集成使用

### 1. 与路由集成

```javascript
// 在路由守卫中保存筛选条件
beforeRouteLeave(to, from, next) {
  // 保存当前筛选条件到sessionStorage
  sessionStorage.setItem('listParams', JSON.stringify(this.filterOptions.params));
  next();
},

beforeRouteEnter(to, from, next) {
  next(vm => {
    // 恢复筛选条件
    const savedParams = sessionStorage.getItem('listParams');
    if (savedParams) {
      vm.filterOptions.params = JSON.parse(savedParams);
    }
    vm.loadData();
  });
},
```

### 2. 与权限系统集成

```javascript
// 使用自定义指令控制按钮显示
modalConfig: {
  addBtn: this.$hasPermi(['system:user:add']),
  editBtn: this.$hasPermi(['system:user:edit']),
  delBtn: this.$hasPermi(['system:user:delete']),

  customOperationTypes: [
    {
      title: '重置密码',
      typeName: 'resetPassword',
      event: (row) => this.resetPassword(row),
      condition: (row) => {
        return this.$hasPermi(['system:user:resetPassword']);
      },
    },
  ],
}
```

### 3. 与消息提示集成

```javascript
// 统一的成功/错误处理
async handleOperation(apiMethod, successMsg = '操作成功') {
  const [res, err] = await apiMethod();
  if (err) {
    this.$message.error(err.message || '操作失败');
    return false;
  }

  this.$message.success(successMsg);
  this.loadData();
  return true;
}

// 使用示例
async handleDelete(row) {
  const confirmed = await this.$confirm('确定要删除这条记录吗？');
  if (!confirmed) return;

  await this.handleOperation(
    () => deleteUser(row.id),
    '删除成功'
  );
}
```

## 性能优化建议

### 1. 大数据量优化

```javascript
// 使用虚拟滚动
tableProps: {
  height: 400,
  scrollY: { enabled: true, gt: 100 }, // 超过100条数据启用虚拟滚动
}

// 分页大小控制
tablePage: {
  total: 0,
  currentPage: 1,
  pageSize: 20, // 适中的分页大小
  pageSizes: [10, 20, 50, 100], // 提供多种分页选项
}
```

### 2. 筛选器优化

```javascript
// 使用防抖优化搜索
import { debounce } from 'lodash';

created() {
  // 创建防抖搜索方法
  this.debouncedSearch = debounce(this.handleSubmit, 300);
},

// 在输入框change事件中使用
handleInputChange() {
  this.debouncedSearch();
}
```

### 3. 组件缓存

```vue
<!-- 使用keep-alive缓存组件状态 -->
<keep-alive>
  <BuseCrud
    v-if="showTable"
    ref="crud"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @loadData="loadData"
  />
</keep-alive>
```

## 注意事项

1. **响应式数据**: 确保所有配置对象都是响应式的，避免数据更新后界面不刷新
2. **事件处理**: 自定义操作的 event 函数必须返回 Promise，确保异步操作正确处理
3. **权限控制**: 合理使用 condition 函数控制操作按钮的显示和隐藏
4. **表单验证**: 在 formConfig 中正确配置验证规则，确保数据完整性
5. **性能考虑**: 对于大数据量场景，合理配置分页和虚拟滚动
6. **错误处理**: 统一处理 API 调用的错误情况，提供友好的用户提示

通过以上详细的使用指南，开发者可以快速掌握 BusCrud 组件的使用方法，并根据不同的业务场景进行灵活配置和扩展。
