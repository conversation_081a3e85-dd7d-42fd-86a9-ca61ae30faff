# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@achrinza/node-ipc@^9.2.5":
  "integrity" "sha512-ULSIYPy4ZPM301dfCxRz0l2GJjOwIo/PqmWonIu1bLml7UmnVQmH+juJcoyXp6E8gIRRNAjGYftJnNQlfy4vPg=="
  "resolved" "https://registry.npmmirror.com/@achrinza/node-ipc/-/node-ipc-9.2.6.tgz"
  "version" "9.2.6"
  dependencies:
    "@node-ipc/js-queue" "2.0.3"
    "event-pubsub" "4.3.0"
    "js-message" "1.0.7"

"@ampproject/remapping@^2.2.0":
  "integrity" "sha512-qRmjj8nj9qmLTQXXmaR1cck3UXSRMPrbsLJAasZpF+t3riI71BXed5ebIOYwQntykeZuhjsdweEc9BxH5Jc26w=="
  "resolved" "https://registry.npmmirror.com/@ampproject/remapping/-/remapping-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "@jridgewell/gen-mapping" "^0.1.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@ant-design/colors@^3.1.0":
  "integrity" "sha512-YKgNbG2dlzqMhA9NtI3/pbY16m3Yl/EeWBRa+lB1X1YaYxHrxNexiQYCLTWO/uDvAjLFMEDU+zR901waBtMtjQ=="
  "resolved" "https://registry.npmmirror.com/@ant-design/colors/-/colors-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "tinycolor2" "^1.4.1"

"@ant-design/icons-vue@^2.0.0":
  "integrity" "sha512-2c0QQE5hL4N48k5NkPG5sdpMl9YnvyNhf0U7YkdZYDlLnspoRU7vIA0UK9eHBs6OpFLcJB6o8eJrIl2ajBskPg=="
  "resolved" "https://registry.npmmirror.com/@ant-design/icons-vue/-/icons-vue-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "@ant-design/colors" "^3.1.0"
    "babel-runtime" "^6.26.0"

"@ant-design/icons@^2.0.0", "@ant-design/icons@^2.1.1":
  "integrity" "sha512-jCH+k2Vjlno4YWl6g535nHR09PwCEmTBKAG6VqF+rhkrSPRLfgpU2maagwbZPLjaHuU5Jd1DFQ2KJpQuI6uG8w=="
  "resolved" "https://registry.npmmirror.com/@ant-design/icons/-/icons-2.1.1.tgz"
  "version" "2.1.1"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.18.6":
  "integrity" "sha512-TDCmlK5eOvH+eH7cdAFlNXeVJqWIQ7gW9tY1GJIpUtFb6CmjVyq2VM3u71bOyR8CRihcCgMUYoDNyLXao3+70Q=="
  "resolved" "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/highlight" "^7.18.6"

"@babel/code-frame@7.12.11":
  "integrity" "sha512-Zt1yodBx1UcyiePMSkWnU4hPqhwq7hGi2nFL1LeA3EUl+q2LQx16MISgJ0+z7dnmgvP9QtIleuETGOiOH1RcIw=="
  "resolved" "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.12.11.tgz"
  "version" "7.12.11"
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/compat-data@^7.17.7", "@babel/compat-data@^7.20.1", "@babel/compat-data@^7.20.5":
  "integrity" "sha512-gMuZsmsgxk/ENC3O/fRw5QY8A9/uxQbbCEypnLIiYYc/qVJtEV7ouxC3EllIIwNzMqAQee5tanFabWsUOutS7g=="
  "resolved" "https://registry.npmmirror.com/@babel/compat-data/-/compat-data-7.21.0.tgz"
  "version" "7.21.0"

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.12.0", "@babel/core@^7.12.16", "@babel/core@^7.13.0", "@babel/core@^7.4.0-0", "@babel/core@>=7.11.0", "@babel/core@7.21.3":
  "integrity" "sha512-qIJONzoa/qiHghnm0l1n4i/6IIziDpzqc36FBs4pzMhDUraHqponwJLiAKm1hGLP3OSB/TVNz6rMwVGpwxxySw=="
  "resolved" "https://registry.npmmirror.com/@babel/core/-/core-7.21.3.tgz"
  "version" "7.21.3"
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.18.6"
    "@babel/generator" "^7.21.3"
    "@babel/helper-compilation-targets" "^7.20.7"
    "@babel/helper-module-transforms" "^7.21.2"
    "@babel/helpers" "^7.21.0"
    "@babel/parser" "^7.21.3"
    "@babel/template" "^7.20.7"
    "@babel/traverse" "^7.21.3"
    "@babel/types" "^7.21.3"
    "convert-source-map" "^1.7.0"
    "debug" "^4.1.0"
    "gensync" "^1.0.0-beta.2"
    "json5" "^2.2.2"
    "semver" "^6.3.0"

"@babel/eslint-parser@7.21.3":
  "integrity" "sha512-kfhmPimwo6k4P8zxNs8+T7yR44q1LdpsZdE1NkCsVlfiuTPRfnGgjaF8Qgug9q9Pou17u6wneYF0lDCZJATMFg=="
  "resolved" "https://registry.npmmirror.com/@babel/eslint-parser/-/eslint-parser-7.21.3.tgz"
  "version" "7.21.3"
  dependencies:
    "@nicolo-ribaudo/eslint-scope-5-internals" "5.1.1-v1"
    "eslint-visitor-keys" "^2.1.0"
    "semver" "^6.3.0"

"@babel/generator@^7.21.3":
  "integrity" "sha512-QS3iR1GYC/YGUnW7IdggFeN5c1poPUurnGttOV/bZgPGV+izC/D8HnD6DLwod0fsatNyVn1G3EVWMYIF0nHbeA=="
  "resolved" "https://registry.npmmirror.com/@babel/generator/-/generator-7.21.3.tgz"
  "version" "7.21.3"
  dependencies:
    "@babel/types" "^7.21.3"
    "@jridgewell/gen-mapping" "^0.3.2"
    "@jridgewell/trace-mapping" "^0.3.17"
    "jsesc" "^2.5.1"

"@babel/helper-annotate-as-pure@^7.18.6":
  "integrity" "sha512-duORpUiYrEpzKIop6iNbjnwKLAKnJ47csTyRACyEmWj0QdUrm5aqNJGHSSEQSUAvNW0ojX0dOmK9dZduvkfeXA=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.18.6":
  "integrity" "sha512-yFQ0YCHoIqarl8BCRwBL8ulYUaZpz3bNsA7oFepAzee+8/+ImtADXNOmO5vJvsPff3qi+hvpkY/NYBTrBQgdNw=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.18.6"
    "@babel/types" "^7.18.9"

"@babel/helper-compilation-targets@^7.12.16", "@babel/helper-compilation-targets@^7.17.7", "@babel/helper-compilation-targets@^7.18.9", "@babel/helper-compilation-targets@^7.20.0", "@babel/helper-compilation-targets@^7.20.7":
  "integrity" "sha512-4tGORmfQcrc+bvrjb5y3dG9Mx1IOZjsHqQVUz7XCNHO+iTmqxWnVg3KRygjGmpRLJGdQSKuvFinbIb0CnZwHAQ=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.20.7.tgz"
  "version" "7.20.7"
  dependencies:
    "@babel/compat-data" "^7.20.5"
    "@babel/helper-validator-option" "^7.18.6"
    "browserslist" "^4.21.3"
    "lru-cache" "^5.1.1"
    "semver" "^6.3.0"

"@babel/helper-create-class-features-plugin@^7.18.6", "@babel/helper-create-class-features-plugin@^7.21.0":
  "integrity" "sha512-Q8wNiMIdwsv5la5SPxNYzzkPnjgC0Sy0i7jLkVOCdllu/xcVNkr3TeZzbHBJrj+XXRqzX5uCyCoV9eu6xUG7KQ=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.21.0"
    "@babel/helper-member-expression-to-functions" "^7.21.0"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/helper-replace-supers" "^7.20.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.20.0"
    "@babel/helper-split-export-declaration" "^7.18.6"

"@babel/helper-create-regexp-features-plugin@^7.18.6", "@babel/helper-create-regexp-features-plugin@^7.20.5":
  "integrity" "sha512-N+LaFW/auRSWdx7SHD/HiARwXQju1vXTW4fKr4u5SgBUTm51OKEjKgj+cs00ggW3kEvNqwErnlwuq7Y3xBe4eg=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "regexpu-core" "^5.3.1"

"@babel/helper-define-polyfill-provider@^0.3.3":
  "integrity" "sha512-z5aQKU4IzbqCC1XH0nAqfsFLMVSo22SBKUc0BxGrLkolTdPTructy0ToNnlO2zA4j9Q/7pjMZf0DSY+DSTYzww=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.3.3.tgz"
  "version" "0.3.3"
  dependencies:
    "@babel/helper-compilation-targets" "^7.17.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "debug" "^4.1.1"
    "lodash.debounce" "^4.0.8"
    "resolve" "^1.14.2"
    "semver" "^6.1.2"

"@babel/helper-environment-visitor@^7.18.9":
  "integrity" "sha512-3r/aACDJ3fhQ/EVgFy0hpj8oHyHpQc+LPtJoY9SzTThAsStm4Ptegq92vqKoE3vD706ZVFWITnMnxucw+S9Ipg=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-environment-visitor/-/helper-environment-visitor-7.18.9.tgz"
  "version" "7.18.9"

"@babel/helper-explode-assignable-expression@^7.18.6":
  "integrity" "sha512-eyAYAsQmB80jNfg4baAtLeWAQHfHFiR483rzFK+BhETlGZaQC9bsfrugfXDCbRHLQbIA7U5NxhhOxN7p/dWIcg=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-function-name@^7.18.9", "@babel/helper-function-name@^7.19.0", "@babel/helper-function-name@^7.21.0":
  "integrity" "sha512-HfK1aMRanKHpxemaY2gqBmL04iAPOPRj7DxtNbiDOrJK+gdwkiNRVpCpUJYbUT+aZyemKN8brqTOxzCaG6ExRg=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-function-name/-/helper-function-name-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/template" "^7.20.7"
    "@babel/types" "^7.21.0"

"@babel/helper-hoist-variables@^7.18.6":
  "integrity" "sha512-UlJQPkFqFULIcyW5sbzgbkxn2FKRgwWiRexcuaR8RNJRy8+LLveqPjwZV/bwrLZCN0eUHD/x8D0heK1ozuoo6Q=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-hoist-variables/-/helper-hoist-variables-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-member-expression-to-functions@^7.20.7", "@babel/helper-member-expression-to-functions@^7.21.0":
  "integrity" "sha512-Muu8cdZwNN6mRRNG6lAYErJ5X3bRevgYR2O8wN0yn7jJSnGDu6eG59RfT29JHxGUovyfrh6Pj0XzmR7drNVL3Q=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/types" "^7.21.0"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.12.13", "@babel/helper-module-imports@^7.18.6":
  "integrity" "sha512-0NFvs3VkuSYbFi1x2Vd6tKrywq+z/cLeYC/RJNFrIX/30Bf5aiGYbtvGXolEktzJH8o5E5KJ3tT+nkxuuZFVlA=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-module-imports/-/helper-module-imports-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-module-transforms@^7.18.6", "@babel/helper-module-transforms@^7.20.11", "@babel/helper-module-transforms@^7.21.2":
  "integrity" "sha512-79yj2AR4U/Oqq/WOV7Lx6hUjau1Zfo4cI+JLAVYeMV5XIlbOhmjEk5ulbTc9fMpmlojzZHkUUxAiK+UKn+hNQQ=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-module-transforms/-/helper-module-transforms-7.21.2.tgz"
  "version" "7.21.2"
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-simple-access" "^7.20.2"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/helper-validator-identifier" "^7.19.1"
    "@babel/template" "^7.20.7"
    "@babel/traverse" "^7.21.2"
    "@babel/types" "^7.21.2"

"@babel/helper-optimise-call-expression@^7.18.6":
  "integrity" "sha512-HP59oD9/fEHQkdcbgFCnbmgH5vIQTJbxh2yf+CdM89/glUNnuzr87Q8GIjGEnOktTROemO0Pe0iPAYbqZuOUiA=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.16.7", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.18.9", "@babel/helper-plugin-utils@^7.19.0", "@babel/helper-plugin-utils@^7.20.2", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  "integrity" "sha512-8RvlJG2mj4huQ4pZ+rU9lqKi9ZKiRmuvGuM2HlWmkmgOhbs6zEAw6IEiJ5cQqGbDzGZOhwuOQNtZMi/ENLjZoQ=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz"
  "version" "7.20.2"

"@babel/helper-remap-async-to-generator@^7.18.9":
  "integrity" "sha512-dI7q50YKd8BAv3VEfgg7PS7yD3Rtbi2J1XMXaalXO0W0164hYLnh8zpjRS0mte9MfVp/tltvr/cfdXPvJr1opA=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-wrap-function" "^7.18.9"
    "@babel/types" "^7.18.9"

"@babel/helper-replace-supers@^7.18.6", "@babel/helper-replace-supers@^7.20.7":
  "integrity" "sha512-vujDMtB6LVfNW13jhlCrp48QNslK6JXi7lQG736HVbHz/mbf4Dc7tIRh1Xf5C0rF7BP8iiSxGMCmY6Ci1ven3A=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-replace-supers/-/helper-replace-supers-7.20.7.tgz"
  "version" "7.20.7"
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-member-expression-to-functions" "^7.20.7"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/template" "^7.20.7"
    "@babel/traverse" "^7.20.7"
    "@babel/types" "^7.20.7"

"@babel/helper-simple-access@^7.20.2":
  "integrity" "sha512-+0woI/WPq59IrqDYbVGfshjT5Dmk/nnbdpcF8SnMhhXObpTq2KNBdLFRFrkVdbDOyUmHBCxzm5FHV1rACIkIbA=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-simple-access/-/helper-simple-access-7.20.2.tgz"
  "version" "7.20.2"
  dependencies:
    "@babel/types" "^7.20.2"

"@babel/helper-skip-transparent-expression-wrappers@^7.20.0":
  "integrity" "sha512-5y1JYeNKfvnT8sZcK9DVRtpTbGiomYIHviSP3OQWmDPU3DeH4a1ZlT/N2lyQ5P8egjcRaT/Y9aNqUxK0WsnIIg=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.20.0.tgz"
  "version" "7.20.0"
  dependencies:
    "@babel/types" "^7.20.0"

"@babel/helper-split-export-declaration@^7.18.6":
  "integrity" "sha512-bde1etTx6ZyTmobl9LLMMQsaizFVZrquTEHOqKeQESMKo4PlObf+8+JA25ZsIpZhT/WEd39+vOdLXAFG/nELpA=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-string-parser@^7.19.4":
  "integrity" "sha512-nHtDoQcuqFmwYNYPz3Rah5ph2p8PFeFCsZk9A/48dPc/rGocJ5J3hAAZ7pb76VWX3fZKu+uEr/FhH5jLx7umrw=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-string-parser/-/helper-string-parser-7.19.4.tgz"
  "version" "7.19.4"

"@babel/helper-validator-identifier@^7.18.6", "@babel/helper-validator-identifier@^7.19.1":
  "integrity" "sha512-awrNfaMtnHUr653GgGEs++LlAvW6w+DcPrOliSMXWCKo597CwL5Acf/wWdNkf/tfEQE3mjkeD1YOVZOUV/od1w=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz"
  "version" "7.19.1"

"@babel/helper-validator-option@^7.18.6":
  "integrity" "sha512-rmL/B8/f0mKS2baE9ZpyTcTavvEuWhTTW8amjzXNvYG4AwBsqTLikfXsEofsJEfKHf+HQVQbFOHy6o+4cnC/fQ=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-validator-option/-/helper-validator-option-7.21.0.tgz"
  "version" "7.21.0"

"@babel/helper-wrap-function@^7.18.9":
  "integrity" "sha512-bYMxIWK5mh+TgXGVqAtnu5Yn1un+v8DDZtqyzKRLUzrh70Eal2O3aZ7aPYiMADO4uKlkzOiRiZ6GX5q3qxvW9Q=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-wrap-function/-/helper-wrap-function-7.20.5.tgz"
  "version" "7.20.5"
  dependencies:
    "@babel/helper-function-name" "^7.19.0"
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.20.5"
    "@babel/types" "^7.20.5"

"@babel/helpers@^7.21.0":
  "integrity" "sha512-XXve0CBtOW0pd7MRzzmoyuSj0e3SEzj8pgyFxnTT1NJZL38BD1MK7yYrm8yefRPIDvNNe14xR4FdbHwpInD4rA=="
  "resolved" "https://registry.npmmirror.com/@babel/helpers/-/helpers-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/template" "^7.20.7"
    "@babel/traverse" "^7.21.0"
    "@babel/types" "^7.21.0"

"@babel/highlight@^7.10.4", "@babel/highlight@^7.18.6":
  "integrity" "sha512-u7stbOuYjaPezCuLj29hNW1v64M2Md2qupEKP1fHc7WdOA3DgLh37suiSrZYY7haUB7iBeQZ9P1uiRF359do3g=="
  "resolved" "https://registry.npmmirror.com/@babel/highlight/-/highlight-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-validator-identifier" "^7.18.6"
    "chalk" "^2.0.0"
    "js-tokens" "^4.0.0"

"@babel/parser@^7.18.4", "@babel/parser@^7.20.7", "@babel/parser@^7.21.3":
  "integrity" "sha512-lobG0d7aOfQRXh8AyklEAgZGvA4FShxo6xQbUrrT/cNBPUdIDojlokwJsQyCC/eKia7ifqM0yP+2DRZ4WKw2RQ=="
  "resolved" "https://registry.npmmirror.com/@babel/parser/-/parser-7.21.3.tgz"
  "version" "7.21.3"

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.18.6":
  "integrity" "sha512-Dgxsyg54Fx1d4Nge8UnvTrED63vrwOdPmyvPzlNN/boaliRP54pm3pGzZD1SJUwrBA+Cs/xdG8kXX6Mn/RfISQ=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.18.9":
  "integrity" "sha512-sbr9+wNE5aXMBBFBICk01tt7sBf2Oc9ikRFEcem/ZORup9IMUdNhW7/wVLEbbtlWOsEubJet46mHAL2C8+2jKQ=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.20.7.tgz"
  "version" "7.20.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.20.0"
    "@babel/plugin-proposal-optional-chaining" "^7.20.7"

"@babel/plugin-proposal-async-generator-functions@^7.20.1":
  "integrity" "sha512-xMbiLsn/8RK7Wq7VeVytytS2L6qE69bXPB10YCmMdDZbKF4okCqY74pI/jJQ/8U0b/F6NrT2+14b8/P9/3AMGA=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.20.7.tgz"
  "version" "7.20.7"
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-remap-async-to-generator" "^7.18.9"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-proposal-class-properties@^7.12.13", "@babel/plugin-proposal-class-properties@^7.18.6":
  "integrity" "sha512-cumfXOF0+nzZrrN8Rf0t7M+tF6sZc7vhQwYQck9q1/5w2OExlD+b4v4RpMJFaV1Z7WcDRgO6FqvxqxGlwo+RHQ=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-class-static-block@^7.18.6":
  "integrity" "sha512-XP5G9MWNUskFuP30IfFSEFB0Z6HzLIUcjYM4bYOPHXl7eiJ9HFv8tWj6TXTN5QODiEhDZAeI4hLok2iHFFV4hw=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-class-static-block/-/plugin-proposal-class-static-block-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.21.0"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"

"@babel/plugin-proposal-decorators@^7.12.13":
  "integrity" "sha512-MfgX49uRrFUTL/HvWtmx3zmpyzMMr4MTj3d527MLlr/4RTT9G/ytFFP7qet2uM2Ve03b+BkpWUpK+lRXnQ+v9w=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.21.0"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-replace-supers" "^7.20.7"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/plugin-syntax-decorators" "^7.21.0"

"@babel/plugin-proposal-dynamic-import@^7.18.6":
  "integrity" "sha512-1auuwmK+Rz13SJj36R+jqFPMJWyKEDd7lLSdOj4oJK0UTgGueSAtkrCvz9ewmgyU/P941Rv2fQwZJN8s6QruXw=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-dynamic-import/-/plugin-proposal-dynamic-import-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"

"@babel/plugin-proposal-export-namespace-from@^7.18.9":
  "integrity" "sha512-k1NtHyOMvlDDFeb9G5PhUXuGj8m/wiwojgQVEhJ/fsVsMCpLyOP4h0uGEjYJKrRI+EVPlb5Jk+Gt9P97lOGwtA=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-export-namespace-from/-/plugin-proposal-export-namespace-from-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-proposal-json-strings@^7.18.6":
  "integrity" "sha512-lr1peyn9kOdbYc0xr0OdHTZ5FMqS6Di+H0Fz2I/JwMzGmzJETNeOFq2pBySw6X/KFL5EWDjlJuMsUGRFb8fQgQ=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-json-strings" "^7.8.3"

"@babel/plugin-proposal-logical-assignment-operators@^7.18.9":
  "integrity" "sha512-y7C7cZgpMIjWlKE5T7eJwp+tnRYM89HmRvWM5EQuB5BoHEONjmQ8lSNmBUwOyy/GFRsohJED51YBF79hE1djug=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-logical-assignment-operators/-/plugin-proposal-logical-assignment-operators-7.20.7.tgz"
  "version" "7.20.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.18.6":
  "integrity" "sha512-wQxQzxYeJqHcfppzBDnm1yAY0jSRkUXR2z8RePZYrKwMKgMlE8+Z6LUno+bd6LvbGh8Gltvy74+9pIYkr+XkKA=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-numeric-separator@^7.18.6":
  "integrity" "sha512-ozlZFogPqoLm8WBr5Z8UckIoE4YQ5KESVcNudyXOR8uqIkliTEgJ3RoketfG6pmzLdeZF0H/wjE9/cCEitBl7Q=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-numeric-separator/-/plugin-proposal-numeric-separator-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-proposal-object-rest-spread@^7.20.2":
  "integrity" "sha512-d2S98yCiLxDVmBmE8UjGcfPvNEUbA1U5q5WxaWFUGRzJSVAZqm5W6MbPct0jxnegUZ0niLeNX+IOzEs7wYg9Dg=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.20.7.tgz"
  "version" "7.20.7"
  dependencies:
    "@babel/compat-data" "^7.20.5"
    "@babel/helper-compilation-targets" "^7.20.7"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.20.7"

"@babel/plugin-proposal-optional-catch-binding@^7.18.6":
  "integrity" "sha512-Q40HEhs9DJQyaZfUjjn6vE8Cv4GmMHCYuMGIWUnlxH6400VGxOuwWsPt4FxXxJkC/5eOzgn0z21M9gMT4MOhbw=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-proposal-optional-chaining@^7.18.9", "@babel/plugin-proposal-optional-chaining@^7.20.7":
  "integrity" "sha512-p4zeefM72gpmEe2fkUr/OnOXpWEf8nAgk7ZYVqqfFiyIG7oFfVZcCrU64hWn5xp4tQ9LkV4bTIa5rD0KANpKNA=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.20.0"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-proposal-private-methods@^7.18.6":
  "integrity" "sha512-nutsvktDItsNn4rpGItSNV2sz1XwS+nfU0Rg8aCx3W3NOKVzdMjJRu0O5OkgDp3ZGICSTbgRpxZoWsxoKRvbeA=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-private-methods/-/plugin-proposal-private-methods-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-private-property-in-object@^7.18.6":
  "integrity" "sha512-ha4zfehbJjc5MmXBlHec1igel5TJXXLDDRbuJ4+XT2TJcyD9/V1919BA8gMvsdHcNMBy4WBUBiRb3nw/EQUtBw=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-create-class-features-plugin" "^7.21.0"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"

"@babel/plugin-proposal-unicode-property-regex@^7.18.6", "@babel/plugin-proposal-unicode-property-regex@^7.4.4":
  "integrity" "sha512-2BShG/d5yoZyXZfVePH91urL5wTG6ASZU9M4o03lKK8u8UW1y08OMttBSOADTcJrnPMpvDXRG3G8fyLh4ovs8w=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-syntax-async-generators@^7.8.4":
  "integrity" "sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
  "version" "7.8.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  "integrity" "sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  "integrity" "sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-decorators@^7.21.0":
  "integrity" "sha512-tIoPpGBR8UuM4++ccWN3gifhVvQu7ZizuR1fklhRJrd5ewgbkUS+0KVFeWWxELtn18NTLoW32XV7zyOgIAiz+w=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-syntax-dynamic-import@^7.8.3":
  "integrity" "sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  "integrity" "sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-import-assertions@^7.20.0":
  "integrity" "sha512-IUh1vakzNoWalR8ch/areW7qFopR2AEw03JlG7BbrDqmQ4X3q9uuipQwSGrUn7oGiemKjtSLDhNtQHzMHr1JdQ=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.20.0.tgz"
  "version" "7.20.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.19.0"

"@babel/plugin-syntax-json-strings@^7.8.3":
  "integrity" "sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.0.0", "@babel/plugin-syntax-jsx@^7.12.13", "@babel/plugin-syntax-jsx@^7.2.0":
  "integrity" "sha512-6mmljtAedFGTWu2p/8WIORGwy+61PLgOMPOdazc7YoJ9ZCWUyFy3A6CpPkRKLKD1ToAesxX8KGEViAiLo9N+7Q=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  "integrity" "sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  "integrity" "sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  "integrity" "sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  "integrity" "sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  "integrity" "sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  "integrity" "sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  "integrity" "sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  "integrity" "sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-arrow-functions@^7.18.6":
  "integrity" "sha512-3poA5E7dzDomxj9WXWwuD6A5F3kc7VXwIJO+E+J8qtDtS+pXPAhrgEyh+9GBwBgPq1Z+bB+/JD60lp5jsN7JPQ=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.20.7.tgz"
  "version" "7.20.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-async-to-generator@^7.18.6":
  "integrity" "sha512-Uo5gwHPT9vgnSXQxqGtpdufUiWp96gk7yiP4Mp5bm1QMkEmLXBO7PAGYbKoJ6DhAwiNkcHFBol/x5zZZkL/t0Q=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.20.7.tgz"
  "version" "7.20.7"
  dependencies:
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-remap-async-to-generator" "^7.18.9"

"@babel/plugin-transform-block-scoped-functions@^7.18.6":
  "integrity" "sha512-ExUcOqpPWnliRcPqves5HJcJOvHvIIWfuS4sroBUenPuMdmW+SMHDakmtS7qOo13sVppmUijqeTv7qqGsvURpQ=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-block-scoping@^7.20.2":
  "integrity" "sha512-Mdrbunoh9SxwFZapeHVrwFmri16+oYotcZysSzhNIVDwIAb1UV+kvnxULSYq9J3/q5MDG+4X6w8QVgD1zhBXNQ=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-classes@^7.20.2":
  "integrity" "sha512-RZhbYTCEUAe6ntPehC4hlslPWosNHDox+vAs4On/mCLRLfoDVHf6hVEd7kuxr1RnHwJmxFfUM3cZiZRmPxJPXQ=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-classes/-/plugin-transform-classes-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-compilation-targets" "^7.20.7"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.21.0"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-replace-supers" "^7.20.7"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "globals" "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.18.9":
  "integrity" "sha512-Lz7MvBK6DTjElHAmfu6bfANzKcxpyNPeYBGEafyA6E5HtRpjpZwU+u7Qrgz/2OR0z+5TvKYbPdphfSaAcZBrYQ=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.20.7.tgz"
  "version" "7.20.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/template" "^7.20.7"

"@babel/plugin-transform-destructuring@^7.20.2":
  "integrity" "sha512-bp6hwMFzuiE4HqYEyoGJ/V2LeIWn+hLVKc4pnj++E5XQptwhtcGmSayM029d/j2X1bPKGTlsyPwAubuU22KhMA=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.21.3.tgz"
  "version" "7.21.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-dotall-regex@^7.18.6", "@babel/plugin-transform-dotall-regex@^7.4.4":
  "integrity" "sha512-6S3jpun1eEbAxq7TdjLotAsl4WpQI9DxfkycRcKrjhQYzU87qpXdknpBg/e+TdcMehqGnLFi7tnFUBR02Vq6wg=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-duplicate-keys@^7.18.9":
  "integrity" "sha512-d2bmXCtZXYc59/0SanQKbiWINadaJXqtvIQIzd4+hNwkWBgyCd5F/2t1kXoUdvPMrxzPvhK6EMQRROxsue+mfw=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-exponentiation-operator@^7.18.6":
  "integrity" "sha512-wzEtc0+2c88FVR34aQmiz56dxEkxr2g8DQb/KfaFa1JYXOFVsbhvAonFN6PwVWj++fKmku8NP80plJ5Et4wqHw=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-for-of@^7.18.8":
  "integrity" "sha512-LlUYlydgDkKpIY7mcBWvyPPmMcOphEyYA27Ef4xpbh1IiDNLr0kZsos2nf92vz3IccvJI25QUwp86Eo5s6HmBQ=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-function-name@^7.18.9":
  "integrity" "sha512-WvIBoRPaJQ5yVHzcnJFor7oS5Ls0PYixlTYE63lCj2RtdQEl15M68FXQlxnG6wdraJIXRdR7KI+hQ7q/9QjrCQ=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-compilation-targets" "^7.18.9"
    "@babel/helper-function-name" "^7.18.9"
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-literals@^7.18.9":
  "integrity" "sha512-IFQDSRoTPnrAIrI5zoZv73IFeZu2dhu6irxQjY9rNjTT53VmKg9fenjvoiOWOkJ6mm4jKVPtdMzBY98Fp4Z4cg=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-literals/-/plugin-transform-literals-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-member-expression-literals@^7.18.6":
  "integrity" "sha512-qSF1ihLGO3q+/g48k85tUjD033C29TNTVB2paCwZPVmOsjn9pClvYYrM2VeJpBY2bcNkuny0YUyTNRyRxJ54KA=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-modules-amd@^7.19.6":
  "integrity" "sha512-NuzCt5IIYOW0O30UvqktzHYR2ud5bOWbY0yaxWZ6G+aFzOMJvrs5YHNikrbdaT15+KNO31nPOy5Fim3ku6Zb5g=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.20.11.tgz"
  "version" "7.20.11"
  dependencies:
    "@babel/helper-module-transforms" "^7.20.11"
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-modules-commonjs@^7.19.6":
  "integrity" "sha512-Cln+Yy04Gxua7iPdj6nOV96smLGjpElir5YwzF0LBPKoPlLDNJePNlrGGaybAJkd0zKRnOVXOgizSqPYMNYkzA=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.21.2.tgz"
  "version" "7.21.2"
  dependencies:
    "@babel/helper-module-transforms" "^7.21.2"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-simple-access" "^7.20.2"

"@babel/plugin-transform-modules-systemjs@^7.19.6":
  "integrity" "sha512-vVu5g9BPQKSFEmvt2TA4Da5N+QVS66EX21d8uoOihC+OCpUoGvzVsXeqFdtAEfVa5BILAeFt+U7yVmLbQnAJmw=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.20.11.tgz"
  "version" "7.20.11"
  dependencies:
    "@babel/helper-hoist-variables" "^7.18.6"
    "@babel/helper-module-transforms" "^7.20.11"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-validator-identifier" "^7.19.1"

"@babel/plugin-transform-modules-umd@^7.18.6":
  "integrity" "sha512-dcegErExVeXcRqNtkRU/z8WlBLnvD4MRnHgNs3MytRO1Mn1sHRyhbcpYbVMGclAqOjdW+9cfkdZno9dFdfKLfQ=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-module-transforms" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-named-capturing-groups-regex@^7.19.1":
  "integrity" "sha512-mOW4tTzi5iTLnw+78iEq3gr8Aoq4WNRGpmSlrogqaiCBoR1HFhpU4JkpQFOHfeYx3ReVIFWOQJS4aZBRvuZ6mA=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.20.5.tgz"
  "version" "7.20.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.20.5"
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-new-target@^7.18.6":
  "integrity" "sha512-DjwFA/9Iu3Z+vrAn+8pBUGcjhxKguSMlsFqeCKbhb9BAV756v0krzVK04CRDi/4aqmk8BsHb4a/gFcaA5joXRw=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-object-super@^7.18.6":
  "integrity" "sha512-uvGz6zk+pZoS1aTZrOvrbj6Pp/kK2mp45t2B+bTDre2UgsZZ8EZLSJtUg7m/no0zOJUWgFONpB7Zv9W2tSaFlA=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/helper-replace-supers" "^7.18.6"

"@babel/plugin-transform-parameters@^7.20.1", "@babel/plugin-transform-parameters@^7.20.7":
  "integrity" "sha512-Wxc+TvppQG9xWFYatvCGPvZ6+SIUxQ2ZdiBP+PHYMIjnPXD+uThCshaz4NZOnODAtBjjcVQQ/3OKs9LW28purQ=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.21.3.tgz"
  "version" "7.21.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-property-literals@^7.18.6":
  "integrity" "sha512-cYcs6qlgafTud3PAzrrRNbQtfpQ8+y/+M5tKmksS9+M1ckbH6kzY8MrexEM9mcA6JDsukE19iIRvAyYl463sMg=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-regenerator@^7.18.6":
  "integrity" "sha512-kW/oO7HPBtntbsahzQ0qSE3tFvkFwnbozz3NWFhLGqH75vLEg+sCGngLlhVkePlCs3Jv0dBBHDzCHxNiFAQKCQ=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.20.5.tgz"
  "version" "7.20.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "regenerator-transform" "^0.15.1"

"@babel/plugin-transform-reserved-words@^7.18.6":
  "integrity" "sha512-oX/4MyMoypzHjFrT1CdivfKZ+XvIPMFXwwxHp/r0Ddy2Vuomt4HDFGmft1TAY2yiTKiNSsh3kjBAzcM8kSdsjA=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-runtime@^7.12.15":
  "integrity" "sha512-ReY6pxwSzEU0b3r2/T/VhqMKg/AkceBT19X0UptA3/tYi5Pe2eXgEUH+NNMC5nok6c6XQz5tyVTUpuezRfSMSg=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.20.2"
    "babel-plugin-polyfill-corejs2" "^0.3.3"
    "babel-plugin-polyfill-corejs3" "^0.6.0"
    "babel-plugin-polyfill-regenerator" "^0.4.1"
    "semver" "^6.3.0"

"@babel/plugin-transform-shorthand-properties@^7.18.6":
  "integrity" "sha512-eCLXXJqv8okzg86ywZJbRn19YJHU4XUa55oz2wbHhaQVn/MM+XhukiT7SYqp/7o00dg52Rj51Ny+Ecw4oyoygw=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-spread@^7.19.0":
  "integrity" "sha512-ewBbHQ+1U/VnH1fxltbJqDeWBU1oNLG8Dj11uIv3xVf7nrQu0bPGe5Rf716r7K5Qz+SqtAOVswoVunoiBtGhxw=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-spread/-/plugin-transform-spread-7.20.7.tgz"
  "version" "7.20.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.20.0"

"@babel/plugin-transform-sticky-regex@^7.18.6":
  "integrity" "sha512-kfiDrDQ+PBsQDO85yj1icueWMfGfJFKN1KCkndygtu/C9+XUfydLC8Iv5UYJqRwy4zk8EcplRxEOeLyjq1gm6Q=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-template-literals@^7.18.9":
  "integrity" "sha512-S8cOWfT82gTezpYOiVaGHrCbhlHgKhQt8XH5ES46P2XWmX92yisoZywf5km75wv5sYcXDUCLMmMxOLCtthDgMA=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-typeof-symbol@^7.18.9":
  "integrity" "sha512-SRfwTtF11G2aemAZWivL7PD+C9z52v9EvMqH9BuYbabyPuKUvSWks3oCg6041pT925L4zVFqaVBeECwsmlguEw=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-unicode-escapes@^7.18.10":
  "integrity" "sha512-kKAdAI+YzPgGY/ftStBFXTI1LZFju38rYThnfMykS+IXy8BVx+res7s2fxf1l8I35DV2T97ezo6+SGrXz6B3iQ=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.18.10.tgz"
  "version" "7.18.10"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-unicode-regex@^7.18.6":
  "integrity" "sha512-gE7A6Lt7YLnNOL3Pb9BNeZvi+d8l7tcRrG4+pwJjK9hD2xX4mEvjlQW60G9EEmfXVYRPv9VRQcyegIVHCql/AA=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/preset-env@^7.12.16":
  "integrity" "sha512-1G0efQEWR1EHkKvKHqbG+IN/QdgwfByUpM5V5QroDzGV2t3S/WXNQd693cHiHTlCFMpr9B6FkPFXDA2lQcKoDg=="
  "resolved" "https://registry.npmmirror.com/@babel/preset-env/-/preset-env-7.20.2.tgz"
  "version" "7.20.2"
  dependencies:
    "@babel/compat-data" "^7.20.1"
    "@babel/helper-compilation-targets" "^7.20.0"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-validator-option" "^7.18.6"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.18.6"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.18.9"
    "@babel/plugin-proposal-async-generator-functions" "^7.20.1"
    "@babel/plugin-proposal-class-properties" "^7.18.6"
    "@babel/plugin-proposal-class-static-block" "^7.18.6"
    "@babel/plugin-proposal-dynamic-import" "^7.18.6"
    "@babel/plugin-proposal-export-namespace-from" "^7.18.9"
    "@babel/plugin-proposal-json-strings" "^7.18.6"
    "@babel/plugin-proposal-logical-assignment-operators" "^7.18.9"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.18.6"
    "@babel/plugin-proposal-numeric-separator" "^7.18.6"
    "@babel/plugin-proposal-object-rest-spread" "^7.20.2"
    "@babel/plugin-proposal-optional-catch-binding" "^7.18.6"
    "@babel/plugin-proposal-optional-chaining" "^7.18.9"
    "@babel/plugin-proposal-private-methods" "^7.18.6"
    "@babel/plugin-proposal-private-property-in-object" "^7.18.6"
    "@babel/plugin-proposal-unicode-property-regex" "^7.18.6"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-import-assertions" "^7.20.0"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"
    "@babel/plugin-transform-arrow-functions" "^7.18.6"
    "@babel/plugin-transform-async-to-generator" "^7.18.6"
    "@babel/plugin-transform-block-scoped-functions" "^7.18.6"
    "@babel/plugin-transform-block-scoping" "^7.20.2"
    "@babel/plugin-transform-classes" "^7.20.2"
    "@babel/plugin-transform-computed-properties" "^7.18.9"
    "@babel/plugin-transform-destructuring" "^7.20.2"
    "@babel/plugin-transform-dotall-regex" "^7.18.6"
    "@babel/plugin-transform-duplicate-keys" "^7.18.9"
    "@babel/plugin-transform-exponentiation-operator" "^7.18.6"
    "@babel/plugin-transform-for-of" "^7.18.8"
    "@babel/plugin-transform-function-name" "^7.18.9"
    "@babel/plugin-transform-literals" "^7.18.9"
    "@babel/plugin-transform-member-expression-literals" "^7.18.6"
    "@babel/plugin-transform-modules-amd" "^7.19.6"
    "@babel/plugin-transform-modules-commonjs" "^7.19.6"
    "@babel/plugin-transform-modules-systemjs" "^7.19.6"
    "@babel/plugin-transform-modules-umd" "^7.18.6"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.19.1"
    "@babel/plugin-transform-new-target" "^7.18.6"
    "@babel/plugin-transform-object-super" "^7.18.6"
    "@babel/plugin-transform-parameters" "^7.20.1"
    "@babel/plugin-transform-property-literals" "^7.18.6"
    "@babel/plugin-transform-regenerator" "^7.18.6"
    "@babel/plugin-transform-reserved-words" "^7.18.6"
    "@babel/plugin-transform-shorthand-properties" "^7.18.6"
    "@babel/plugin-transform-spread" "^7.19.0"
    "@babel/plugin-transform-sticky-regex" "^7.18.6"
    "@babel/plugin-transform-template-literals" "^7.18.9"
    "@babel/plugin-transform-typeof-symbol" "^7.18.9"
    "@babel/plugin-transform-unicode-escapes" "^7.18.10"
    "@babel/plugin-transform-unicode-regex" "^7.18.6"
    "@babel/preset-modules" "^0.1.5"
    "@babel/types" "^7.20.2"
    "babel-plugin-polyfill-corejs2" "^0.3.3"
    "babel-plugin-polyfill-corejs3" "^0.6.0"
    "babel-plugin-polyfill-regenerator" "^0.4.1"
    "core-js-compat" "^3.25.1"
    "semver" "^6.3.0"

"@babel/preset-modules@^0.1.5":
  "integrity" "sha512-A57th6YRG7oR3cq/yt/Y84MvGgE0eJG2F1JLhKuyG+jFxEgrd/HAMJatiFtmOiZurz+0DkrvbheCLaV5f2JfjA=="
  "resolved" "https://registry.npmmirror.com/@babel/preset-modules/-/preset-modules-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
    "@babel/plugin-transform-dotall-regex" "^7.4.4"
    "@babel/types" "^7.4.4"
    "esutils" "^2.0.2"

"@babel/regjsgen@^0.8.0":
  "integrity" "sha512-x/rqGMdzj+fWZvCOYForTghzbtqPDZ5gPwaoNGHdgDfF2QA/XZbCBp4Moo5scrkAMPhB7z26XM/AaHuIJdgauA=="
  "resolved" "https://registry.npmmirror.com/@babel/regjsgen/-/regjsgen-0.8.0.tgz"
  "version" "0.8.0"

"@babel/runtime@^7.12.13", "@babel/runtime@^7.8.4":
  "integrity" "sha512-xwII0//EObnq89Ji5AKYQaRYiW/nZ3llSv29d49IuxPhKbtJoLP+9QUUZ4nVragQVtaVGeZrpB+ZtG/Pdy/POw=="
  "resolved" "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "regenerator-runtime" "^0.13.11"

"@babel/template@^7.0.0", "@babel/template@^7.18.10", "@babel/template@^7.20.7":
  "integrity" "sha512-8SegXApWe6VoNw0r9JHpSteLKTpTiLZ4rMlGIm9JQ18KiCtyQiAMEazujAHrUS5flrcqYZa75ukev3P6QmUwUw=="
  "resolved" "https://registry.npmmirror.com/@babel/template/-/template-7.20.7.tgz"
  "version" "7.20.7"
  dependencies:
    "@babel/code-frame" "^7.18.6"
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"

"@babel/traverse@^7.0.0", "@babel/traverse@^7.20.5", "@babel/traverse@^7.20.7", "@babel/traverse@^7.21.0", "@babel/traverse@^7.21.2", "@babel/traverse@^7.21.3":
  "integrity" "sha512-XLyopNeaTancVitYZe2MlUEvgKb6YVVPXzofHgqHijCImG33b/uTurMS488ht/Hbsb2XK3U2BnSTxKVNGV3nGQ=="
  "resolved" "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.21.3.tgz"
  "version" "7.21.3"
  dependencies:
    "@babel/code-frame" "^7.18.6"
    "@babel/generator" "^7.21.3"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.21.0"
    "@babel/helper-hoist-variables" "^7.18.6"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/parser" "^7.21.3"
    "@babel/types" "^7.21.3"
    "debug" "^4.1.0"
    "globals" "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.18.6", "@babel/types@^7.18.9", "@babel/types@^7.20.0", "@babel/types@^7.20.2", "@babel/types@^7.20.5", "@babel/types@^7.20.7", "@babel/types@^7.21.0", "@babel/types@^7.21.2", "@babel/types@^7.21.3", "@babel/types@^7.4.4":
  "integrity" "sha512-sBGdETxC+/M4o/zKC0sl6sjWv62WFR/uzxrJ6uYyMLZOUlPnwzw0tKgVHOXxaAd5l2g8pEDM5RZ495GPQI77kg=="
  "resolved" "https://registry.npmmirror.com/@babel/types/-/types-7.21.3.tgz"
  "version" "7.21.3"
  dependencies:
    "@babel/helper-string-parser" "^7.19.4"
    "@babel/helper-validator-identifier" "^7.19.1"
    "to-fast-properties" "^2.0.0"

"@bangdao/buse-components@^0.2.9":
  "integrity" "sha512-sOR3/fXNYY5T+RW8Q1k5Z6qr+DHmkOu+ifGLicAtJTaTlHQWN4HqHm97HMQ7SOVgu59hku0HqrLcD/AzGHGZcg=="
  "resolved" "https://npm.bangdao-tech.com/@bangdao/buse-components/-/buse-components-0.2.9.tgz"
  "version" "0.2.9"
  dependencies:
    "core-js" "^3.8.3"

"@bangdao/pro-components@1.2.7":
  "integrity" "sha512-gbzn39LNbK2ZutmS1rX2WCzw5Sqfx5/mxo+2TMisMa5cuMxdh+a8EgFJZgr8EejX3FfDBaJYXqQjIuIRVNjIRA=="
  "resolved" "https://npm.bangdao-tech.com/@bangdao/pro-components/-/pro-components-1.2.7.tgz"
  "version" "1.2.7"
  dependencies:
    "sass" "^1.59.2"
    "sass-loader" "^13.2.0"

"@discoveryjs/json-ext@0.5.7":
  "integrity" "sha512-dBVuXR082gk3jsFp7Rd/JI4kytwGHecnCoTtXFb7DB6CNHp4rg5k1bhg0nWdLGLnOV71lmDzGQaLMy8iPLY0pw=="
  "resolved" "https://registry.npmmirror.com/@discoveryjs/json-ext/-/json-ext-0.5.7.tgz"
  "version" "0.5.7"

"@eslint/eslintrc@^0.4.3":
  "integrity" "sha512-J6KFFz5QCYUJq3pf0mjEcCJVERbzv71PUIDczuh9JkwGEzced6CO5ADLHB1rbf/+oPBtoPfMYNOpGDzCANlbXw=="
  "resolved" "https://registry.npmmirror.com/@eslint/eslintrc/-/eslintrc-0.4.3.tgz"
  "version" "0.4.3"
  dependencies:
    "ajv" "^6.12.4"
    "debug" "^4.1.1"
    "espree" "^7.3.0"
    "globals" "^13.9.0"
    "ignore" "^4.0.6"
    "import-fresh" "^3.2.1"
    "js-yaml" "^3.13.1"
    "minimatch" "^3.0.4"
    "strip-json-comments" "^3.1.1"

"@hapi/hoek@^9.0.0":
  "integrity" "sha512-/c6rf4UJlmHlC9b5BaNvzAcFv7HZ2QHaV0D4/HNlBdvFnvQq8RI4kYdhyPCl7Xj+oWvTWQ8ujhqS53LIgAe6KQ=="
  "resolved" "https://registry.npmmirror.com/@hapi/hoek/-/hoek-9.3.0.tgz"
  "version" "9.3.0"

"@hapi/topo@^5.0.0":
  "integrity" "sha512-foQZKJig7Ob0BMAYBfcJk8d77QtOe7Wo4ox7ff1lQYoNNAb6jwcY1ncdoy2e9wQZzvNy7ODZCYJkK8kzmcAnAg=="
  "resolved" "https://registry.npmmirror.com/@hapi/topo/-/topo-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "@hapi/hoek" "^9.0.0"

"@humanwhocodes/config-array@^0.5.0":
  "integrity" "sha512-FagtKFz74XrTl7y6HCzQpwDfXP0yhxe9lHLD1UZxjvZIcbyRz8zTFF/yYNfSfzU414eDwZ1SrO0Qvtyf+wFMQg=="
  "resolved" "https://registry.npmmirror.com/@humanwhocodes/config-array/-/config-array-0.5.0.tgz"
  "version" "0.5.0"
  dependencies:
    "@humanwhocodes/object-schema" "^1.2.0"
    "debug" "^4.1.1"
    "minimatch" "^3.0.4"

"@humanwhocodes/object-schema@^1.2.0":
  "integrity" "sha512-ZnQMnLV4e7hDlUvw8H+U8ASL02SS2Gn6+9Ac3wGGLIe7+je2AeAOxPY+izIPJDfFDb7eDjev0Us8MO1iFRN8hA=="
  "resolved" "https://registry.npmmirror.com/@humanwhocodes/object-schema/-/object-schema-1.2.1.tgz"
  "version" "1.2.1"

"@jridgewell/gen-mapping@^0.1.0":
  "integrity" "sha512-sQXCasFk+U8lWYEe66WxRDOE9PjVz4vSM51fTu3Hw+ClTpUSQb718772vH3pyS5pShp6lvQM7SxgIDXXXmOX7w=="
  "resolved" "https://registry.npmmirror.com/@jridgewell/gen-mapping/-/gen-mapping-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "@jridgewell/set-array" "^1.0.0"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@jridgewell/gen-mapping@^0.3.0", "@jridgewell/gen-mapping@^0.3.2":
  "integrity" "sha512-mh65xKQAzI6iBcFzwv28KVWSmCkdRBWoOh+bYQGW3+6OZvbbN3TqMGo5hqYxQniRcH9F2VZIoJCm4pa3BPDK/A=="
  "resolved" "https://registry.npmmirror.com/@jridgewell/gen-mapping/-/gen-mapping-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "@jridgewell/set-array" "^1.0.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/resolve-uri@3.1.0":
  "integrity" "sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w=="
  "resolved" "https://registry.npmmirror.com/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz"
  "version" "3.1.0"

"@jridgewell/set-array@^1.0.0", "@jridgewell/set-array@^1.0.1":
  "integrity" "sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw=="
  "resolved" "https://registry.npmmirror.com/@jridgewell/set-array/-/set-array-1.1.2.tgz"
  "version" "1.1.2"

"@jridgewell/source-map@^0.3.2":
  "integrity" "sha512-m7O9o2uR8k2ObDysZYzdfhb08VuEml5oWGiosa1VdaPZ/A6QyPkAJuwN0Q1lhULOf6B7MtQmHENS743hWtCrgw=="
  "resolved" "https://registry.npmmirror.com/@jridgewell/source-map/-/source-map-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@1.4.14":
  "integrity" "sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw=="
  "resolved" "https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz"
  "version" "1.4.14"

"@jridgewell/trace-mapping@^0.3.17", "@jridgewell/trace-mapping@^0.3.9":
  "integrity" "sha512-MCNzAp77qzKca9+W/+I0+sEpaUnZoeasnghNeVc41VZCEKaCH73Vq3BZZ/SzWIgrqE4H4ceI+p+b6C0mHf9T4g=="
  "resolved" "https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.17.tgz"
  "version" "0.3.17"
  dependencies:
    "@jridgewell/resolve-uri" "3.1.0"
    "@jridgewell/sourcemap-codec" "1.4.14"

"@leichtgewicht/ip-codec@^2.0.1":
  "integrity" "sha512-Hcv+nVC0kZnQ3tD9GVu5xSMR4VVYOteQIr/hwFPVEvPdlXqgGEuRjiheChHgdM+JyqdgNcmzZOX/tnl0JOiI7A=="
  "resolved" "https://registry.npmmirror.com/@leichtgewicht/ip-codec/-/ip-codec-2.0.4.tgz"
  "version" "2.0.4"

"@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1":
  "integrity" "sha512-54/JRvkLIzzDWshCWfuhadfrfZVPiElY8Fcgmg1HroEly/EDSszzhBAsarCux+D/kOslTRquNzuyGSmUSTTHGg=="
  "resolved" "https://registry.npmmirror.com/@nicolo-ribaudo/eslint-scope-5-internals/-/eslint-scope-5-internals-5.1.1-v1.tgz"
  "version" "5.1.1-v1"
  dependencies:
    "eslint-scope" "5.1.1"

"@node-ipc/js-queue@2.0.3":
  "integrity" "sha512-fL1wpr8hhD5gT2dA1qifeVaoDFlQR5es8tFuKqjHX+kdOtdNHnxkVZbtIrR2rxnMFvehkjaZRNV2H/gPXlb0hw=="
  "resolved" "https://registry.npmmirror.com/@node-ipc/js-queue/-/js-queue-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "easy-stack" "1.0.1"

"@nodelib/fs.scandir@2.1.5":
  "integrity" "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g=="
  "resolved" "https://registry.npmmirror.com/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  "version" "2.1.5"
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    "run-parallel" "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  "integrity" "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A=="
  "resolved" "https://registry.npmmirror.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  "version" "2.0.5"

"@nodelib/fs.walk@^1.2.3":
  "integrity" "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg=="
  "resolved" "https://registry.npmmirror.com/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  "version" "1.2.8"
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    "fastq" "^1.6.0"

"@polka/url@^1.0.0-next.20":
  "integrity" "sha512-a5Sab1C4/icpTZVzZc5Ghpz88yQtGOyNqYXcZgOssB2uuAr+wF/MvN6bgtW32q7HHrvBki+BsZ0OuNv6EV3K9g=="
  "resolved" "https://registry.npmmirror.com/@polka/url/-/url-1.0.0-next.21.tgz"
  "version" "1.0.0-next.21"

"@sideway/address@^4.1.3":
  "integrity" "sha512-7vwq+rOHVWjyXxVlR76Agnvhy8I9rpzjosTESvmhNeXOXdZZB15Fl+TI9x1SiHZH5Jv2wTGduSxFDIaq0m3DUw=="
  "resolved" "https://registry.npmmirror.com/@sideway/address/-/address-4.1.4.tgz"
  "version" "4.1.4"
  dependencies:
    "@hapi/hoek" "^9.0.0"

"@sideway/formula@^3.0.1":
  "integrity" "sha512-/poHZJJVjx3L+zVD6g9KgHfYnb443oi7wLu/XKojDviHy6HOEOA6z1Trk5aR1dGcmPenJEgb2sK2I80LeS3MIg=="
  "resolved" "https://registry.npmmirror.com/@sideway/formula/-/formula-3.0.1.tgz"
  "version" "3.0.1"

"@sideway/pinpoint@^2.0.0":
  "integrity" "sha512-RNiOoTPkptFtSVzQevY/yWtZwf/RxyVnPy/OcA9HBM3MlGDnBEYL5B41H0MTn0Uec8Hi+2qUtTfG2WWZBmMejQ=="
  "resolved" "https://registry.npmmirror.com/@sideway/pinpoint/-/pinpoint-2.0.0.tgz"
  "version" "2.0.0"

"@simonwep/pickr@~1.7.0":
  "integrity" "sha512-fq7jgKJT21uWGC1mARBHvvd1JYlEf93o7SuVOB4Lr0x/2UPuNC9Oe9n/GzVeg4oVtqMDfh1wIEJpsdOJEZb+3g=="
  "resolved" "https://registry.npmmirror.com/@simonwep/pickr/-/pickr-1.7.4.tgz"
  "version" "1.7.4"
  dependencies:
    "core-js" "^3.6.5"
    "nanopop" "^2.1.0"

"@soda/friendly-errors-webpack-plugin@^1.8.0":
  "integrity" "sha512-h2ooWqP8XuFqTXT+NyAFbrArzfQA7R6HTezADrvD9Re8fxMLTPPniLdqVTdDaO0eIoLaAwKT+d6w+5GeTk7Vbg=="
  "resolved" "https://registry.npmmirror.com/@soda/friendly-errors-webpack-plugin/-/friendly-errors-webpack-plugin-1.8.1.tgz"
  "version" "1.8.1"
  dependencies:
    "chalk" "^3.0.0"
    "error-stack-parser" "^2.0.6"
    "string-width" "^4.2.3"
    "strip-ansi" "^6.0.1"

"@soda/get-current-script@^1.0.2":
  "integrity" "sha512-T7VNNlYVM1SgQ+VsMYhnDkcGmWhQdL0bDyGm5TlQ3GBXnJscEClUUOKduWTmm2zCnvNLC1hc3JpuXjs/nFOc5w=="
  "resolved" "https://registry.npmmirror.com/@soda/get-current-script/-/get-current-script-1.0.2.tgz"
  "version" "1.0.2"

"@tootallnate/once@1":
  "integrity" "sha512-RbzJvlNzmRq5c3O09UipeuXno4tA1FE6ikOjxZK0tuxVv3412l64l5t1W5pj4+rJq9vpkm/kwiR07aZXnsKPxw=="
  "resolved" "https://registry.npmmirror.com/@tootallnate/once/-/once-1.1.2.tgz"
  "version" "1.1.2"

"@trysound/sax@0.2.0":
  "integrity" "sha512-L7z9BgrNEcYyUYtF+HaEfiS5ebkh9jXqbszz7pC0hRBPaatV0XjSD3+eHrpqFemQfgwiFF0QPIarnIihIDn7OA=="
  "resolved" "https://registry.npmmirror.com/@trysound/sax/-/sax-0.2.0.tgz"
  "version" "0.2.0"

"@types/body-parser@*":
  "integrity" "sha512-ALYone6pm6QmwZoAgeyNksccT9Q4AWZQ6PvfwR37GT6r6FWUPguq6sUmNGSMV2Wr761oQoBxwGGa6DR5o1DC9g=="
  "resolved" "https://registry.npmmirror.com/@types/body-parser/-/body-parser-1.19.2.tgz"
  "version" "1.19.2"
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/bonjour@^3.5.9":
  "integrity" "sha512-p7ienRMiS41Nu2/igbJxxLDWrSZ0WxM8UQgCeO9KhoVF7cOVFkrKsiDr1EsJIla8vV3oEEjGcz11jc5yimhzZw=="
  "resolved" "https://registry.npmmirror.com/@types/bonjour/-/bonjour-3.5.10.tgz"
  "version" "3.5.10"
  dependencies:
    "@types/node" "*"

"@types/connect-history-api-fallback@^1.3.5":
  "integrity" "sha512-h8QJa8xSb1WD4fpKBDcATDNGXghFj6/3GRWG6dhmRcu0RX1Ubasur2Uvx5aeEwlf0MwblEC2bMzzMQntxnw/Cw=="
  "resolved" "https://registry.npmmirror.com/@types/connect-history-api-fallback/-/connect-history-api-fallback-1.3.5.tgz"
  "version" "1.3.5"
  dependencies:
    "@types/express-serve-static-core" "*"
    "@types/node" "*"

"@types/connect@*":
  "integrity" "sha512-cdeYyv4KWoEgpBISTxWvqYsVy444DOqehiF3fM3ne10AmJ62RSyNkUnxMJXHQWRQQX2eR94m5y1IZyDwBjV9FQ=="
  "resolved" "https://registry.npmmirror.com/@types/connect/-/connect-3.4.35.tgz"
  "version" "3.4.35"
  dependencies:
    "@types/node" "*"

"@types/eslint-scope@^3.7.3":
  "integrity" "sha512-9K4zoImiZc3HlIp6AVUDE4CWYx22a+lhSZMYNpbjW04+YF0KWj4pJXnEMjdnFTiQibFFmElcsasJXDbdI/EPhA=="
  "resolved" "https://registry.npmmirror.com/@types/eslint-scope/-/eslint-scope-3.7.4.tgz"
  "version" "3.7.4"
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*", "@types/eslint@^7.29.0 || ^8.4.1":
  "integrity" "sha512-fa7GkppZVEByMWGbTtE5MbmXWJTVbrjjaS8K6uQj+XtuuUv1fsuPAxhygfqLmsb/Ufb3CV8deFCpiMfAgi00Sw=="
  "resolved" "https://registry.npmmirror.com/@types/eslint/-/eslint-8.21.3.tgz"
  "version" "8.21.3"
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*":
  "integrity" "sha512-WulqXMDUTYAXCjZnk6JtIHPigp55cVtDgDrO2gHRwhyJto21+1zbVCtOYB2L1F9w4qCQ0rOGWBnBe0FNTiEJIQ=="
  "resolved" "https://registry.npmmirror.com/@types/estree/-/estree-1.0.0.tgz"
  "version" "1.0.0"

"@types/estree@^0.0.51":
  "integrity" "sha512-CuPgU6f3eT/XgKKPqKd/gLZV1Xmvf1a2R5POBOGQa6uv82xpls89HU5zKeVoyR8XzHd1RGNOlQlvUe3CFkjWNQ=="
  "resolved" "https://registry.npmmirror.com/@types/estree/-/estree-0.0.51.tgz"
  "version" "0.0.51"

"@types/express-serve-static-core@*", "@types/express-serve-static-core@^4.17.33":
  "integrity" "sha512-TPBqmR/HRYI3eC2E5hmiivIzv+bidAfXofM+sbonAGvyDhySGw9/PQZFt2BLOrjUUR++4eJVpx6KnLQK1Fk9tA=="
  "resolved" "https://registry.npmmirror.com/@types/express-serve-static-core/-/express-serve-static-core-4.17.33.tgz"
  "version" "4.17.33"
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"

"@types/express@*", "@types/express@^4.17.13":
  "integrity" "sha512-Q4FmmuLGBG58btUnfS1c1r/NQdlp3DMfGDGig8WhfpA2YRUtEkxAjkZb0yvplJGYdF1fsQ81iMDcH24sSCNC/Q=="
  "resolved" "https://registry.npmmirror.com/@types/express/-/express-4.17.17.tgz"
  "version" "4.17.17"
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.33"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/html-minifier-terser@^6.0.0":
  "integrity" "sha512-oh/6byDPnL1zeNXFrDXFLyZjkr1MsBG667IM792caf1L2UPOOMf65NFzjUH/ltyfwjAGfs1rsX1eftK0jC/KIg=="
  "resolved" "https://registry.npmmirror.com/@types/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz"
  "version" "6.1.0"

"@types/http-proxy@^1.17.8":
  "integrity" "sha512-Qs5aULi+zV1bwKAg5z1PWnDXWmsn+LxIvUGv6E2+OOMYhclZMO+OXd9pYVf2gLykf2I7IV2u7oTHwChPNsvJ7g=="
  "resolved" "https://registry.npmmirror.com/@types/http-proxy/-/http-proxy-1.17.10.tgz"
  "version" "1.17.10"
  dependencies:
    "@types/node" "*"

"@types/json-schema@*", "@types/json-schema@^7.0.5", "@types/json-schema@^7.0.8", "@types/json-schema@^7.0.9":
  "integrity" "sha512-wOuvG1SN4Us4rez+tylwwwCV1psiNVOkJeM3AUWUNWg/jDQY2+HE/444y5gc+jBmRqASOm2Oeh5c1axHobwRKQ=="
  "resolved" "https://registry.npmmirror.com/@types/json-schema/-/json-schema-7.0.11.tgz"
  "version" "7.0.11"

"@types/mime@*":
  "integrity" "sha512-Y4XFY5VJAuw0FgAqPNd6NNoV44jbq9Bz2L7Rh/J6jLTiHBSBJa9fxqQIvkIld4GsoDOcCbvzOUAbLPsSKKg+uA=="
  "resolved" "https://registry.npmmirror.com/@types/mime/-/mime-3.0.1.tgz"
  "version" "3.0.1"

"@types/minimist@^1.2.0":
  "integrity" "sha512-jhuKLIRrhvCPLqwPcx6INqmKeiA5EWrsCOPhrlFSrbrmU4ZMPjj5Ul/oLCMDO98XRUIwVm78xICz4EPCektzeQ=="
  "resolved" "https://registry.npmmirror.com/@types/minimist/-/minimist-1.2.2.tgz"
  "version" "1.2.2"

"@types/node@*":
  "integrity" "sha512-E5Kwq2n4SbMzQOn6wnmBjuK9ouqlURrcZDVfbo9ftDDTFt3nk7ZKK4GMOzoYgnpQJKcxwQw+lGaBvvlMo0qN/Q=="
  "resolved" "https://registry.npmmirror.com/@types/node/-/node-18.15.11.tgz"
  "version" "18.15.11"

"@types/normalize-package-data@^2.4.0":
  "integrity" "sha512-Gj7cI7z+98M282Tqmp2K5EIsoouUEzbBJhQQzDE3jSIRk6r9gsz0oUokqIUR4u1R3dMHo0pDHM7sNOHyhulypw=="
  "resolved" "https://registry.npmmirror.com/@types/normalize-package-data/-/normalize-package-data-2.4.1.tgz"
  "version" "2.4.1"

"@types/parse-json@^4.0.0":
  "integrity" "sha512-//oorEZjL6sbPcKUaCdIGlIUeH26mgzimjBB77G6XRgnDl/L5wOnpyBGRe/Mmf5CVW3PwEBE1NjiMZ/ssFh4wA=="
  "resolved" "https://registry.npmmirror.com/@types/parse-json/-/parse-json-4.0.0.tgz"
  "version" "4.0.0"

"@types/qs@*":
  "integrity" "sha512-FGa1F62FT09qcrueBA6qYTrJPVDzah9a+493+o2PCXsesWHIn27G98TsSMs3WPNbZIEj4+VJf6saSFpvD+3Zsw=="
  "resolved" "https://registry.npmmirror.com/@types/qs/-/qs-6.9.7.tgz"
  "version" "6.9.7"

"@types/range-parser@*":
  "integrity" "sha512-EEhsLsD6UsDM1yFhAvy0Cjr6VwmpMWqFBCb9w07wVugF7w9nfajxLuVmngTIpgS6svCnm6Vaw+MZhoDCKnOfsw=="
  "resolved" "https://registry.npmmirror.com/@types/range-parser/-/range-parser-1.2.4.tgz"
  "version" "1.2.4"

"@types/retry@0.12.0":
  "integrity" "sha512-wWKOClTTiizcZhXnPY4wikVAwmdYHp8q6DmC+EJUzAMsycb7HB32Kh9RN4+0gExjmPmZSAQjgURXIGATPegAvA=="
  "resolved" "https://registry.npmmirror.com/@types/retry/-/retry-0.12.0.tgz"
  "version" "0.12.0"

"@types/serve-index@^1.9.1":
  "integrity" "sha512-d/Hs3nWDxNL2xAczmOVZNj92YZCS6RGxfBPjKzuu/XirCgXdpKEb88dYNbrYGint6IVWLNP+yonwVAuRC0T2Dg=="
  "resolved" "https://registry.npmmirror.com/@types/serve-index/-/serve-index-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "@types/express" "*"

"@types/serve-static@*", "@types/serve-static@^1.13.10":
  "integrity" "sha512-NUo5XNiAdULrJENtJXZZ3fHtfMolzZwczzBbnAeBbqBwG+LaG6YaJtuwzwGSQZ2wsCrxjEhNNjAkKigy3n8teQ=="
  "resolved" "https://registry.npmmirror.com/@types/serve-static/-/serve-static-1.15.1.tgz"
  "version" "1.15.1"
  dependencies:
    "@types/mime" "*"
    "@types/node" "*"

"@types/sockjs@^0.3.33":
  "integrity" "sha512-f0KEEe05NvUnat+boPTZ0dgaLZ4SfSouXUgv5noUiefG2ajgKjmETo9ZJyuqsl7dfl2aHlLJUiki6B4ZYldiiw=="
  "resolved" "https://registry.npmmirror.com/@types/sockjs/-/sockjs-0.3.33.tgz"
  "version" "0.3.33"
  dependencies:
    "@types/node" "*"

"@types/ws@^8.5.1":
  "integrity" "sha512-zdQDHKUgcX/zBc4GrwsE/7dVdAD8JR4EuiAXiiUhhfyIJXXb2+PrGshFyeXWQPMmmZ2XxgaqclgpIC7eTXc1mg=="
  "resolved" "https://registry.npmmirror.com/@types/ws/-/ws-8.5.4.tgz"
  "version" "8.5.4"
  dependencies:
    "@types/node" "*"

"@vue/babel-helper-vue-jsx-merge-props@^1.4.0":
  "integrity" "sha512-JkqXfCkUDp4PIlFdDQ0TdXoIejMtTHP67/pvxlgeY+u5k3LEdKuWZ3LK6xkxo52uDoABIVyRwqVkfLQJhk7VBA=="
  "resolved" "https://registry.npmmirror.com/@vue/babel-helper-vue-jsx-merge-props/-/babel-helper-vue-jsx-merge-props-1.4.0.tgz"
  "version" "1.4.0"

"@vue/babel-helper-vue-transform-on@^1.0.2":
  "integrity" "sha512-hz4R8tS5jMn8lDq6iD+yWL6XNB699pGIVLk7WSJnn1dbpjaazsjZQkieJoRX6gW5zpYSCFqQ7jUquPNY65tQYA=="
  "resolved" "https://registry.npmmirror.com/@vue/babel-helper-vue-transform-on/-/babel-helper-vue-transform-on-1.0.2.tgz"
  "version" "1.0.2"

"@vue/babel-plugin-jsx@^1.0.3":
  "integrity" "sha512-j2uVfZjnB5+zkcbc/zsOc0fSNGCMMjaEXP52wdwdIfn0qjFfEYpYZBFKFg+HHnQeJCVrjOeO0YxgaL7DMrym9w=="
  "resolved" "https://registry.npmmirror.com/@vue/babel-plugin-jsx/-/babel-plugin-jsx-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.0.0"
    "@babel/template" "^7.0.0"
    "@babel/traverse" "^7.0.0"
    "@babel/types" "^7.0.0"
    "@vue/babel-helper-vue-transform-on" "^1.0.2"
    "camelcase" "^6.0.0"
    "html-tags" "^3.1.0"
    "svg-tags" "^1.0.0"

"@vue/babel-plugin-transform-vue-jsx@^1.4.0":
  "integrity" "sha512-Fmastxw4MMx0vlgLS4XBX0XiBbUFzoMGeVXuMV08wyOfXdikAFqBTuYPR0tlk+XskL19EzHc39SgjrPGY23JnA=="
  "resolved" "https://registry.npmmirror.com/@vue/babel-plugin-transform-vue-jsx/-/babel-plugin-transform-vue-jsx-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.4.0"
    "html-tags" "^2.0.0"
    "lodash.kebabcase" "^4.1.1"
    "svg-tags" "^1.0.0"

"@vue/babel-preset-app@^5.0.8":
  "integrity" "sha512-yl+5qhpjd8e1G4cMXfORkkBlvtPCIgmRf3IYCWYDKIQ7m+PPa5iTm4feiNmCMD6yGqQWMhhK/7M3oWGL9boKwg=="
  "resolved" "https://registry.npmmirror.com/@vue/babel-preset-app/-/babel-preset-app-5.0.8.tgz"
  "version" "5.0.8"
  dependencies:
    "@babel/core" "^7.12.16"
    "@babel/helper-compilation-targets" "^7.12.16"
    "@babel/helper-module-imports" "^7.12.13"
    "@babel/plugin-proposal-class-properties" "^7.12.13"
    "@babel/plugin-proposal-decorators" "^7.12.13"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-jsx" "^7.12.13"
    "@babel/plugin-transform-runtime" "^7.12.15"
    "@babel/preset-env" "^7.12.16"
    "@babel/runtime" "^7.12.13"
    "@vue/babel-plugin-jsx" "^1.0.3"
    "@vue/babel-preset-jsx" "^1.1.2"
    "babel-plugin-dynamic-import-node" "^2.3.3"
    "core-js" "^3.8.3"
    "core-js-compat" "^3.8.3"
    "semver" "^7.3.4"

"@vue/babel-preset-jsx@^1.1.2":
  "integrity" "sha512-QmfRpssBOPZWL5xw7fOuHNifCQcNQC1PrOo/4fu6xlhlKJJKSA3HqX92Nvgyx8fqHZTUGMPHmFA+IDqwXlqkSA=="
  "resolved" "https://registry.npmmirror.com/@vue/babel-preset-jsx/-/babel-preset-jsx-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@vue/babel-helper-vue-jsx-merge-props" "^1.4.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.4.0"
    "@vue/babel-sugar-composition-api-inject-h" "^1.4.0"
    "@vue/babel-sugar-composition-api-render-instance" "^1.4.0"
    "@vue/babel-sugar-functional-vue" "^1.4.0"
    "@vue/babel-sugar-inject-h" "^1.4.0"
    "@vue/babel-sugar-v-model" "^1.4.0"
    "@vue/babel-sugar-v-on" "^1.4.0"

"@vue/babel-sugar-composition-api-inject-h@^1.4.0":
  "integrity" "sha512-VQq6zEddJHctnG4w3TfmlVp5FzDavUSut/DwR0xVoe/mJKXyMcsIibL42wPntozITEoY90aBV0/1d2KjxHU52g=="
  "resolved" "https://registry.npmmirror.com/@vue/babel-sugar-composition-api-inject-h/-/babel-sugar-composition-api-inject-h-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-composition-api-render-instance@^1.4.0":
  "integrity" "sha512-6ZDAzcxvy7VcnCjNdHJ59mwK02ZFuP5CnucloidqlZwVQv5CQLijc3lGpR7MD3TWFi78J7+a8J56YxbCtHgT9Q=="
  "resolved" "https://registry.npmmirror.com/@vue/babel-sugar-composition-api-render-instance/-/babel-sugar-composition-api-render-instance-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-functional-vue@^1.4.0":
  "integrity" "sha512-lTEB4WUFNzYt2In6JsoF9sAYVTo84wC4e+PoZWSgM6FUtqRJz7wMylaEhSRgG71YF+wfLD6cc9nqVeXN2rwBvw=="
  "resolved" "https://registry.npmmirror.com/@vue/babel-sugar-functional-vue/-/babel-sugar-functional-vue-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-inject-h@^1.4.0":
  "integrity" "sha512-muwWrPKli77uO2fFM7eA3G1lAGnERuSz2NgAxuOLzrsTlQl8W4G+wwbM4nB6iewlKbwKRae3nL03UaF5ffAPMA=="
  "resolved" "https://registry.npmmirror.com/@vue/babel-sugar-inject-h/-/babel-sugar-inject-h-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-v-model@^1.4.0":
  "integrity" "sha512-0t4HGgXb7WHYLBciZzN5s0Hzqan4Ue+p/3FdQdcaHAb7s5D9WZFGoSxEZHrR1TFVZlAPu1bejTKGeAzaaG3NCQ=="
  "resolved" "https://registry.npmmirror.com/@vue/babel-sugar-v-model/-/babel-sugar-v-model-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.4.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.4.0"
    "camelcase" "^5.0.0"
    "html-tags" "^2.0.0"
    "svg-tags" "^1.0.0"

"@vue/babel-sugar-v-on@^1.4.0":
  "integrity" "sha512-m+zud4wKLzSKgQrWwhqRObWzmTuyzl6vOP7024lrpeJM4x2UhQtRDLgYjXAw9xBXjCwS0pP9kXjg91F9ZNo9JA=="
  "resolved" "https://registry.npmmirror.com/@vue/babel-sugar-v-on/-/babel-sugar-v-on-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.4.0"
    "camelcase" "^5.0.0"

"@vue/cli-overlay@^5.0.8":
  "integrity" "sha512-KmtievE/B4kcXp6SuM2gzsnSd8WebkQpg3XaB6GmFh1BJGRqa1UiW9up7L/Q67uOdTigHxr5Ar2lZms4RcDjwQ=="
  "resolved" "https://registry.npmmirror.com/@vue/cli-overlay/-/cli-overlay-5.0.8.tgz"
  "version" "5.0.8"

"@vue/cli-plugin-babel@~5.0.0":
  "integrity" "sha512-a4qqkml3FAJ3auqB2kN2EMPocb/iu0ykeELwed+9B1c1nQ1HKgslKMHMPavYx3Cd/QAx2mBD4hwKBqZXEI/CsQ=="
  "resolved" "https://registry.npmmirror.com/@vue/cli-plugin-babel/-/cli-plugin-babel-5.0.8.tgz"
  "version" "5.0.8"
  dependencies:
    "@babel/core" "^7.12.16"
    "@vue/babel-preset-app" "^5.0.8"
    "@vue/cli-shared-utils" "^5.0.8"
    "babel-loader" "^8.2.2"
    "thread-loader" "^3.0.0"
    "webpack" "^5.54.0"

"@vue/cli-plugin-eslint@~5.0.0":
  "integrity" "sha512-d11+I5ONYaAPW1KyZj9GlrV/E6HZePq5L5eAF5GgoVdu6sxr6bDgEoxzhcS1Pk2eh8rn1MxG/FyyR+eCBj/CNg=="
  "resolved" "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.8.tgz"
  "version" "5.0.8"
  dependencies:
    "@vue/cli-shared-utils" "^5.0.8"
    "eslint-webpack-plugin" "^3.1.0"
    "globby" "^11.0.2"
    "webpack" "^5.54.0"
    "yorkie" "^2.0.0"

"@vue/cli-plugin-router@^5.0.8", "@vue/cli-plugin-router@~5.0.0":
  "integrity" "sha512-Gmv4dsGdAsWPqVijz3Ux2OS2HkMrWi1ENj2cYL75nUeL+Xj5HEstSqdtfZ0b1q9NCce+BFB6QnHfTBXc/fCvMg=="
  "resolved" "https://registry.npmmirror.com/@vue/cli-plugin-router/-/cli-plugin-router-5.0.8.tgz"
  "version" "5.0.8"
  dependencies:
    "@vue/cli-shared-utils" "^5.0.8"

"@vue/cli-plugin-vuex@^5.0.8", "@vue/cli-plugin-vuex@~5.0.0":
  "integrity" "sha512-HSYWPqrunRE5ZZs8kVwiY6oWcn95qf/OQabwLfprhdpFWAGtLStShjsGED2aDpSSeGAskQETrtR/5h7VqgIlBA=="
  "resolved" "https://registry.npmmirror.com/@vue/cli-plugin-vuex/-/cli-plugin-vuex-5.0.8.tgz"
  "version" "5.0.8"

"@vue/cli-service@^3.0.0 || ^4.0.0 || ^5.0.0-0", "@vue/cli-service@~5.0.0":
  "integrity" "sha512-nV7tYQLe7YsTtzFrfOMIHc5N2hp5lHG2rpYr0aNja9rNljdgcPZLyQRb2YRivTHqTv7lI962UXFURcpStHgyFw=="
  "resolved" "https://registry.npmmirror.com/@vue/cli-service/-/cli-service-5.0.8.tgz"
  "version" "5.0.8"
  dependencies:
    "@babel/helper-compilation-targets" "^7.12.16"
    "@soda/friendly-errors-webpack-plugin" "^1.8.0"
    "@soda/get-current-script" "^1.0.2"
    "@types/minimist" "^1.2.0"
    "@vue/cli-overlay" "^5.0.8"
    "@vue/cli-plugin-router" "^5.0.8"
    "@vue/cli-plugin-vuex" "^5.0.8"
    "@vue/cli-shared-utils" "^5.0.8"
    "@vue/component-compiler-utils" "^3.3.0"
    "@vue/vue-loader-v15" "npm:vue-loader@^15.9.7"
    "@vue/web-component-wrapper" "^1.3.0"
    "acorn" "^8.0.5"
    "acorn-walk" "^8.0.2"
    "address" "^1.1.2"
    "autoprefixer" "^10.2.4"
    "browserslist" "^4.16.3"
    "case-sensitive-paths-webpack-plugin" "^2.3.0"
    "cli-highlight" "^2.1.10"
    "clipboardy" "^2.3.0"
    "cliui" "^7.0.4"
    "copy-webpack-plugin" "^9.0.1"
    "css-loader" "^6.5.0"
    "css-minimizer-webpack-plugin" "^3.0.2"
    "cssnano" "^5.0.0"
    "debug" "^4.1.1"
    "default-gateway" "^6.0.3"
    "dotenv" "^10.0.0"
    "dotenv-expand" "^5.1.0"
    "fs-extra" "^9.1.0"
    "globby" "^11.0.2"
    "hash-sum" "^2.0.0"
    "html-webpack-plugin" "^5.1.0"
    "is-file-esm" "^1.0.0"
    "launch-editor-middleware" "^2.2.1"
    "lodash.defaultsdeep" "^4.6.1"
    "lodash.mapvalues" "^4.6.0"
    "mini-css-extract-plugin" "^2.5.3"
    "minimist" "^1.2.5"
    "module-alias" "^2.2.2"
    "portfinder" "^1.0.26"
    "postcss" "^8.2.6"
    "postcss-loader" "^6.1.1"
    "progress-webpack-plugin" "^1.0.12"
    "ssri" "^8.0.1"
    "terser-webpack-plugin" "^5.1.1"
    "thread-loader" "^3.0.0"
    "vue-loader" "^17.0.0"
    "vue-style-loader" "^4.1.3"
    "webpack" "^5.54.0"
    "webpack-bundle-analyzer" "^4.4.0"
    "webpack-chain" "^6.5.1"
    "webpack-dev-server" "^4.7.3"
    "webpack-merge" "^5.7.3"
    "webpack-virtual-modules" "^0.4.2"
    "whatwg-fetch" "^3.6.2"

"@vue/cli-shared-utils@^5.0.8":
  "integrity" "sha512-uK2YB7bBVuQhjOJF+O52P9yFMXeJVj7ozqJkwYE9PlMHL1LMHjtCYm4cSdOebuPzyP+/9p0BimM/OqxsevIopQ=="
  "resolved" "https://registry.npmmirror.com/@vue/cli-shared-utils/-/cli-shared-utils-5.0.8.tgz"
  "version" "5.0.8"
  dependencies:
    "@achrinza/node-ipc" "^9.2.5"
    "chalk" "^4.1.2"
    "execa" "^1.0.0"
    "joi" "^17.4.0"
    "launch-editor" "^2.2.1"
    "lru-cache" "^6.0.0"
    "node-fetch" "^2.6.7"
    "open" "^8.0.2"
    "ora" "^5.3.0"
    "read-pkg" "^5.1.1"
    "semver" "^7.3.4"
    "strip-ansi" "^6.0.0"

"@vue/compiler-sfc@2.7.14":
  "integrity" "sha512-aNmNHyLPsw+sVvlQFQ2/8sjNuLtK54TC6cuKnVzAY93ks4ZBrvwQSnkkIh7bsbNhum5hJBS00wSDipQ937f5DA=="
  "resolved" "https://registry.npmmirror.com/@vue/compiler-sfc/-/compiler-sfc-2.7.14.tgz"
  "version" "2.7.14"
  dependencies:
    "@babel/parser" "^7.18.4"
    "postcss" "^8.4.14"
    "source-map" "^0.6.1"

"@vue/component-compiler-utils@^3.1.0", "@vue/component-compiler-utils@^3.3.0":
  "integrity" "sha512-97sfH2mYNU+2PzGrmK2haqffDpVASuib9/w2/noxiFi31Z54hW+q3izKQXXQZSNhtiUpAI36uSuYepeBe4wpHQ=="
  "resolved" "https://registry.npmmirror.com/@vue/component-compiler-utils/-/component-compiler-utils-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "consolidate" "^0.15.1"
    "hash-sum" "^1.0.2"
    "lru-cache" "^4.1.2"
    "merge-source-map" "^1.1.0"
    "postcss" "^7.0.36"
    "postcss-selector-parser" "^6.0.2"
    "source-map" "~0.6.1"
    "vue-template-es2015-compiler" "^1.9.0"
  optionalDependencies:
    "prettier" "^1.18.2 || ^2.0.0"

"@vue/vue-loader-v15@npm:vue-loader@^15.9.7":
  "integrity" "sha512-SaPHK1A01VrNthlix6h1hq4uJu7S/z0kdLUb6klubo738NeQoLbS6V9/d8Pv19tU0XdQKju3D1HSKuI8wJ5wMA=="
  "resolved" "https://registry.npmmirror.com/vue-loader/-/vue-loader-15.10.1.tgz"
  "version" "15.10.1"
  dependencies:
    "@vue/component-compiler-utils" "^3.1.0"
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.1.0"
    "vue-hot-reload-api" "^2.3.0"
    "vue-style-loader" "^4.1.0"

"@vue/web-component-wrapper@^1.3.0":
  "integrity" "sha512-Iu8Tbg3f+emIIMmI2ycSI8QcEuAUgPTgHwesDU1eKMLE4YC/c/sFbGc70QgMq31ijRftV0R7vCm9co6rldCeOA=="
  "resolved" "https://registry.npmmirror.com/@vue/web-component-wrapper/-/web-component-wrapper-1.3.0.tgz"
  "version" "1.3.0"

"@webassemblyjs/ast@1.11.1":
  "integrity" "sha512-ukBh14qFLjxTQNTXocdyksN5QdM28S1CxHt2rdskFyL+xFV7VremuBLVbmCePj+URalXBENx/9Lm7lnhihtCSw=="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/ast/-/ast-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/helper-numbers" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"

"@webassemblyjs/floating-point-hex-parser@1.11.1":
  "integrity" "sha512-iGRfyc5Bq+NnNuX8b5hwBrRjzf0ocrJPI6GWFodBFzmFnyvrQ83SHKhmilCU/8Jv67i4GJZBMhEzltxzcNagtQ=="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.11.1.tgz"
  "version" "1.11.1"

"@webassemblyjs/helper-api-error@1.11.1":
  "integrity" "sha512-RlhS8CBCXfRUR/cwo2ho9bkheSXG0+NwooXcc3PAILALf2QLdFyj7KGsKRbVc95hZnhnERon4kW/D3SZpp6Tcg=="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/helper-api-error/-/helper-api-error-1.11.1.tgz"
  "version" "1.11.1"

"@webassemblyjs/helper-buffer@1.11.1":
  "integrity" "sha512-gwikF65aDNeeXa8JxXa2BAk+REjSyhrNC9ZwdT0f8jc4dQQeDQ7G4m0f2QCLPJiMTTO6wfDmRmj/pW0PsUvIcA=="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/helper-buffer/-/helper-buffer-1.11.1.tgz"
  "version" "1.11.1"

"@webassemblyjs/helper-numbers@1.11.1":
  "integrity" "sha512-vDkbxiB8zfnPdNK9Rajcey5C0w+QJugEglN0of+kmO8l7lDb77AnlKYQF7aarZuCrv+l0UvqL+68gSDr3k9LPQ=="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/helper-numbers/-/helper-numbers-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.11.1"
    "@webassemblyjs/helper-api-error" "1.11.1"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.11.1":
  "integrity" "sha512-PvpoOGiJwXeTrSf/qfudJhwlvDQxFgelbMqtq52WWiXC6Xgg1IREdngmPN3bs4RoO83PnL/nFrxucXj1+BX62Q=="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.11.1.tgz"
  "version" "1.11.1"

"@webassemblyjs/helper-wasm-section@1.11.1":
  "integrity" "sha512-10P9No29rYX1j7F3EVPX3JvGPQPae+AomuSTPiF9eBQeChHI6iqjMIwR9JmOJXwpnn/oVGDk7I5IlskuMwU/pg=="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"

"@webassemblyjs/ieee754@1.11.1":
  "integrity" "sha512-hJ87QIPtAMKbFq6CGTkZYJivEwZDbQUgYd3qKSadTNOhVY7p+gfP6Sr0lLRVTaG1JjFj+r3YchoqRYxNH3M0GQ=="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/ieee754/-/ieee754-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.11.1":
  "integrity" "sha512-BJ2P0hNZ0u+Th1YZXJpzW6miwqQUGcIHT1G/sf72gLVD9DZ5AdYTqPNbHZh6K1M5VmKvFXwGSWZADz+qBWxeRw=="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/leb128/-/leb128-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.11.1":
  "integrity" "sha512-9kqcxAEdMhiwQkHpkNiorZzqpGrodQQ2IGrHHxCy+Ozng0ofyMA0lTqiLkVs1uzTRejX+/O0EOT7KxqVPuXosQ=="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/utf8/-/utf8-1.11.1.tgz"
  "version" "1.11.1"

"@webassemblyjs/wasm-edit@1.11.1":
  "integrity" "sha512-g+RsupUC1aTHfR8CDgnsVRVZFJqdkFHpsHMfJuWQzWU3tvnLC07UqHICfP+4XyL2tnr1amvl1Sdp06TnYCmVkA=="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/wasm-edit/-/wasm-edit-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/helper-wasm-section" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"
    "@webassemblyjs/wasm-opt" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"
    "@webassemblyjs/wast-printer" "1.11.1"

"@webassemblyjs/wasm-gen@1.11.1":
  "integrity" "sha512-F7QqKXwwNlMmsulj6+O7r4mmtAlCWfO/0HdgOxSklZfQcDu0TpLiD1mRt/zF25Bk59FIjEuGAIyn5ei4yMfLhA=="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/wasm-gen/-/wasm-gen-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/ieee754" "1.11.1"
    "@webassemblyjs/leb128" "1.11.1"
    "@webassemblyjs/utf8" "1.11.1"

"@webassemblyjs/wasm-opt@1.11.1":
  "integrity" "sha512-VqnkNqnZlU5EB64pp1l7hdm3hmQw7Vgqa0KF/KCNO9sIpI6Fk6brDEiX+iCOYrvMuBWDws0NkTOxYEb85XQHHw=="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/wasm-opt/-/wasm-opt-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"

"@webassemblyjs/wasm-parser@1.11.1":
  "integrity" "sha512-rrBujw+dJu32gYB7/Lup6UhdkPx9S9SnobZzRVL7VcBH9Bt9bCBLEuX/YXOOtBsOZ4NQrRykKhffRWHvigQvOA=="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/wasm-parser/-/wasm-parser-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-api-error" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/ieee754" "1.11.1"
    "@webassemblyjs/leb128" "1.11.1"
    "@webassemblyjs/utf8" "1.11.1"

"@webassemblyjs/wast-printer@1.11.1":
  "integrity" "sha512-IQboUWM4eKzWW+N/jij2sRatKMh99QEelo3Eb2q0qXkvPRISAj8Qxtmw5itwqK+TTkBuUIE45AxYPToqPtL5gg=="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/wast-printer/-/wast-printer-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  "integrity" "sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA=="
  "resolved" "https://registry.npmmirror.com/@xtuc/ieee754/-/ieee754-1.2.0.tgz"
  "version" "1.2.0"

"@xtuc/long@4.2.2":
  "integrity" "sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ=="
  "resolved" "https://registry.npmmirror.com/@xtuc/long/-/long-4.2.2.tgz"
  "version" "4.2.2"

"abab@^2.0.3", "abab@^2.0.5":
  "integrity" "sha512-j2afSsaIENvHZN2B8GOpF566vZ5WVk5opAiMTvWgaQT8DkbOqsTfvNAvHoRGU2zzP8cPoqys+xHTRDWW8L+/BA=="
  "resolved" "https://registry.npmmirror.com/abab/-/abab-2.0.6.tgz"
  "version" "2.0.6"

"accepts@~1.3.4", "accepts@~1.3.5", "accepts@~1.3.8":
  "integrity" "sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw=="
  "resolved" "https://registry.npmmirror.com/accepts/-/accepts-1.3.8.tgz"
  "version" "1.3.8"
  dependencies:
    "mime-types" "~2.1.34"
    "negotiator" "0.6.3"

"acorn-globals@^6.0.0":
  "integrity" "sha512-ZQl7LOWaF5ePqqcX4hLuv/bLXYQNfNWw2c0/yX/TsPRKamzHcTGQnlCjHT3TsmkOUVEPS3crCxiPfdzE/Trlhg=="
  "resolved" "https://registry.npmmirror.com/acorn-globals/-/acorn-globals-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "acorn" "^7.1.1"
    "acorn-walk" "^7.1.1"

"acorn-import-assertions@^1.7.6":
  "integrity" "sha512-m7VZ3jwz4eK6A4Vtt8Ew1/mNbP24u0FhdyfA7fSvnJR6LMdfOYnmuIrrJAgrYfYJ10F/otaHTtrtrtmHdMNzEw=="
  "resolved" "https://registry.npmmirror.com/acorn-import-assertions/-/acorn-import-assertions-1.8.0.tgz"
  "version" "1.8.0"

"acorn-jsx@^5.3.1":
  "integrity" "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ=="
  "resolved" "https://registry.npmmirror.com/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  "version" "5.3.2"

"acorn-jsx@^5.3.2":
  "integrity" "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ=="
  "resolved" "https://registry.npmmirror.com/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  "version" "5.3.2"

"acorn-walk@^7.1.1":
  "integrity" "sha512-OPdCF6GsMIP+Az+aWfAAOEt2/+iVDKE7oy6lJ098aoe59oAmK76qV6Gw60SbZ8jHuG2wH058GF4pLFbYamYrVA=="
  "resolved" "https://registry.npmmirror.com/acorn-walk/-/acorn-walk-7.2.0.tgz"
  "version" "7.2.0"

"acorn-walk@^8.0.0", "acorn-walk@^8.0.2":
  "integrity" "sha512-k+iyHEuPgSw6SbuDpGQM+06HQUa04DZ3o+F6CSzXMvvI5KMvnaEqXe+YVe555R9nn6GPt404fos4wcgpw12SDA=="
  "resolved" "https://registry.npmmirror.com/acorn-walk/-/acorn-walk-8.2.0.tgz"
  "version" "8.2.0"

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", "acorn@^8", "acorn@^8.0.4", "acorn@^8.0.5", "acorn@^8.2.4", "acorn@^8.5.0", "acorn@^8.7.1", "acorn@^8.8.0":
  "integrity" "sha512-Y9rRfJG5jcKOE0CLisYbojUjIrIEE7AGMzA/Sm4BslANhbS+cDMpgBdcPT91oJ7OuJ9hYJBx59RjbhxVnrF8Xg=="
  "resolved" "https://npm.bangdao-tech.com/acorn/-/acorn-8.11.3.tgz"
  "version" "8.11.3"

"acorn@^7.1.1":
  "integrity" "sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A=="
  "resolved" "https://registry.npmmirror.com/acorn/-/acorn-7.4.1.tgz"
  "version" "7.4.1"

"acorn@^7.4.0":
  "integrity" "sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A=="
  "resolved" "https://registry.npmmirror.com/acorn/-/acorn-7.4.1.tgz"
  "version" "7.4.1"

"add-dom-event-listener@^1.0.2":
  "integrity" "sha512-WCxx1ixHT0GQU9hb0KI/mhgRQhnU+U3GvwY6ZvVjYq8rsihIGoaIOUbY0yMPBxLH5MDtr0kz3fisWGNcbWW7Jw=="
  "resolved" "https://registry.npmmirror.com/add-dom-event-listener/-/add-dom-event-listener-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "object-assign" "4.x"

"address@^1.1.2":
  "integrity" "sha512-4B/qKCfeE/ODUaAUpSwfzazo5x29WD4r3vXiWsB7I2mSDAihwEqKO+g8GELZUQSSAo5e1XTYh3ZVfLyxBc12nA=="
  "resolved" "https://registry.npmmirror.com/address/-/address-1.2.2.tgz"
  "version" "1.2.2"

"adler-32@~1.2.0":
  "integrity" "sha512-/vUqU/UY4MVeFsg+SsK6c+/05RZXIHZMGJA+PX5JyWI0ZRcBpupnRuPLU/NXXoFwMYCPCoxIfElM2eS+DUXCqQ=="
  "resolved" "https://registry.npmmirror.com/adler-32/-/adler-32-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "exit-on-epipe" "~1.0.1"
    "printj" "~1.1.0"

"adler-32@~1.3.0":
  "integrity" "sha512-ynZ4w/nUUv5rrsR8UUGoe1VC9hZj6V5hU9Qw1HlMDJGEJw5S7TfTErWTjMys6M7vr0YWcPqs3qAr4ss0nDfP+A=="
  "resolved" "https://registry.npmmirror.com/adler-32/-/adler-32-1.3.1.tgz"
  "version" "1.3.1"

"agent-base@6":
  "integrity" "sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ=="
  "resolved" "https://registry.npmmirror.com/agent-base/-/agent-base-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "debug" "4"

"aggregate-error@^3.0.0":
  "integrity" "sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA=="
  "resolved" "https://registry.npmmirror.com/aggregate-error/-/aggregate-error-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "clean-stack" "^2.0.0"
    "indent-string" "^4.0.0"

"ajv-formats@^2.1.1":
  "integrity" "sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA=="
  "resolved" "https://registry.npmmirror.com/ajv-formats/-/ajv-formats-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "ajv" "^8.0.0"

"ajv-keywords@^3.5.2":
  "integrity" "sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ=="
  "resolved" "https://registry.npmmirror.com/ajv-keywords/-/ajv-keywords-3.5.2.tgz"
  "version" "3.5.2"

"ajv-keywords@^5.0.0":
  "integrity" "sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw=="
  "resolved" "https://registry.npmmirror.com/ajv-keywords/-/ajv-keywords-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "fast-deep-equal" "^3.1.3"

"ajv@^6.10.0", "ajv@^6.12.4", "ajv@^6.12.5", "ajv@^6.9.1":
  "integrity" "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g=="
  "resolved" "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz"
  "version" "6.12.6"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"ajv@^8.0.0", "ajv@^8.8.0", "ajv@^8.8.2":
  "integrity" "sha512-sRu1kpcO9yLtYxBKvqfTeh9KzZEwO3STyX1HT+4CaDzC6HpTGYhIhPIzj9XuKU7KYDwnaeh5hcOwjy1QuJzBPA=="
  "resolved" "https://registry.npmmirror.com/ajv/-/ajv-8.12.0.tgz"
  "version" "8.12.0"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"
    "uri-js" "^4.2.2"

"ajv@^8.0.1":
  "integrity" "sha512-sRu1kpcO9yLtYxBKvqfTeh9KzZEwO3STyX1HT+4CaDzC6HpTGYhIhPIzj9XuKU7KYDwnaeh5hcOwjy1QuJzBPA=="
  "resolved" "https://registry.npmmirror.com/ajv/-/ajv-8.12.0.tgz"
  "version" "8.12.0"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"
    "uri-js" "^4.2.2"

"ansi-colors@^4.1.1":
  "integrity" "sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw=="
  "resolved" "https://registry.npmmirror.com/ansi-colors/-/ansi-colors-4.1.3.tgz"
  "version" "4.1.3"

"ansi-escapes@^3.0.0":
  "integrity" "sha512-cBhpre4ma+U0T1oM5fXg7Dy1Jw7zzwv7lt/GoCpr+hDQJoYnKVPLL4dCvSEFMmQurOQvSrwT7SL/DAlhBI97RQ=="
  "resolved" "https://registry.npmmirror.com/ansi-escapes/-/ansi-escapes-3.2.0.tgz"
  "version" "3.2.0"

"ansi-escapes@^4.3.0":
  "integrity" "sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ=="
  "resolved" "https://registry.npmmirror.com/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
  "version" "4.3.2"
  dependencies:
    "type-fest" "^0.21.3"

"ansi-html-community@^0.0.8":
  "integrity" "sha512-1APHAyr3+PCamwNw3bXCPp4HFLONZt/yIH0sZp0/469KWNTEy+qN5jQ3GVX6DMZ1UXAi34yVwtTeaG/HpBuuzw=="
  "resolved" "https://registry.npmmirror.com/ansi-html-community/-/ansi-html-community-0.0.8.tgz"
  "version" "0.0.8"

"ansi-regex@^3.0.0":
  "integrity" "sha512-+O9Jct8wf++lXxxFc4hc8LsjaSq0HFzzL7cVsw8pRDIPdjKD2mT4ytDZlLuSBZ4cLKZFXIrMGO7DbQCtMJJMKw=="
  "resolved" "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-3.0.1.tgz"
  "version" "3.0.1"

"ansi-regex@^5.0.1":
  "integrity" "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="
  "resolved" "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz"
  "version" "5.0.1"

"ansi-styles@^3.2.1":
  "integrity" "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA=="
  "resolved" "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.0"

"ansi-styles@^4.0.0", "ansi-styles@^4.1.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"ant-design-vue@>=1.7.0 <=1", "ant-design-vue@1.7.8":
  "integrity" "sha512-F1hmiS9vwbyfuFvlamdW5l9bHKqRlj9wHaGDIE41NZMWXyWy8qL0UFa/+I0Wl8gQWZCqODW5pN6Yfoyn85At3A=="
  "resolved" "https://npm.bangdao-tech.com/ant-design-vue/-/ant-design-vue-1.7.8.tgz"
  "version" "1.7.8"
  dependencies:
    "@ant-design/icons" "^2.1.1"
    "@ant-design/icons-vue" "^2.0.0"
    "@simonwep/pickr" "~1.7.0"
    "add-dom-event-listener" "^1.0.2"
    "array-tree-filter" "^2.1.0"
    "async-validator" "^3.0.3"
    "babel-helper-vue-jsx-merge-props" "^2.0.3"
    "babel-runtime" "6.x"
    "classnames" "^2.2.5"
    "component-classes" "^1.2.6"
    "dom-align" "^1.10.4"
    "dom-closest" "^0.2.0"
    "dom-scroll-into-view" "^2.0.0"
    "enquire.js" "^2.1.6"
    "intersperse" "^1.0.0"
    "is-mobile" "^2.2.1"
    "is-negative-zero" "^2.0.0"
    "ismobilejs" "^1.0.0"
    "json2mq" "^0.2.0"
    "lodash" "^4.17.5"
    "moment" "^2.21.0"
    "mutationobserver-shim" "^0.3.2"
    "node-emoji" "^1.10.0"
    "omit.js" "^1.0.0"
    "raf" "^3.4.0"
    "resize-observer-polyfill" "^1.5.1"
    "shallow-equal" "^1.0.0"
    "shallowequal" "^1.0.2"
    "vue-ref" "^2.0.0"
    "warning" "^4.0.0"

"any-promise@^1.0.0":
  "integrity" "sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A=="
  "resolved" "https://registry.npmmirror.com/any-promise/-/any-promise-1.3.0.tgz"
  "version" "1.3.0"

"anymatch@~3.1.2":
  "integrity" "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw=="
  "resolved" "https://registry.npmmirror.com/anymatch/-/anymatch-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"arch@^2.1.1":
  "integrity" "sha512-Of/R0wqp83cgHozfIYLbBMnej79U/SVGOOyuB3VVFv1NRM/PSFMK12x9KVtiYzJqmnU5WR2qp0Z5rHb7sWGnFQ=="
  "resolved" "https://registry.npmmirror.com/arch/-/arch-2.2.0.tgz"
  "version" "2.2.0"

"argparse@^1.0.7":
  "integrity" "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg=="
  "resolved" "https://registry.npmmirror.com/argparse/-/argparse-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "sprintf-js" "~1.0.2"

"array-flatten@^2.1.2":
  "integrity" "sha512-hNfzcOV8W4NdualtqBFPyVO+54DSJuZGY9qT4pRroB6S9e3iiido2ISIC5h9R2sPJ8H3FHCIiEnsv1lPXO3KtQ=="
  "resolved" "https://registry.npmmirror.com/array-flatten/-/array-flatten-2.1.2.tgz"
  "version" "2.1.2"

"array-flatten@1.1.1":
  "integrity" "sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg=="
  "resolved" "https://registry.npmmirror.com/array-flatten/-/array-flatten-1.1.1.tgz"
  "version" "1.1.1"

"array-tree-filter@^2.1.0":
  "integrity" "sha512-4ROwICNlNw/Hqa9v+rk5h22KjmzB1JGTMVKP2AKJBOCgb0yL0ASf0+YvCcLNNwquOHNX48jkeZIJ3a+oOQqKcw=="
  "resolved" "https://registry.npmmirror.com/array-tree-filter/-/array-tree-filter-2.1.0.tgz"
  "version" "2.1.0"

"array-union@^2.1.0":
  "integrity" "sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw=="
  "resolved" "https://registry.npmmirror.com/array-union/-/array-union-2.1.0.tgz"
  "version" "2.1.0"

"astral-regex@^2.0.0":
  "integrity" "sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ=="
  "resolved" "https://registry.npmmirror.com/astral-regex/-/astral-regex-2.0.0.tgz"
  "version" "2.0.0"

"async-validator@^3.0.3":
  "integrity" "sha512-8eLCg00W9pIRZSB781UUX/H6Oskmm8xloZfr09lz5bikRpBVDlJ3hRVuxxP1SxcwsEYfJ4IU8Q19Y8/893r3rQ=="
  "resolved" "https://registry.npmmirror.com/async-validator/-/async-validator-3.5.2.tgz"
  "version" "3.5.2"

"async@^2.6.4":
  "integrity" "sha512-mzo5dfJYwAn29PeiJ0zvwTo04zj8HDJj0Mn8TD7sno7q12prdbnasKJHhkm2c1LgrhlJ0teaea8860oxi51mGA=="
  "resolved" "https://registry.npmmirror.com/async/-/async-2.6.4.tgz"
  "version" "2.6.4"
  dependencies:
    "lodash" "^4.17.14"

"asynckit@^0.4.0":
  "integrity" "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="
  "resolved" "https://registry.npmmirror.com/asynckit/-/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"at-least-node@^1.0.0":
  "integrity" "sha512-+q/t7Ekv1EDY2l6Gda6LLiX14rU9TV20Wa3ofeQmwPFZbOMo9DXrLbOjFaaclkXKWidIaopwAObQDqwWtGUjqg=="
  "resolved" "https://registry.npmmirror.com/at-least-node/-/at-least-node-1.0.0.tgz"
  "version" "1.0.0"

"autoprefixer@^10.2.4":
  "integrity" "sha512-FQzyfOsTlwVzjHxKEqRIAdJx9niO6VCBCoEwax/VLSoQF29ggECcPuBqUMZ+u8jCZOPSy8b8/8KnuFbp0SaFZQ=="
  "resolved" "https://registry.npmmirror.com/autoprefixer/-/autoprefixer-10.4.14.tgz"
  "version" "10.4.14"
  dependencies:
    "browserslist" "^4.21.5"
    "caniuse-lite" "^1.0.30001464"
    "fraction.js" "^4.2.0"
    "normalize-range" "^0.1.2"
    "picocolors" "^1.0.0"
    "postcss-value-parser" "^4.2.0"

"axios@^1.3.4":
  "integrity" "sha512-toYm+Bsyl6VC5wSkfkbbNB6ROv7KY93PEBBL6xyDczaIHasAiv4wPqQ/c4RjoQzipxRD2W5g21cOqQulZ7rHwQ=="
  "resolved" "https://registry.npmmirror.com/axios/-/axios-1.3.4.tgz"
  "version" "1.3.4"
  dependencies:
    "follow-redirects" "^1.15.0"
    "form-data" "^4.0.0"
    "proxy-from-env" "^1.1.0"

"babel-helper-vue-jsx-merge-props@^2.0.3":
  "integrity" "sha512-gsLiKK7Qrb7zYJNgiXKpXblxbV5ffSwR0f5whkPAaBAR4fhi6bwRZxX9wBlIc5M/v8CCkXUbXZL4N/nSE97cqg=="
  "resolved" "https://registry.npmmirror.com/babel-helper-vue-jsx-merge-props/-/babel-helper-vue-jsx-merge-props-2.0.3.tgz"
  "version" "2.0.3"

"babel-loader@^8.2.2":
  "integrity" "sha512-H8SvsMF+m9t15HNLMipppzkC+Y2Yq+v3SonZyU70RBL/h1gxPkH08Ot8pEE9Z4Kd+czyWJClmFS8qzIP9OZ04Q=="
  "resolved" "https://registry.npmmirror.com/babel-loader/-/babel-loader-8.3.0.tgz"
  "version" "8.3.0"
  dependencies:
    "find-cache-dir" "^3.3.1"
    "loader-utils" "^2.0.0"
    "make-dir" "^3.1.0"
    "schema-utils" "^2.6.5"

"babel-plugin-dynamic-import-node@^2.3.3":
  "integrity" "sha512-jZVI+s9Zg3IqA/kdi0i6UDCybUI3aSBLnglhYbSSjKlV7yF1F/5LWv8MakQmvYpnbJDS6fcBL2KzHSxNCMtWSQ=="
  "resolved" "https://registry.npmmirror.com/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "object.assign" "^4.1.0"

"babel-plugin-polyfill-corejs2@^0.3.3":
  "integrity" "sha512-8hOdmFYFSZhqg2C/JgLUQ+t52o5nirNwaWM2B9LWteozwIvM14VSwdsCAUET10qT+kmySAlseadmfeeSWFCy+Q=="
  "resolved" "https://registry.npmmirror.com/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.3.3.tgz"
  "version" "0.3.3"
  dependencies:
    "@babel/compat-data" "^7.17.7"
    "@babel/helper-define-polyfill-provider" "^0.3.3"
    "semver" "^6.1.1"

"babel-plugin-polyfill-corejs3@^0.6.0":
  "integrity" "sha512-+eHqR6OPcBhJOGgsIar7xoAB1GcSwVUA3XjAd7HJNzOXT4wv6/H7KIdA/Nc60cvUlDbKApmqNvD1B1bzOt4nyA=="
  "resolved" "https://registry.npmmirror.com/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.6.0.tgz"
  "version" "0.6.0"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.3"
    "core-js-compat" "^3.25.1"

"babel-plugin-polyfill-regenerator@^0.4.1":
  "integrity" "sha512-NtQGmyQDXjQqQ+IzRkBVwEOz9lQ4zxAQZgoAYEtU9dJjnl1Oc98qnN7jcp+bE7O7aYzVpavXE3/VKXNzUbh7aw=="
  "resolved" "https://registry.npmmirror.com/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.3"

"babel-runtime@^6.23.0", "babel-runtime@^6.26.0", "babel-runtime@6.x":
  "integrity" "sha512-ITKNuq2wKlW1fJg9sSW52eepoYgZBggvOAHC0u/CYu/qxQ9EVzThCgR69BnSXLHjy2f7SY5zaQ4yt7H9ZVxY2g=="
  "resolved" "https://registry.npmmirror.com/babel-runtime/-/babel-runtime-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "core-js" "^2.4.0"
    "regenerator-runtime" "^0.11.0"

"balanced-match@^1.0.0":
  "integrity" "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw=="
  "resolved" "https://registry.npmmirror.com/balanced-match/-/balanced-match-1.0.2.tgz"
  "version" "1.0.2"

"base64-js@^1.3.1":
  "integrity" "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA=="
  "resolved" "https://registry.npmmirror.com/base64-js/-/base64-js-1.5.1.tgz"
  "version" "1.5.1"

"batch@0.6.1":
  "integrity" "sha512-x+VAiMRL6UPkx+kudNvxTl6hB2XNNCG2r+7wixVfIYwu/2HKRXimwQyaumLjMveWvT2Hkd/cAJw+QBMfJ/EKVw=="
  "resolved" "https://registry.npmmirror.com/batch/-/batch-0.6.1.tgz"
  "version" "0.6.1"

"big.js@^5.2.2":
  "integrity" "sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ=="
  "resolved" "https://registry.npmmirror.com/big.js/-/big.js-5.2.2.tgz"
  "version" "5.2.2"

"binary-extensions@^2.0.0":
  "integrity" "sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA=="
  "resolved" "https://registry.npmmirror.com/binary-extensions/-/binary-extensions-2.2.0.tgz"
  "version" "2.2.0"

"bl@^4.1.0":
  "integrity" "sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w=="
  "resolved" "https://registry.npmmirror.com/bl/-/bl-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "buffer" "^5.5.0"
    "inherits" "^2.0.4"
    "readable-stream" "^3.4.0"

"bluebird@^3.1.1":
  "integrity" "sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg=="
  "resolved" "https://registry.npmmirror.com/bluebird/-/bluebird-3.7.2.tgz"
  "version" "3.7.2"

"body-parser@1.20.1":
  "integrity" "sha512-jWi7abTbYwajOytWCQc37VulmWiRae5RyTpaCyDcS5/lMdtwSz5lOpDE67srw/HYe35f1z3fDQw+3txg7gNtWw=="
  "resolved" "https://registry.npmmirror.com/body-parser/-/body-parser-1.20.1.tgz"
  "version" "1.20.1"
  dependencies:
    "bytes" "3.1.2"
    "content-type" "~1.0.4"
    "debug" "2.6.9"
    "depd" "2.0.0"
    "destroy" "1.2.0"
    "http-errors" "2.0.0"
    "iconv-lite" "0.4.24"
    "on-finished" "2.4.1"
    "qs" "6.11.0"
    "raw-body" "2.5.1"
    "type-is" "~1.6.18"
    "unpipe" "1.0.0"

"bonjour-service@^1.0.11":
  "integrity" "sha512-Z/5lQRMOG9k7W+FkeGTNjh7htqn/2LMnfOvBZ8pynNZCM9MwkQkI3zeI4oz09uWdcgmgHugVvBqxGg4VQJ5PCg=="
  "resolved" "https://registry.npmmirror.com/bonjour-service/-/bonjour-service-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "array-flatten" "^2.1.2"
    "dns-equal" "^1.0.0"
    "fast-deep-equal" "^3.1.3"
    "multicast-dns" "^7.2.5"

"boolbase@^1.0.0":
  "integrity" "sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww=="
  "resolved" "https://registry.npmmirror.com/boolbase/-/boolbase-1.0.0.tgz"
  "version" "1.0.0"

"brace-expansion@^1.1.7":
  "integrity" "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA=="
  "resolved" "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"braces@^3.0.2", "braces@~3.0.2":
  "integrity" "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A=="
  "resolved" "https://registry.npmmirror.com/braces/-/braces-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "fill-range" "^7.0.1"

"browser-process-hrtime@^1.0.0":
  "integrity" "sha512-9o5UecI3GhkpM6DrXr69PblIuWxPKk9Y0jHBRhdocZ2y7YECBFCsHm79Pr3OyR2AvjhDkabFJaDJMYRazHgsow=="
  "resolved" "https://registry.npmmirror.com/browser-process-hrtime/-/browser-process-hrtime-1.0.0.tgz"
  "version" "1.0.0"

"browserslist@^4.0.0", "browserslist@^4.14.5", "browserslist@^4.16.3", "browserslist@^4.21.3", "browserslist@^4.21.4", "browserslist@^4.21.5", "browserslist@>= 4.21.0":
  "integrity" "sha512-tUkiguQGW7S3IhB7N+c2MV/HZPSCPAAiYBZXLsBhFB/PCy6ZKKsZrmBayHV9fdGV/ARIfJ14NkxKzRDjvp7L6w=="
  "resolved" "https://registry.npmmirror.com/browserslist/-/browserslist-4.21.5.tgz"
  "version" "4.21.5"
  dependencies:
    "caniuse-lite" "^1.0.30001449"
    "electron-to-chromium" "^1.4.284"
    "node-releases" "^2.0.8"
    "update-browserslist-db" "^1.0.10"

"buffer-from@^1.0.0":
  "integrity" "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ=="
  "resolved" "https://registry.npmmirror.com/buffer-from/-/buffer-from-1.1.2.tgz"
  "version" "1.1.2"

"buffer@^5.5.0":
  "integrity" "sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ=="
  "resolved" "https://registry.npmmirror.com/buffer/-/buffer-5.7.1.tgz"
  "version" "5.7.1"
  dependencies:
    "base64-js" "^1.3.1"
    "ieee754" "^1.1.13"

"bytes@3.0.0":
  "integrity" "sha512-pMhOfFDPiv9t5jjIXkHosWmkSyQbvsgEVNkz0ERHbuLh2T/7j4Mqqpz523Fe8MVY89KC6Sh/QfS2sM+SjgFDcw=="
  "resolved" "https://registry.npmmirror.com/bytes/-/bytes-3.0.0.tgz"
  "version" "3.0.0"

"bytes@3.1.2":
  "integrity" "sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg=="
  "resolved" "https://registry.npmmirror.com/bytes/-/bytes-3.1.2.tgz"
  "version" "3.1.2"

"call-bind@^1.0.0", "call-bind@^1.0.2":
  "integrity" "sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA=="
  "resolved" "https://registry.npmmirror.com/call-bind/-/call-bind-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "function-bind" "^1.1.1"
    "get-intrinsic" "^1.0.2"

"callsites@^3.0.0":
  "integrity" "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ=="
  "resolved" "https://registry.npmmirror.com/callsites/-/callsites-3.1.0.tgz"
  "version" "3.1.0"

"camel-case@^4.1.2":
  "integrity" "sha512-gxGWBrTT1JuMx6R+o5PTXMmUnhnVzLQ9SNutD4YqKtI6ap897t3tKECYla6gCWEkplXnlNybEkZg9GEGxKFCgw=="
  "resolved" "https://registry.npmmirror.com/camel-case/-/camel-case-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "pascal-case" "^3.1.2"
    "tslib" "^2.0.3"

"camelcase@^5.0.0":
  "integrity" "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg=="
  "resolved" "https://registry.npmmirror.com/camelcase/-/camelcase-5.3.1.tgz"
  "version" "5.3.1"

"camelcase@^6.0.0":
  "integrity" "sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA=="
  "resolved" "https://registry.npmmirror.com/camelcase/-/camelcase-6.3.0.tgz"
  "version" "6.3.0"

"caniuse-api@^3.0.0":
  "integrity" "sha512-bsTwuIg/BZZK/vreVTYYbSWoe2F+71P7K5QGEX+pT250DZbfU1MQ5prOKpPR+LL6uWKK3KMwMCAS74QB3Um1uw=="
  "resolved" "https://registry.npmmirror.com/caniuse-api/-/caniuse-api-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-lite" "^1.0.0"
    "lodash.memoize" "^4.1.2"
    "lodash.uniq" "^4.5.0"

"caniuse-lite@^1.0.0", "caniuse-lite@^1.0.30001449", "caniuse-lite@^1.0.30001464":
  "integrity" "sha512-xWC/0+hHHQgj3/vrKYY0AAzeIUgr7L9wlELIcAvZdDUHlhL/kNxMdnQLOSOQfP8R51ZzPhmHdyMkI0MMpmxCfg=="
  "resolved" "https://registry.npmmirror.com/caniuse-lite/-/caniuse-lite-1.0.30001472.tgz"
  "version" "1.0.30001472"

"case-sensitive-paths-webpack-plugin@^2.3.0":
  "integrity" "sha512-roIFONhcxog0JSSWbvVAh3OocukmSgpqOH6YpMkCvav/ySIV3JKg4Dc8vYtQjYi/UxpNE36r/9v+VqTQqgkYmw=="
  "resolved" "https://registry.npmmirror.com/case-sensitive-paths-webpack-plugin/-/case-sensitive-paths-webpack-plugin-2.4.0.tgz"
  "version" "2.4.0"

"cfb@^1.1.4", "cfb@^1.2.0":
  "integrity" "sha512-KfdUZsSOw19/ObEWasvBP/Ac4reZvAGauZhs6S/gqNhXhI7cKwvlH7ulj+dOEYnca4bm4SGo8C1bTAQvnTjgQA=="
  "resolved" "https://registry.npmmirror.com/cfb/-/cfb-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "adler-32" "~1.3.0"
    "crc-32" "~1.2.0"

"chalk@^2.0.0":
  "integrity" "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ=="
  "resolved" "https://registry.npmmirror.com/chalk/-/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^2.1.0":
  "integrity" "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ=="
  "resolved" "https://registry.npmmirror.com/chalk/-/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^3.0.0":
  "integrity" "sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg=="
  "resolved" "https://registry.npmmirror.com/chalk/-/chalk-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chalk@^4.0.0", "chalk@^4.1.0", "chalk@^4.1.2":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chokidar@^3.5.3", "chokidar@>=3.0.0 <4.0.0":
  "integrity" "sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw=="
  "resolved" "https://registry.npmmirror.com/chokidar/-/chokidar-3.5.3.tgz"
  "version" "3.5.3"
  dependencies:
    "anymatch" "~3.1.2"
    "braces" "~3.0.2"
    "glob-parent" "~5.1.2"
    "is-binary-path" "~2.1.0"
    "is-glob" "~4.0.1"
    "normalize-path" "~3.0.0"
    "readdirp" "~3.6.0"
  optionalDependencies:
    "fsevents" "~2.3.2"

"chrome-trace-event@^1.0.2":
  "integrity" "sha512-p3KULyQg4S7NIHixdwbGX+nFHkoBiA4YQmyWtjb8XngSKV124nJmRysgAeujbUVb15vh+RvFUfCPqU7rXk+hZg=="
  "resolved" "https://registry.npmmirror.com/chrome-trace-event/-/chrome-trace-event-1.0.3.tgz"
  "version" "1.0.3"

"ci-info@^1.5.0":
  "integrity" "sha512-vsGdkwSCDpWmP80ncATX7iea5DWQemg1UgCW5J8tqjU3lYw4FBYuj89J0CTVomA7BEfvSZd84GmHko+MxFQU2A=="
  "resolved" "https://registry.npmmirror.com/ci-info/-/ci-info-1.6.0.tgz"
  "version" "1.6.0"

"classnames@^2.2.5":
  "integrity" "sha512-CSbhY4cFEJRe6/GQzIk5qXZ4Jeg5pcsP7b5peFSDpffpe1cqjASH/n9UTjBwOp6XpMSTwQ8Za2K5V02ueA7Tmw=="
  "resolved" "https://registry.npmmirror.com/classnames/-/classnames-2.3.2.tgz"
  "version" "2.3.2"

"clean-css@^5.2.2":
  "integrity" "sha512-JVJbM+f3d3Q704rF4bqQ5UUyTtuJ0JRKNbTKVEeujCCBoMdkEi+V+e8oktO9qGQNSvHrFTM6JZRXrUvGR1czww=="
  "resolved" "https://registry.npmmirror.com/clean-css/-/clean-css-5.3.2.tgz"
  "version" "5.3.2"
  dependencies:
    "source-map" "~0.6.0"

"clean-stack@^2.0.0":
  "integrity" "sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A=="
  "resolved" "https://registry.npmmirror.com/clean-stack/-/clean-stack-2.2.0.tgz"
  "version" "2.2.0"

"cli-cursor@^2.0.0":
  "integrity" "sha512-8lgKz8LmCRYZZQDpRyT2m5rKJ08TnU4tR9FFFW2rxpxR1FzWi4PQ/NfyODchAatHaUgnSPVcx/R5w6NuTBzFiw=="
  "resolved" "https://registry.npmmirror.com/cli-cursor/-/cli-cursor-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "restore-cursor" "^2.0.0"

"cli-cursor@^3.1.0":
  "integrity" "sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw=="
  "resolved" "https://registry.npmmirror.com/cli-cursor/-/cli-cursor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "restore-cursor" "^3.1.0"

"cli-highlight@^2.1.10":
  "integrity" "sha512-9KDcoEVwyUXrjcJNvHD0NFc/hiwe/WPVYIleQh2O1N2Zro5gWJZ/K+3DGn8w8P/F6FxOgzyC5bxDyHIgCSPhGg=="
  "resolved" "https://registry.npmmirror.com/cli-highlight/-/cli-highlight-2.1.11.tgz"
  "version" "2.1.11"
  dependencies:
    "chalk" "^4.0.0"
    "highlight.js" "^10.7.1"
    "mz" "^2.4.0"
    "parse5" "^5.1.1"
    "parse5-htmlparser2-tree-adapter" "^6.0.0"
    "yargs" "^16.0.0"

"cli-spinners@^2.5.0":
  "integrity" "sha512-qu3pN8Y3qHNgE2AFweciB1IfMnmZ/fsNTEE+NOFjmGB2F/7rLhnhzppvpCnN4FovtP26k8lHyy9ptEbNwWFLzw=="
  "resolved" "https://registry.npmmirror.com/cli-spinners/-/cli-spinners-2.7.0.tgz"
  "version" "2.7.0"

"cli-truncate@^2.1.0", "cli-truncate@2.1.0":
  "integrity" "sha512-n8fOixwDD6b/ObinzTrp1ZKFzbgvKZvuz/TvejnLn1aQfC6r52XEx85FmuC+3HI+JM7coBRXUvNqEU2PHVrHpg=="
  "resolved" "https://registry.npmmirror.com/cli-truncate/-/cli-truncate-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "slice-ansi" "^3.0.0"
    "string-width" "^4.2.0"

"clipboardy@^2.3.0":
  "integrity" "sha512-mKhiIL2DrQIsuXMgBgnfEHOZOryC7kY7YO//TN6c63wlEm3NG5tz+YgY5rVi29KCmq/QQjKYvM7a19+MDOTHOQ=="
  "resolved" "https://registry.npmmirror.com/clipboardy/-/clipboardy-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "arch" "^2.1.1"
    "execa" "^1.0.0"
    "is-wsl" "^2.1.1"

"cliui@^7.0.2", "cliui@^7.0.4":
  "integrity" "sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ=="
  "resolved" "https://registry.npmmirror.com/cliui/-/cliui-7.0.4.tgz"
  "version" "7.0.4"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^7.0.0"

"clone-deep@^4.0.1":
  "integrity" "sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ=="
  "resolved" "https://registry.npmmirror.com/clone-deep/-/clone-deep-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"
    "kind-of" "^6.0.2"
    "shallow-clone" "^3.0.0"

"clone@^1.0.2":
  "integrity" "sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg=="
  "resolved" "https://registry.npmmirror.com/clone/-/clone-1.0.4.tgz"
  "version" "1.0.4"

"clone@^2.1.1", "clone@^2.1.2":
  "integrity" "sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w=="
  "resolved" "https://registry.npmmirror.com/clone/-/clone-2.1.2.tgz"
  "version" "2.1.2"

"codepage@~1.15.0":
  "integrity" "sha512-3g6NUTPd/YtuuGrhMnOMRjFc+LJw/bnMp3+0r/Wcz3IXUuCosKRJvMphm5+Q+bvTVGcJJuRvVLuYba+WojaFaA=="
  "resolved" "https://registry.npmmirror.com/codepage/-/codepage-1.15.0.tgz"
  "version" "1.15.0"

"color-convert@^1.9.0":
  "integrity" "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg=="
  "resolved" "https://registry.npmmirror.com/color-convert/-/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-convert@^2.0.1":
  "integrity" "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ=="
  "resolved" "https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "color-name" "~1.1.4"

"color-name@~1.1.4":
  "integrity" "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="
  "resolved" "https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz"
  "version" "1.1.4"

"color-name@1.1.3":
  "integrity" "sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw=="
  "resolved" "https://registry.npmmirror.com/color-name/-/color-name-1.1.3.tgz"
  "version" "1.1.3"

"colord@^2.9.1":
  "integrity" "sha512-jeC1axXpnb0/2nn/Y1LPuLdgXBLH7aDcHu4KEKfqw3CUhX7ZpfBSlPKyqXE6btIgEzfWtrX3/tyBCaCvXvMkOw=="
  "resolved" "https://registry.npmmirror.com/colord/-/colord-2.9.3.tgz"
  "version" "2.9.3"

"colorette@^1.4.0":
  "integrity" "sha512-Y2oEozpomLn7Q3HFP7dpww7AtMJplbM9lGZP6RDfHqmbeRjiwRg4n6VM6j4KLmRke85uWEI7JqF17f3pqdRA0g=="
  "resolved" "https://registry.npmmirror.com/colorette/-/colorette-1.4.0.tgz"
  "version" "1.4.0"

"colorette@^2.0.10", "colorette@^2.0.16":
  "integrity" "sha512-3tlv/dIP7FWvj3BsbHrGLJ6l/oKh1O3TcgBqMn+yyCagOxc23fyzDS6HypQbgxWbkpDnf52p1LuR4eWDQ/K9WQ=="
  "resolved" "https://registry.npmmirror.com/colorette/-/colorette-2.0.19.tgz"
  "version" "2.0.19"

"combined-stream@^1.0.8":
  "integrity" "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg=="
  "resolved" "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "delayed-stream" "~1.0.0"

"commander@^2.20.0":
  "integrity" "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="
  "resolved" "https://registry.npmmirror.com/commander/-/commander-2.20.3.tgz"
  "version" "2.20.3"

"commander@^7.2.0":
  "integrity" "sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw=="
  "resolved" "https://registry.npmmirror.com/commander/-/commander-7.2.0.tgz"
  "version" "7.2.0"

"commander@^8.2.0":
  "integrity" "sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww=="
  "resolved" "https://registry.npmmirror.com/commander/-/commander-8.3.0.tgz"
  "version" "8.3.0"

"commander@^8.3.0":
  "integrity" "sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww=="
  "resolved" "https://registry.npmmirror.com/commander/-/commander-8.3.0.tgz"
  "version" "8.3.0"

"commander@~2.17.1":
  "integrity" "sha512-wPMUt6FnH2yzG95SA6mzjQOEKUU3aLaDEmzs1ti+1E9h+CsrZghRlqEM/EJ4KscsQVG8uNN4uVreUeT8+drlgg=="
  "resolved" "https://registry.npmmirror.com/commander/-/commander-2.17.1.tgz"
  "version" "2.17.1"

"commondir@^1.0.1":
  "integrity" "sha512-W9pAhw0ja1Edb5GVdIF1mjZw/ASI0AlShXM83UUGe2DVr5TdAPEA1OA8m/g8zWp9x6On7gqufY+FatDbC3MDQg=="
  "resolved" "https://registry.npmmirror.com/commondir/-/commondir-1.0.1.tgz"
  "version" "1.0.1"

"component-classes@^1.2.6":
  "integrity" "sha512-hPFGULxdwugu1QWW3SvVOCUHLzO34+a2J6Wqy0c5ASQkfi9/8nZcBB0ZohaEbXOQlCflMAEMmEWk7u7BVs4koA=="
  "resolved" "https://registry.npmmirror.com/component-classes/-/component-classes-1.2.6.tgz"
  "version" "1.2.6"
  dependencies:
    "component-indexof" "0.0.3"

"component-indexof@0.0.3":
  "integrity" "sha512-puDQKvx/64HZXb4hBwIcvQLaLgux8o1CbWl39s41hrIIZDl1lJiD5jc22gj3RBeGK0ovxALDYpIbyjqDUUl0rw=="
  "resolved" "https://registry.npmmirror.com/component-indexof/-/component-indexof-0.0.3.tgz"
  "version" "0.0.3"

"compressible@~2.0.16":
  "integrity" "sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg=="
  "resolved" "https://registry.npmmirror.com/compressible/-/compressible-2.0.18.tgz"
  "version" "2.0.18"
  dependencies:
    "mime-db" ">= 1.43.0 < 2"

"compression@^1.7.4":
  "integrity" "sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ=="
  "resolved" "https://registry.npmmirror.com/compression/-/compression-1.7.4.tgz"
  "version" "1.7.4"
  dependencies:
    "accepts" "~1.3.5"
    "bytes" "3.0.0"
    "compressible" "~2.0.16"
    "debug" "2.6.9"
    "on-headers" "~1.0.2"
    "safe-buffer" "5.1.2"
    "vary" "~1.1.2"

"concat-map@0.0.1":
  "integrity" "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg=="
  "resolved" "https://registry.npmmirror.com/concat-map/-/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"connect-history-api-fallback@^2.0.0":
  "integrity" "sha512-U73+6lQFmfiNPrYbXqr6kZ1i1wiRqXnp2nhMsINseWXO8lDau0LGEffJ8kQi4EjLZympVgRdvqjAgiZ1tgzDDA=="
  "resolved" "https://registry.npmmirror.com/connect-history-api-fallback/-/connect-history-api-fallback-2.0.0.tgz"
  "version" "2.0.0"

"consolidate@^0.15.1":
  "integrity" "sha512-DW46nrsMJgy9kqAbPt5rKaCr7uFtpo4mSUvLHIUbJEjm0vo+aY5QLwBUq3FK4tRnJr/X0Psc0C4jf/h+HtXSMw=="
  "resolved" "https://registry.npmmirror.com/consolidate/-/consolidate-0.15.1.tgz"
  "version" "0.15.1"
  dependencies:
    "bluebird" "^3.1.1"

"content-disposition@0.5.4":
  "integrity" "sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ=="
  "resolved" "https://registry.npmmirror.com/content-disposition/-/content-disposition-0.5.4.tgz"
  "version" "0.5.4"
  dependencies:
    "safe-buffer" "5.2.1"

"content-type@~1.0.4":
  "integrity" "sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA=="
  "resolved" "https://registry.npmmirror.com/content-type/-/content-type-1.0.5.tgz"
  "version" "1.0.5"

"convert-source-map@^1.7.0":
  "integrity" "sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A=="
  "resolved" "https://registry.npmmirror.com/convert-source-map/-/convert-source-map-1.9.0.tgz"
  "version" "1.9.0"

"cookie-signature@1.0.6":
  "integrity" "sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ=="
  "resolved" "https://registry.npmmirror.com/cookie-signature/-/cookie-signature-1.0.6.tgz"
  "version" "1.0.6"

"cookie@0.5.0":
  "integrity" "sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw=="
  "resolved" "https://registry.npmmirror.com/cookie/-/cookie-0.5.0.tgz"
  "version" "0.5.0"

"copy-anything@^2.0.1":
  "integrity" "sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw=="
  "resolved" "https://registry.npmmirror.com/copy-anything/-/copy-anything-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "is-what" "^3.14.1"

"copy-webpack-plugin@^9.0.1":
  "integrity" "sha512-rxnR7PaGigJzhqETHGmAcxKnLZSR5u1Y3/bcIv/1FnqXedcL/E2ewK7ZCNrArJKCiSv8yVXhTqetJh8inDvfsA=="
  "resolved" "https://registry.npmmirror.com/copy-webpack-plugin/-/copy-webpack-plugin-9.1.0.tgz"
  "version" "9.1.0"
  dependencies:
    "fast-glob" "^3.2.7"
    "glob-parent" "^6.0.1"
    "globby" "^11.0.3"
    "normalize-path" "^3.0.0"
    "schema-utils" "^3.1.1"
    "serialize-javascript" "^6.0.0"

"core-js-compat@^3.25.1", "core-js-compat@^3.8.3":
  "integrity" "sha512-QmchCua884D8wWskMX8tW5ydINzd8oSJVx38lx/pVkFGqztxt73GYre3pm/hyYq8bPf+MW5In4I/uRShFDsbrA=="
  "resolved" "https://registry.npmmirror.com/core-js-compat/-/core-js-compat-3.29.1.tgz"
  "version" "3.29.1"
  dependencies:
    "browserslist" "^4.21.5"

"core-js@^2.4.0":
  "integrity" "sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ=="
  "resolved" "https://registry.npmmirror.com/core-js/-/core-js-2.6.12.tgz"
  "version" "2.6.12"

"core-js@^3.6.5", "core-js@^3.8.3", "core-js@3.29.1":
  "integrity" "sha512-+jwgnhg6cQxKYIIjGtAHq2nwUOolo9eoFZ4sHfUH09BLXBgxnH4gA0zEd+t+BO2cNB8idaBtZFcFTRjQJRJmAw=="
  "resolved" "https://registry.npmmirror.com/core-js/-/core-js-3.29.1.tgz"
  "version" "3.29.1"

"core-util-is@~1.0.0":
  "integrity" "sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ=="
  "resolved" "https://registry.npmmirror.com/core-util-is/-/core-util-is-1.0.3.tgz"
  "version" "1.0.3"

"cosmiconfig@^7.0.0", "cosmiconfig@^7.0.1":
  "integrity" "sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA=="
  "resolved" "https://registry.npmmirror.com/cosmiconfig/-/cosmiconfig-7.1.0.tgz"
  "version" "7.1.0"
  dependencies:
    "@types/parse-json" "^4.0.0"
    "import-fresh" "^3.2.1"
    "parse-json" "^5.0.0"
    "path-type" "^4.0.0"
    "yaml" "^1.10.0"

"crc-32@~1.2.0":
  "integrity" "sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ=="
  "resolved" "https://registry.npmmirror.com/crc-32/-/crc-32-1.2.2.tgz"
  "version" "1.2.2"

"cross-spawn@^5.0.1":
  "integrity" "sha512-pTgQJ5KC0d2hcY8eyL1IzlBPYjTkyH72XRZPnLyKus2mBfNjQs3klqbJU2VILqZryAZUt9JOb3h/mWMy23/f5A=="
  "resolved" "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "lru-cache" "^4.0.1"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^6.0.0":
  "integrity" "sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ=="
  "resolved" "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-6.0.5.tgz"
  "version" "6.0.5"
  dependencies:
    "nice-try" "^1.0.4"
    "path-key" "^2.0.1"
    "semver" "^5.5.0"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^7.0.2", "cross-spawn@^7.0.3":
  "integrity" "sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w=="
  "resolved" "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.3.tgz"
  "version" "7.0.3"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"crypto-js@4.1.1":
  "integrity" "sha512-o2JlM7ydqd3Qk9CA0L4NL6mTzU2sdx96a+oOfPu8Mkl/PK51vSyoi8/rQ8NknZtk44vq15lmhAj9CIAGwgeWKw=="
  "resolved" "https://registry.npmmirror.com/crypto-js/-/crypto-js-4.1.1.tgz"
  "version" "4.1.1"

"css-declaration-sorter@^6.3.1":
  "integrity" "sha512-jDfsatwWMWN0MODAFuHszfjphEXfNw9JUAhmY4pLu3TyTU+ohUpsbVtbU+1MZn4a47D9kqh03i4eyOm+74+zew=="
  "resolved" "https://registry.npmmirror.com/css-declaration-sorter/-/css-declaration-sorter-6.4.0.tgz"
  "version" "6.4.0"

"css-loader@*", "css-loader@^6.5.0":
  "integrity" "sha512-qhOH1KlBMnZP8FzRO6YCH9UHXQhVMcEGLyNdb7Hv2cpcmJbW0YrddO+tG1ab5nT41KpHIYGsbeHqxB9xPu1pKQ=="
  "resolved" "https://registry.npmmirror.com/css-loader/-/css-loader-6.7.3.tgz"
  "version" "6.7.3"
  dependencies:
    "icss-utils" "^5.1.0"
    "postcss" "^8.4.19"
    "postcss-modules-extract-imports" "^3.0.0"
    "postcss-modules-local-by-default" "^4.0.0"
    "postcss-modules-scope" "^3.0.0"
    "postcss-modules-values" "^4.0.0"
    "postcss-value-parser" "^4.2.0"
    "semver" "^7.3.8"

"css-minimizer-webpack-plugin@^3.0.2":
  "integrity" "sha512-1u6D71zeIfgngN2XNRJefc/hY7Ybsxd74Jm4qngIXyUEk7fss3VUzuHxLAq/R8NAba4QU9OUSaMZlbpRc7bM4Q=="
  "resolved" "https://registry.npmmirror.com/css-minimizer-webpack-plugin/-/css-minimizer-webpack-plugin-3.4.1.tgz"
  "version" "3.4.1"
  dependencies:
    "cssnano" "^5.0.6"
    "jest-worker" "^27.0.2"
    "postcss" "^8.3.5"
    "schema-utils" "^4.0.0"
    "serialize-javascript" "^6.0.0"
    "source-map" "^0.6.1"

"css-select@^4.1.3":
  "integrity" "sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ=="
  "resolved" "https://registry.npmmirror.com/css-select/-/css-select-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "boolbase" "^1.0.0"
    "css-what" "^6.0.1"
    "domhandler" "^4.3.1"
    "domutils" "^2.8.0"
    "nth-check" "^2.0.1"

"css-tree@^1.1.2", "css-tree@^1.1.3":
  "integrity" "sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q=="
  "resolved" "https://registry.npmmirror.com/css-tree/-/css-tree-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "mdn-data" "2.0.14"
    "source-map" "^0.6.1"

"css-what@^6.0.1":
  "integrity" "sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw=="
  "resolved" "https://registry.npmmirror.com/css-what/-/css-what-6.1.0.tgz"
  "version" "6.1.0"

"cssesc@^3.0.0":
  "integrity" "sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg=="
  "resolved" "https://registry.npmmirror.com/cssesc/-/cssesc-3.0.0.tgz"
  "version" "3.0.0"

"cssnano-preset-default@^5.2.14":
  "integrity" "sha512-t0SFesj/ZV2OTylqQVOrFgEh5uanxbO6ZAdeCrNsUQ6fVuXwYTxJPNAGvGTxHbD68ldIJNec7PyYZDBrfDQ+6A=="
  "resolved" "https://registry.npmmirror.com/cssnano-preset-default/-/cssnano-preset-default-5.2.14.tgz"
  "version" "5.2.14"
  dependencies:
    "css-declaration-sorter" "^6.3.1"
    "cssnano-utils" "^3.1.0"
    "postcss-calc" "^8.2.3"
    "postcss-colormin" "^5.3.1"
    "postcss-convert-values" "^5.1.3"
    "postcss-discard-comments" "^5.1.2"
    "postcss-discard-duplicates" "^5.1.0"
    "postcss-discard-empty" "^5.1.1"
    "postcss-discard-overridden" "^5.1.0"
    "postcss-merge-longhand" "^5.1.7"
    "postcss-merge-rules" "^5.1.4"
    "postcss-minify-font-values" "^5.1.0"
    "postcss-minify-gradients" "^5.1.1"
    "postcss-minify-params" "^5.1.4"
    "postcss-minify-selectors" "^5.2.1"
    "postcss-normalize-charset" "^5.1.0"
    "postcss-normalize-display-values" "^5.1.0"
    "postcss-normalize-positions" "^5.1.1"
    "postcss-normalize-repeat-style" "^5.1.1"
    "postcss-normalize-string" "^5.1.0"
    "postcss-normalize-timing-functions" "^5.1.0"
    "postcss-normalize-unicode" "^5.1.1"
    "postcss-normalize-url" "^5.1.0"
    "postcss-normalize-whitespace" "^5.1.1"
    "postcss-ordered-values" "^5.1.3"
    "postcss-reduce-initial" "^5.1.2"
    "postcss-reduce-transforms" "^5.1.0"
    "postcss-svgo" "^5.1.0"
    "postcss-unique-selectors" "^5.1.1"

"cssnano-utils@^3.1.0":
  "integrity" "sha512-JQNR19/YZhz4psLX/rQ9M83e3z2Wf/HdJbryzte4a3NSuafyp9w/I4U+hx5C2S9g41qlstH7DEWnZaaj83OuEA=="
  "resolved" "https://registry.npmmirror.com/cssnano-utils/-/cssnano-utils-3.1.0.tgz"
  "version" "3.1.0"

"cssnano@^5.0.0", "cssnano@^5.0.6":
  "integrity" "sha512-j+BKgDcLDQA+eDifLx0EO4XSA56b7uut3BQFH+wbSaSTuGLuiyTa/wbRYthUXX8LC9mLg+WWKe8h+qJuwTAbHw=="
  "resolved" "https://registry.npmmirror.com/cssnano/-/cssnano-5.1.15.tgz"
  "version" "5.1.15"
  dependencies:
    "cssnano-preset-default" "^5.2.14"
    "lilconfig" "^2.0.3"
    "yaml" "^1.10.2"

"csso@^4.2.0":
  "integrity" "sha512-wvlcdIbf6pwKEk7vHj8/Bkc0B4ylXZruLvOgs9doS5eOsOpuodOV2zJChSpkp+pRpYQLQMeF04nr3Z68Sta9jA=="
  "resolved" "https://registry.npmmirror.com/csso/-/csso-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "css-tree" "^1.1.2"

"cssom@^0.4.4":
  "integrity" "sha512-p3pvU7r1MyyqbTk+WbNJIgJjG2VmTIaB10rI93LzVPrmDJKkzKYMtxxyAvQXR/NS6otuzveI7+7BBq3SjBS2mw=="
  "resolved" "https://registry.npmmirror.com/cssom/-/cssom-0.4.4.tgz"
  "version" "0.4.4"

"cssom@~0.3.6":
  "integrity" "sha512-b0tGHbfegbhPJpxpiBPU2sCkigAqtM9O121le6bbOlgyV+NyGyCmVfJ6QW9eRjz8CpNfWEOYBIMIGRYkLwsIYg=="
  "resolved" "https://registry.npmmirror.com/cssom/-/cssom-0.3.8.tgz"
  "version" "0.3.8"

"cssstyle@^2.3.0":
  "integrity" "sha512-AZL67abkUzIuvcHqk7c09cezpGNcxUxU4Ioi/05xHk4DQeTkWmGYftIE6ctU6AEt+Gn4n1lDStOtj7FKycP71A=="
  "resolved" "https://registry.npmmirror.com/cssstyle/-/cssstyle-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "cssom" "~0.3.6"

"csstype@^3.1.0":
  "integrity" "sha512-DJR/VvkAvSZW9bTouZue2sSxDwdTN92uHjqeKVm+0dAqdfNykRzQ95tay8aXMBAAPpUiq4Qcug2L7neoRh2Egw=="
  "resolved" "https://registry.npmmirror.com/csstype/-/csstype-3.1.1.tgz"
  "version" "3.1.1"

"data-urls@^2.0.0":
  "integrity" "sha512-X5eWTSXO/BJmpdIKCRuKUgSCgAN0OwliVK3yPKbwIWU1Tdw5BRajxlzMidvh+gwko9AfQ9zIj52pzF91Q3YAvQ=="
  "resolved" "https://registry.npmmirror.com/data-urls/-/data-urls-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "abab" "^2.0.3"
    "whatwg-mimetype" "^2.3.0"
    "whatwg-url" "^8.0.0"

"de-indent@^1.0.2":
  "integrity" "sha512-e/1zu3xH5MQryN2zdVaF0OrdNLUbvWxzMbi+iNA6Bky7l1RoP8a2fIbRocyHclXt/arDrrR6lL3TqFD9pMQTsg=="
  "resolved" "https://npm.bangdao-tech.com/de-indent/-/de-indent-1.0.2.tgz"
  "version" "1.0.2"

"debug@^3.2.7":
  "integrity" "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ=="
  "resolved" "https://registry.npmmirror.com/debug/-/debug-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "ms" "^2.1.1"

"debug@^4.0.1", "debug@^4.1.0", "debug@^4.1.1", "debug@^4.3.2", "debug@4":
  "integrity" "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ=="
  "resolved" "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "ms" "2.1.2"

"debug@2.6.9":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "https://registry.npmmirror.com/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"decimal.js@^10.2.1":
  "integrity" "sha512-VBBaLc1MgL5XpzgIP7ny5Z6Nx3UrRkIViUkPUdtl9aya5amy3De1gsUUSB1g3+3sExYNjCAsAznmukyxCb1GRA=="
  "resolved" "https://registry.npmmirror.com/decimal.js/-/decimal.js-10.4.3.tgz"
  "version" "10.4.3"

"deep-equal@^1.0.1":
  "integrity" "sha512-yd9c5AdiqVcR+JjcwUQb9DkhJc8ngNr0MahEBGvDiJw8puWab2yZlh+nkasOnZP+EGTAP6rRp2JzJhJZzvNF8g=="
  "resolved" "https://registry.npmjs.org/deep-equal/-/deep-equal-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "is-arguments" "^1.0.4"
    "is-date-object" "^1.0.1"
    "is-regex" "^1.0.4"
    "object-is" "^1.0.1"
    "object-keys" "^1.1.1"
    "regexp.prototype.flags" "^1.2.0"

"deep-is@^0.1.3", "deep-is@~0.1.3":
  "integrity" "sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ=="
  "resolved" "https://registry.npmmirror.com/deep-is/-/deep-is-0.1.4.tgz"
  "version" "0.1.4"

"deepmerge@^1.5.2":
  "integrity" "sha512-95k0GDqvBjZavkuvzx/YqVLv/6YYa17fz6ILMSf7neqQITCPbnfEnQvEgMPNjH4kgobe7+WIL0yJEHku+H3qtQ=="
  "resolved" "https://registry.npmmirror.com/deepmerge/-/deepmerge-1.5.2.tgz"
  "version" "1.5.2"

"default-gateway@^6.0.3":
  "integrity" "sha512-fwSOJsbbNzZ/CUFpqFBqYfYNLj1NbMPm8MMCIzHjC83iSJRBEGmDUxU+WP661BaBQImeC2yHwXtz+P/O9o+XEg=="
  "resolved" "https://registry.npmmirror.com/default-gateway/-/default-gateway-6.0.3.tgz"
  "version" "6.0.3"
  dependencies:
    "execa" "^5.0.0"

"defaults@^1.0.3":
  "integrity" "sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A=="
  "resolved" "https://registry.npmmirror.com/defaults/-/defaults-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "clone" "^1.0.2"

"define-lazy-prop@^2.0.0":
  "integrity" "sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og=="
  "resolved" "https://registry.npmmirror.com/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz"
  "version" "2.0.0"

"define-properties@^1.1.3", "define-properties@^1.1.4", "define-properties@^1.2.0":
  "integrity" "sha512-xvqAVKGfT1+UAvPwKTVw/njhdQ8ZhXK4lI0bCIuCMrp2up9nPnaDftrLtmpTazqd1o+UY4zgzU+avtMbDP+ldA=="
  "resolved" "https://registry.npmmirror.com/define-properties/-/define-properties-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "has-property-descriptors" "^1.0.0"
    "object-keys" "^1.1.1"

"delayed-stream@~1.0.0":
  "integrity" "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ=="
  "resolved" "https://registry.npmmirror.com/delayed-stream/-/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"depd@~1.1.2":
  "integrity" "sha512-7emPTl6Dpo6JRXOXjLRxck+FlLRX5847cLKEn00PLAgc3g2hTZZgr+e4c2v6QpSmLeFP3n5yUo7ft6avBK/5jQ=="
  "resolved" "https://registry.npmmirror.com/depd/-/depd-1.1.2.tgz"
  "version" "1.1.2"

"depd@2.0.0":
  "integrity" "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw=="
  "resolved" "https://registry.npmmirror.com/depd/-/depd-2.0.0.tgz"
  "version" "2.0.0"

"destroy@1.2.0":
  "integrity" "sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg=="
  "resolved" "https://registry.npmmirror.com/destroy/-/destroy-1.2.0.tgz"
  "version" "1.2.0"

"detect-node@^2.0.4":
  "integrity" "sha512-T0NIuQpnTvFDATNuHN5roPwSBG83rFsuO+MXXH9/3N1eFbn4wcPjttvjMLEPWJ0RGUYgQE7cGgS3tNxbqCGM7g=="
  "resolved" "https://registry.npmmirror.com/detect-node/-/detect-node-2.1.0.tgz"
  "version" "2.1.0"

"dir-glob@^3.0.1":
  "integrity" "sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA=="
  "resolved" "https://registry.npmmirror.com/dir-glob/-/dir-glob-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "path-type" "^4.0.0"

"dns-equal@^1.0.0":
  "integrity" "sha512-z+paD6YUQsk+AbGCEM4PrOXSss5gd66QfcVBFTKR/HpFL9jCqikS94HYwKww6fQyO7IxrIIyUu+g0Ka9tUS2Cg=="
  "resolved" "https://registry.npmmirror.com/dns-equal/-/dns-equal-1.0.0.tgz"
  "version" "1.0.0"

"dns-packet@^5.2.2":
  "integrity" "sha512-USawdAUzRkV6xrqTjiAEp6M9YagZEzWcSUaZTcIFAiyQWW1SoI6KyId8y2+/71wbgHKQAKd+iupLv4YvEwYWvA=="
  "resolved" "https://registry.npmmirror.com/dns-packet/-/dns-packet-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@leichtgewicht/ip-codec" "^2.0.1"

"doctrine@^3.0.0":
  "integrity" "sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w=="
  "resolved" "https://registry.npmmirror.com/doctrine/-/doctrine-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "esutils" "^2.0.2"

"dom-align@^1.10.4":
  "integrity" "sha512-R8LUSEay/68zE5c8/3BDxiTEvgb4xZTF0RKmAHfiEVN3klfIpXfi2/QCoiWPccVQ0J/ZGdz9OjzL4uJEP/MRAw=="
  "resolved" "https://registry.npmmirror.com/dom-align/-/dom-align-1.12.4.tgz"
  "version" "1.12.4"

"dom-closest@^0.2.0":
  "integrity" "sha512-6neTn1BtJlTSt+XSISXpnOsF1uni1CHsP/tmzZMGWxasYFHsBOqrHPnzmneqEgKhpagnfnfSfbvRRW0xFsBHAA=="
  "resolved" "https://registry.npmmirror.com/dom-closest/-/dom-closest-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "dom-matches" ">=1.0.1"

"dom-converter@^0.2.0":
  "integrity" "sha512-gd3ypIPfOMr9h5jIKq8E3sHOTCjeirnl0WK5ZdS1AW0Odt0b1PaWaHdJ4Qk4klv+YB9aJBS7mESXjFoDQPu6DA=="
  "resolved" "https://registry.npmmirror.com/dom-converter/-/dom-converter-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "utila" "~0.4"

"dom-matches@>=1.0.1":
  "integrity" "sha512-2VI856xEDCLXi19W+4BechR5/oIS6bKCKqcf16GR8Pg7dGLJ/eBOWVbCmQx2ISvYH6wTNx5Ef7JTOw1dRGRx6A=="
  "resolved" "https://registry.npmmirror.com/dom-matches/-/dom-matches-2.0.0.tgz"
  "version" "2.0.0"

"dom-scroll-into-view@^2.0.0":
  "integrity" "sha512-bvVTQe1lfaUr1oFzZX80ce9KLDlZ3iU+XGNE/bz9HnGdklTieqsbmsLHe+rT2XWqopvL0PckkYqN7ksmm5pe3w=="
  "resolved" "https://registry.npmmirror.com/dom-scroll-into-view/-/dom-scroll-into-view-2.0.1.tgz"
  "version" "2.0.1"

"dom-serializer@^1.0.1":
  "integrity" "sha512-VHwB3KfrcOOkelEG2ZOfxqLZdfkil8PtJi4P8N2MMXucZq2yLp75ClViUlOVwyoHEDjYU433Aq+5zWP61+RGag=="
  "resolved" "https://registry.npmmirror.com/dom-serializer/-/dom-serializer-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "domelementtype" "^2.0.1"
    "domhandler" "^4.2.0"
    "entities" "^2.0.0"

"domelementtype@^2.0.1", "domelementtype@^2.2.0":
  "integrity" "sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw=="
  "resolved" "https://registry.npmmirror.com/domelementtype/-/domelementtype-2.3.0.tgz"
  "version" "2.3.0"

"domexception@^2.0.1":
  "integrity" "sha512-yxJ2mFy/sibVQlu5qHjOkf9J3K6zgmCxgJ94u2EdvDOV09H+32LtRswEcUsmUWN72pVLOEnTSRaIVVzVQgS0dg=="
  "resolved" "https://registry.npmmirror.com/domexception/-/domexception-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "webidl-conversions" "^5.0.0"

"domhandler@^4.0.0", "domhandler@^4.2.0", "domhandler@^4.3.1":
  "integrity" "sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ=="
  "resolved" "https://registry.npmmirror.com/domhandler/-/domhandler-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "domelementtype" "^2.2.0"

"domutils@^2.5.2", "domutils@^2.8.0":
  "integrity" "sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A=="
  "resolved" "https://registry.npmmirror.com/domutils/-/domutils-2.8.0.tgz"
  "version" "2.8.0"
  dependencies:
    "dom-serializer" "^1.0.1"
    "domelementtype" "^2.2.0"
    "domhandler" "^4.2.0"

"dot-case@^3.0.4":
  "integrity" "sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w=="
  "resolved" "https://registry.npmmirror.com/dot-case/-/dot-case-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "no-case" "^3.0.4"
    "tslib" "^2.0.3"

"dotenv-expand@^5.1.0":
  "integrity" "sha512-YXQl1DSa4/PQyRfgrv6aoNjhasp/p4qs9FjJ4q4cQk+8m4r6k4ZSiEyytKG8f8W9gi8WsQtIObNmKd+tMzNTmA=="
  "resolved" "https://registry.npmmirror.com/dotenv-expand/-/dotenv-expand-5.1.0.tgz"
  "version" "5.1.0"

"dotenv@^10.0.0":
  "integrity" "sha512-rlBi9d8jpv9Sf1klPjNfFAuWDjKLwTIJJ/VxtoTwIR6hnZxcEOQCZg2oIL3MWBYw5GpUDKOEnND7LXTbIpQ03Q=="
  "resolved" "https://registry.npmmirror.com/dotenv/-/dotenv-10.0.0.tgz"
  "version" "10.0.0"

"duplexer@^0.1.2":
  "integrity" "sha512-jtD6YG370ZCIi/9GTaJKQxWTZD045+4R4hTk/x1UyoqadyJ9x9CgSi1RlVDQF8U2sxLLSnFkCaMihqljHIWgMg=="
  "resolved" "https://registry.npmmirror.com/duplexer/-/duplexer-0.1.2.tgz"
  "version" "0.1.2"

"easy-stack@1.0.1":
  "integrity" "sha512-wK2sCs4feiiJeFXn3zvY0p41mdU5VUgbgs1rNsc/y5ngFUijdWd+iIN8eoyuZHKB8xN6BL4PdWmzqFmxNg6V2w=="
  "resolved" "https://registry.npmmirror.com/easy-stack/-/easy-stack-1.0.1.tgz"
  "version" "1.0.1"

"echarts@^5.4.3":
  "integrity" "sha512-mYKxLxhzy6zyTi/FaEbJMOZU1ULGEQHaeIeuMR5L+JnJTpz+YR03mnnpBhbR4+UYJAgiXgpyTVLffPAjOTLkZA=="
  "resolved" "https://registry.npmjs.org/echarts/-/echarts-5.4.3.tgz"
  "version" "5.4.3"
  dependencies:
    "tslib" "2.3.0"
    "zrender" "5.4.4"

"ee-first@1.1.1":
  "integrity" "sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow=="
  "resolved" "https://registry.npmmirror.com/ee-first/-/ee-first-1.1.1.tgz"
  "version" "1.1.1"

"electron-to-chromium@^1.4.284":
  "integrity" "sha512-dTei3VResi5bINDENswBxhL+N0Mw5YnfWyTqO75KGsVldurEkhC9+CelJVAse8jycWyP8pv3VSj4BSyP8wTWJA=="
  "resolved" "https://registry.npmmirror.com/electron-to-chromium/-/electron-to-chromium-1.4.342.tgz"
  "version" "1.4.342"

"emoji-regex@^8.0.0":
  "integrity" "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="
  "resolved" "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz"
  "version" "8.0.0"

"emojis-list@^3.0.0":
  "integrity" "sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q=="
  "resolved" "https://registry.npmmirror.com/emojis-list/-/emojis-list-3.0.0.tgz"
  "version" "3.0.0"

"encodeurl@~1.0.2":
  "integrity" "sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w=="
  "resolved" "https://registry.npmmirror.com/encodeurl/-/encodeurl-1.0.2.tgz"
  "version" "1.0.2"

"end-of-stream@^1.1.0":
  "integrity" "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q=="
  "resolved" "https://registry.npmmirror.com/end-of-stream/-/end-of-stream-1.4.4.tgz"
  "version" "1.4.4"
  dependencies:
    "once" "^1.4.0"

"enhanced-resolve@^5.10.0":
  "integrity" "sha512-QHTXI/sZQmko1cbDoNAa3mJ5qhWUUNAq3vR0/YiD379fWQrcfuoX1+HW2S0MTt7XmoPLapdaDKUtelUSPic7hQ=="
  "resolved" "https://registry.npmmirror.com/enhanced-resolve/-/enhanced-resolve-5.12.0.tgz"
  "version" "5.12.0"
  dependencies:
    "graceful-fs" "^4.2.4"
    "tapable" "^2.2.0"

"enquire.js@^2.1.6":
  "integrity" "sha512-/KujNpO+PT63F7Hlpu4h3pE3TokKRHN26JYmQpPyjkRD/N57R7bPDNojMXdi7uveAKjYB7yQnartCxZnFWr0Xw=="
  "resolved" "https://registry.npmmirror.com/enquire.js/-/enquire.js-2.1.6.tgz"
  "version" "2.1.6"

"enquirer@^2.3.5", "enquirer@^2.3.6", "enquirer@>= 2.3.0 < 3":
  "integrity" "sha512-yjNnPr315/FjS4zIsUxYguYUPP2e1NK4d7E7ZOLiyYCcbFBiTMyID+2wvm2w6+pZ/odMA7cRkjhsPbltwBOrLg=="
  "resolved" "https://registry.npmmirror.com/enquirer/-/enquirer-2.3.6.tgz"
  "version" "2.3.6"
  dependencies:
    "ansi-colors" "^4.1.1"

"entities@^2.0.0":
  "integrity" "sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A=="
  "resolved" "https://registry.npmmirror.com/entities/-/entities-2.2.0.tgz"
  "version" "2.2.0"

"errno@^0.1.1":
  "integrity" "sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A=="
  "resolved" "https://registry.npmmirror.com/errno/-/errno-0.1.8.tgz"
  "version" "0.1.8"
  dependencies:
    "prr" "~1.0.1"

"error-ex@^1.3.1":
  "integrity" "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g=="
  "resolved" "https://registry.npmmirror.com/error-ex/-/error-ex-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "is-arrayish" "^0.2.1"

"error-stack-parser@^2.0.6":
  "integrity" "sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ=="
  "resolved" "https://registry.npmmirror.com/error-stack-parser/-/error-stack-parser-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "stackframe" "^1.3.4"

"es-module-lexer@^0.9.0":
  "integrity" "sha512-1HQ2M2sPtxwnvOvT1ZClHyQDiggdNjURWpY2we6aMKCQiUVxTmVs2UYPLIrD84sS+kMdUwfBSylbJPwNnBrnHQ=="
  "resolved" "https://registry.npmmirror.com/es-module-lexer/-/es-module-lexer-0.9.3.tgz"
  "version" "0.9.3"

"escalade@^3.1.1":
  "integrity" "sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw=="
  "resolved" "https://registry.npmmirror.com/escalade/-/escalade-3.1.1.tgz"
  "version" "3.1.1"

"escape-html@~1.0.3":
  "integrity" "sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow=="
  "resolved" "https://registry.npmmirror.com/escape-html/-/escape-html-1.0.3.tgz"
  "version" "1.0.3"

"escape-string-regexp@^1.0.5":
  "integrity" "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg=="
  "resolved" "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"escape-string-regexp@^4.0.0":
  "integrity" "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA=="
  "resolved" "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  "version" "4.0.0"

"escodegen@^2.0.0":
  "integrity" "sha512-mmHKys/C8BFUGI+MAWNcSYoORYLMdPzjrknd2Vc+bUsjN5bXcr8EhrNB+UTqfL1y3I9c4fw2ihgtMPQLBRiQxw=="
  "resolved" "https://registry.npmmirror.com/escodegen/-/escodegen-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "esprima" "^4.0.1"
    "estraverse" "^5.2.0"
    "esutils" "^2.0.2"
    "optionator" "^0.8.1"
  optionalDependencies:
    "source-map" "~0.6.1"

"eslint-config-prettier@8.8.0":
  "integrity" "sha512-wLbQiFre3tdGgpDv67NQKnJuTlcUVYHas3k+DZCc2U2BadthoEY4B7hLPvAxaqdyOGCzuLfii2fqGph10va7oA=="
  "resolved" "https://registry.npmmirror.com/eslint-config-prettier/-/eslint-config-prettier-8.8.0.tgz"
  "version" "8.8.0"

"eslint-plugin-prettier@4.2.1":
  "integrity" "sha512-f/0rXLXUt0oFYs8ra4w49wYZBG5GKZpAYsJSm6rnYL5uVDjd+zowwMwVZHnAjf4edNrKpCDYfXDgmRE/Ak7QyQ=="
  "resolved" "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "prettier-linter-helpers" "^1.0.0"

"eslint-plugin-vue@8.7.1":
  "integrity" "sha512-28sbtm4l4cOzoO1LtzQPxfxhQABararUb1JtqusQqObJpWX2e/gmVyeYVfepizPFne0Q5cILkYGiBoV36L12Wg=="
  "resolved" "https://registry.npmmirror.com/eslint-plugin-vue/-/eslint-plugin-vue-8.7.1.tgz"
  "version" "8.7.1"
  dependencies:
    "eslint-utils" "^3.0.0"
    "natural-compare" "^1.4.0"
    "nth-check" "^2.0.1"
    "postcss-selector-parser" "^6.0.9"
    "semver" "^7.3.5"
    "vue-eslint-parser" "^8.0.1"

"eslint-scope@^5.1.1", "eslint-scope@5.1.1":
  "integrity" "sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw=="
  "resolved" "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^4.1.1"

"eslint-scope@^7.0.0":
  "integrity" "sha512-QKQM/UXpIiHcLqJ5AOyIW7XZmzjkzQXYE54n1++wb0u9V/abW3l9uQnxX8Z5Xd18xyKIMTUAyQ0k1e8pz6LUrw=="
  "resolved" "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^5.2.0"

"eslint-utils@^2.1.0":
  "integrity" "sha512-w94dQYoauyvlDc43XnGB8lU3Zt713vNChgt4EWwhXAP2XkBvndfxF0AgIqKOOasjPIPzj9JqgwkwbCYD0/V3Zg=="
  "resolved" "https://registry.npmmirror.com/eslint-utils/-/eslint-utils-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "eslint-visitor-keys" "^1.1.0"

"eslint-utils@^3.0.0":
  "integrity" "sha512-uuQC43IGctw68pJA1RgbQS8/NP7rch6Cwd4j3ZBtgo4/8Flj4eGE7ZYSZRN3iq5pVUv6GPdW5Z1RFleo84uLDA=="
  "resolved" "https://registry.npmmirror.com/eslint-utils/-/eslint-utils-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "eslint-visitor-keys" "^2.0.0"

"eslint-visitor-keys@^1.1.0":
  "integrity" "sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ=="
  "resolved" "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz"
  "version" "1.3.0"

"eslint-visitor-keys@^1.3.0":
  "integrity" "sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ=="
  "resolved" "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz"
  "version" "1.3.0"

"eslint-visitor-keys@^2.0.0", "eslint-visitor-keys@^2.1.0":
  "integrity" "sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw=="
  "resolved" "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz"
  "version" "2.1.0"

"eslint-visitor-keys@^3.1.0", "eslint-visitor-keys@^3.4.0":
  "integrity" "sha512-HPpKPUBQcAsZOsHAFwTtIKcYlCje62XB7SEAcxjtmW6TD1WVpkS6i6/hOVtTZIl4zGj/mBqpFVGvaDneik+VoQ=="
  "resolved" "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.4.0.tgz"
  "version" "3.4.0"

"eslint-webpack-plugin@^3.1.0":
  "integrity" "sha512-avrKcGncpPbPSUHX6B3stNGzkKFto3eL+DKM4+VyMrVnhPc3vRczVlCq3uhuFOdRvDHTVXuzwk1ZKUrqDQHQ9w=="
  "resolved" "https://registry.npmmirror.com/eslint-webpack-plugin/-/eslint-webpack-plugin-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "@types/eslint" "^7.29.0 || ^8.4.1"
    "jest-worker" "^28.0.2"
    "micromatch" "^4.0.5"
    "normalize-path" "^3.0.0"
    "schema-utils" "^4.0.0"

"eslint@^6.2.0 || ^7.0.0 || ^8.0.0", "eslint@^7.0.0 || ^8.0.0", "eslint@^7.5.0 || ^8.0.0", "eslint@>=5", "eslint@>=6.0.0", "eslint@>=7.0.0", "eslint@>=7.28.0", "eslint@>=7.5.0", "eslint@7.32.0":
  "integrity" "sha512-VHZ8gX+EDfz+97jGcgyGCyRia/dPOd6Xh9yPv8Bl1+SoaIwD+a/vlrOmGRUyOYu7MwUhc7CxqeaDZU13S4+EpA=="
  "resolved" "https://registry.npmmirror.com/eslint/-/eslint-7.32.0.tgz"
  "version" "7.32.0"
  dependencies:
    "@babel/code-frame" "7.12.11"
    "@eslint/eslintrc" "^0.4.3"
    "@humanwhocodes/config-array" "^0.5.0"
    "ajv" "^6.10.0"
    "chalk" "^4.0.0"
    "cross-spawn" "^7.0.2"
    "debug" "^4.0.1"
    "doctrine" "^3.0.0"
    "enquirer" "^2.3.5"
    "escape-string-regexp" "^4.0.0"
    "eslint-scope" "^5.1.1"
    "eslint-utils" "^2.1.0"
    "eslint-visitor-keys" "^2.0.0"
    "espree" "^7.3.1"
    "esquery" "^1.4.0"
    "esutils" "^2.0.2"
    "fast-deep-equal" "^3.1.3"
    "file-entry-cache" "^6.0.1"
    "functional-red-black-tree" "^1.0.1"
    "glob-parent" "^5.1.2"
    "globals" "^13.6.0"
    "ignore" "^4.0.6"
    "import-fresh" "^3.0.0"
    "imurmurhash" "^0.1.4"
    "is-glob" "^4.0.0"
    "js-yaml" "^3.13.1"
    "json-stable-stringify-without-jsonify" "^1.0.1"
    "levn" "^0.4.1"
    "lodash.merge" "^4.6.2"
    "minimatch" "^3.0.4"
    "natural-compare" "^1.4.0"
    "optionator" "^0.9.1"
    "progress" "^2.0.0"
    "regexpp" "^3.1.0"
    "semver" "^7.2.1"
    "strip-ansi" "^6.0.0"
    "strip-json-comments" "^3.1.0"
    "table" "^6.0.9"
    "text-table" "^0.2.0"
    "v8-compile-cache" "^2.0.3"

"espree@^7.3.0", "espree@^7.3.1":
  "integrity" "sha512-v3JCNCE64umkFpmkFGqzVKsOT0tN1Zr+ueqLZfpV1Ob8e+CEgPWa+OxCoGH3tnhimMKIaBm4m/vaRpJ/krRz2g=="
  "resolved" "https://registry.npmmirror.com/espree/-/espree-7.3.1.tgz"
  "version" "7.3.1"
  dependencies:
    "acorn" "^7.4.0"
    "acorn-jsx" "^5.3.1"
    "eslint-visitor-keys" "^1.3.0"

"espree@^9.0.0":
  "integrity" "sha512-5yxtHSZXRSW5pvv3hAlXM5+/Oswi1AUFqBmbibKb5s6bp3rGIDkyXU6xCoyuuLhijr4SFwPrXRoZjz0AZDN9tg=="
  "resolved" "https://registry.npmmirror.com/espree/-/espree-9.5.1.tgz"
  "version" "9.5.1"
  dependencies:
    "acorn" "^8.8.0"
    "acorn-jsx" "^5.3.2"
    "eslint-visitor-keys" "^3.4.0"

"esprima@^4.0.0", "esprima@^4.0.1":
  "integrity" "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A=="
  "resolved" "https://registry.npmmirror.com/esprima/-/esprima-4.0.1.tgz"
  "version" "4.0.1"

"esquery@^1.4.0":
  "integrity" "sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg=="
  "resolved" "https://registry.npmmirror.com/esquery/-/esquery-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "estraverse" "^5.1.0"

"esrecurse@^4.3.0":
  "integrity" "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag=="
  "resolved" "https://registry.npmmirror.com/esrecurse/-/esrecurse-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "estraverse" "^5.2.0"

"estraverse@^4.1.1":
  "integrity" "sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw=="
  "resolved" "https://registry.npmmirror.com/estraverse/-/estraverse-4.3.0.tgz"
  "version" "4.3.0"

"estraverse@^5.1.0", "estraverse@^5.2.0":
  "integrity" "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA=="
  "resolved" "https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz"
  "version" "5.3.0"

"esutils@^2.0.2":
  "integrity" "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g=="
  "resolved" "https://registry.npmmirror.com/esutils/-/esutils-2.0.3.tgz"
  "version" "2.0.3"

"etag@~1.8.1":
  "integrity" "sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg=="
  "resolved" "https://registry.npmmirror.com/etag/-/etag-1.8.1.tgz"
  "version" "1.8.1"

"event-pubsub@4.3.0":
  "integrity" "sha512-z7IyloorXvKbFx9Bpie2+vMJKKx1fH1EN5yiTfp8CiLOTptSYy1g8H4yDpGlEdshL1PBiFtBHepF2cNsqeEeFQ=="
  "resolved" "https://registry.npmmirror.com/event-pubsub/-/event-pubsub-4.3.0.tgz"
  "version" "4.3.0"

"eventemitter3@^2.0.3":
  "integrity" "sha512-jLN68Dx5kyFHaePoXWPsCGW5qdyZQtLYHkxkg02/Mz6g0kYpDx4FyP6XfArhQdlOC4b8Mv+EMxPo/8La7Tzghg=="
  "resolved" "https://registry.npmjs.org/eventemitter3/-/eventemitter3-2.0.3.tgz"
  "version" "2.0.3"

"eventemitter3@^4.0.0":
  "integrity" "sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw=="
  "resolved" "https://registry.npmmirror.com/eventemitter3/-/eventemitter3-4.0.7.tgz"
  "version" "4.0.7"

"events@^3.2.0":
  "integrity" "sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q=="
  "resolved" "https://registry.npmmirror.com/events/-/events-3.3.0.tgz"
  "version" "3.3.0"

"execa@^0.8.0":
  "integrity" "sha512-zDWS+Rb1E8BlqqhALSt9kUhss8Qq4nN3iof3gsOdyINksElaPyNBtKUMTR62qhvgVWR0CqCX7sdnKe4MnUbFEA=="
  "resolved" "https://registry.npmmirror.com/execa/-/execa-0.8.0.tgz"
  "version" "0.8.0"
  dependencies:
    "cross-spawn" "^5.0.1"
    "get-stream" "^3.0.0"
    "is-stream" "^1.1.0"
    "npm-run-path" "^2.0.0"
    "p-finally" "^1.0.0"
    "signal-exit" "^3.0.0"
    "strip-eof" "^1.0.0"

"execa@^1.0.0":
  "integrity" "sha512-adbxcyWV46qiHyvSp50TKt05tB4tK3HcmF7/nxfAdhnox83seTDbwnaqKO4sXRy7roHAIFqJP/Rw/AuEbX61LA=="
  "resolved" "https://registry.npmmirror.com/execa/-/execa-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "cross-spawn" "^6.0.0"
    "get-stream" "^4.0.0"
    "is-stream" "^1.1.0"
    "npm-run-path" "^2.0.0"
    "p-finally" "^1.0.0"
    "signal-exit" "^3.0.0"
    "strip-eof" "^1.0.0"

"execa@^5.0.0":
  "integrity" "sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg=="
  "resolved" "https://registry.npmmirror.com/execa/-/execa-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "cross-spawn" "^7.0.3"
    "get-stream" "^6.0.0"
    "human-signals" "^2.1.0"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^4.0.1"
    "onetime" "^5.1.2"
    "signal-exit" "^3.0.3"
    "strip-final-newline" "^2.0.0"

"execa@^5.1.1":
  "integrity" "sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg=="
  "resolved" "https://registry.npmmirror.com/execa/-/execa-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "cross-spawn" "^7.0.3"
    "get-stream" "^6.0.0"
    "human-signals" "^2.1.0"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^4.0.1"
    "onetime" "^5.1.2"
    "signal-exit" "^3.0.3"
    "strip-final-newline" "^2.0.0"

"exit-on-epipe@~1.0.1":
  "integrity" "sha512-h2z5mrROTxce56S+pnvAV890uu7ls7f1kEvVGJbw1OlFH3/mlJ5bkXu0KRyW94v37zzHPiUd55iLn3DA7TjWpw=="
  "resolved" "https://registry.npmmirror.com/exit-on-epipe/-/exit-on-epipe-1.0.1.tgz"
  "version" "1.0.1"

"express@^4.17.3":
  "integrity" "sha512-5/PsL6iGPdfQ/lKM1UuielYgv3BUoJfz1aUwU9vHZ+J7gyvwdQXFEBIEIaxeGf0GIcreATNyBExtalisDbuMqQ=="
  "resolved" "https://registry.npmmirror.com/express/-/express-4.18.2.tgz"
  "version" "4.18.2"
  dependencies:
    "accepts" "~1.3.8"
    "array-flatten" "1.1.1"
    "body-parser" "1.20.1"
    "content-disposition" "0.5.4"
    "content-type" "~1.0.4"
    "cookie" "0.5.0"
    "cookie-signature" "1.0.6"
    "debug" "2.6.9"
    "depd" "2.0.0"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "finalhandler" "1.2.0"
    "fresh" "0.5.2"
    "http-errors" "2.0.0"
    "merge-descriptors" "1.0.1"
    "methods" "~1.1.2"
    "on-finished" "2.4.1"
    "parseurl" "~1.3.3"
    "path-to-regexp" "0.1.7"
    "proxy-addr" "~2.0.7"
    "qs" "6.11.0"
    "range-parser" "~1.2.1"
    "safe-buffer" "5.2.1"
    "send" "0.18.0"
    "serve-static" "1.15.0"
    "setprototypeof" "1.2.0"
    "statuses" "2.0.1"
    "type-is" "~1.6.18"
    "utils-merge" "1.0.1"
    "vary" "~1.1.2"

"extend@^3.0.2":
  "integrity" "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g=="
  "resolved" "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz"
  "version" "3.0.2"

"fast-deep-equal@^3.1.1", "fast-deep-equal@^3.1.3", "fast-deep-equal@3.1.3":
  "integrity" "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="
  "resolved" "https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  "version" "3.1.3"

"fast-diff@^1.1.2":
  "integrity" "sha512-xJuoT5+L99XlZ8twedaRf6Ax2TgQVxvgZOYoPKqZufmJib0tL2tegPBOZb1pVNgIhlqDlA0eO0c3wBvQcmzx4w=="
  "resolved" "https://registry.npmmirror.com/fast-diff/-/fast-diff-1.2.0.tgz"
  "version" "1.2.0"

"fast-diff@1.1.2":
  "integrity" "sha512-KaJUt+M9t1qaIteSvjc6P3RbMdXsNhK61GRftR6SNxqmhthcd9MGIi4T+o0jD8LUSpSnSKXE20nLtJ3fOHxQig=="
  "resolved" "https://registry.npmjs.org/fast-diff/-/fast-diff-1.1.2.tgz"
  "version" "1.1.2"

"fast-glob@^3.2.7", "fast-glob@^3.2.9":
  "integrity" "sha512-DVj4CQIYYow0BlaelwK1pHl5n5cRSJfM60UA0zK891sVInoPri2Ekj7+e1CT3/3qxXenpI+nBBmQAcJPJgaj4w=="
  "resolved" "https://registry.npmmirror.com/fast-glob/-/fast-glob-3.2.12.tgz"
  "version" "3.2.12"
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    "glob-parent" "^5.1.2"
    "merge2" "^1.3.0"
    "micromatch" "^4.0.4"

"fast-json-stable-stringify@^2.0.0":
  "integrity" "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="
  "resolved" "https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  "version" "2.1.0"

"fast-levenshtein@^2.0.6", "fast-levenshtein@~2.0.6":
  "integrity" "sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw=="
  "resolved" "https://registry.npmmirror.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  "version" "2.0.6"

"fastq@^1.6.0":
  "integrity" "sha512-wBrocU2LCXXa+lWBt8RoIRD89Fi8OdABODa/kEnyeyjS5aZO5/GNvI5sEINADqP/h8M29UHTHUb53sUu5Ihqdw=="
  "resolved" "https://registry.npmmirror.com/fastq/-/fastq-1.15.0.tgz"
  "version" "1.15.0"
  dependencies:
    "reusify" "^1.0.4"

"faye-websocket@^0.11.3":
  "integrity" "sha512-CzbClwlXAuiRQAlUyfqPgvPoNKTckTPGfwZV4ZdAhVcP2lh9KUxJg2b5GkE7XbjKQ3YJnQ9z6D9ntLAlB+tP8g=="
  "resolved" "https://registry.npmmirror.com/faye-websocket/-/faye-websocket-0.11.4.tgz"
  "version" "0.11.4"
  dependencies:
    "websocket-driver" ">=0.5.1"

"fflate@^0.3.8":
  "integrity" "sha512-Rr5QlUeGN1mbOHlaqcSYMKVpPbgLy0AWT/W0EHxA6NGI12yO1jpoui2zBBvU2G824ltM6Ut8BFgfHSBGfkmS0A=="
  "resolved" "https://registry.npmmirror.com/fflate/-/fflate-0.3.11.tgz"
  "version" "0.3.11"

"figures@^2.0.0":
  "integrity" "sha512-Oa2M9atig69ZkfwiApY8F2Yy+tzMbazyvqv21R0NsSC8floSOC09BbT1ITWAdoMGQvJ/aZnR1KMwdx9tvHnTNA=="
  "resolved" "https://registry.npmmirror.com/figures/-/figures-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "escape-string-regexp" "^1.0.5"

"file-entry-cache@^6.0.1":
  "integrity" "sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg=="
  "resolved" "https://registry.npmmirror.com/file-entry-cache/-/file-entry-cache-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "flat-cache" "^3.0.4"

"fill-range@^7.0.1":
  "integrity" "sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ=="
  "resolved" "https://registry.npmmirror.com/fill-range/-/fill-range-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "to-regex-range" "^5.0.1"

"finalhandler@1.2.0":
  "integrity" "sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg=="
  "resolved" "https://registry.npmmirror.com/finalhandler/-/finalhandler-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "debug" "2.6.9"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "on-finished" "2.4.1"
    "parseurl" "~1.3.3"
    "statuses" "2.0.1"
    "unpipe" "~1.0.0"

"find-cache-dir@^3.3.1":
  "integrity" "sha512-wXZV5emFEjrridIgED11OoUKLxiYjAcqot/NJdAkOhlJ+vGzwhOAfcG5OX1jP+S0PcjEn8bdMJv+g2jwQ3Onig=="
  "resolved" "https://registry.npmmirror.com/find-cache-dir/-/find-cache-dir-3.3.2.tgz"
  "version" "3.3.2"
  dependencies:
    "commondir" "^1.0.1"
    "make-dir" "^3.0.2"
    "pkg-dir" "^4.1.0"

"find-up@^4.0.0", "find-up@^4.1.0":
  "integrity" "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw=="
  "resolved" "https://registry.npmmirror.com/find-up/-/find-up-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "locate-path" "^5.0.0"
    "path-exists" "^4.0.0"

"flat-cache@^3.0.4":
  "integrity" "sha512-dm9s5Pw7Jc0GvMYbshN6zchCA9RgQlzzEZX3vylR9IqFfS8XciblUXOKfW6SiuJ0e13eDYZoZV5wdrev7P3Nwg=="
  "resolved" "https://registry.npmmirror.com/flat-cache/-/flat-cache-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "flatted" "^3.1.0"
    "rimraf" "^3.0.2"

"flatted@^3.1.0":
  "integrity" "sha512-5nqDSxl8nn5BSNxyR3n4I6eDmbolI6WT+QqR547RwxQapgjQBmtktdP+HTBb/a/zLsbzERTONyUB5pefh5TtjQ=="
  "resolved" "https://registry.npmmirror.com/flatted/-/flatted-3.2.7.tgz"
  "version" "3.2.7"

"follow-redirects@^1.0.0", "follow-redirects@^1.15.0":
  "integrity" "sha512-VQLG33o04KaQ8uYi2tVNbdrWp1QWxNNea+nmIB4EVM28v0hmP17z7aG1+wAkNzVq4KeXTq3221ye5qTJP91JwA=="
  "resolved" "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.2.tgz"
  "version" "1.15.2"

"form-data@^3.0.0":
  "integrity" "sha512-RHkBKtLWUVwd7SqRIvCZMEvAMoGUp0XU+seQiZejj0COz3RI3hWP4sCv3gZWWLjJTd7rGwcsF5eKZGii0r/hbg=="
  "resolved" "https://registry.npmmirror.com/form-data/-/form-data-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.8"
    "mime-types" "^2.1.12"

"form-data@^4.0.0":
  "integrity" "sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww=="
  "resolved" "https://registry.npmmirror.com/form-data/-/form-data-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.8"
    "mime-types" "^2.1.12"

"forwarded@0.2.0":
  "integrity" "sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow=="
  "resolved" "https://registry.npmmirror.com/forwarded/-/forwarded-0.2.0.tgz"
  "version" "0.2.0"

"frac@~1.1.2":
  "integrity" "sha512-w/XBfkibaTl3YDqASwfDUqkna4Z2p9cFSr1aHDt0WoMTECnRfBOv2WArlZILlqgWlmdIlALXGpM2AOhEk5W3IA=="
  "resolved" "https://registry.npmmirror.com/frac/-/frac-1.1.2.tgz"
  "version" "1.1.2"

"fraction.js@^4.2.0":
  "integrity" "sha512-MhLuK+2gUcnZe8ZHlaaINnQLl0xRIGRfcGk2yl8xoQAfHrSsL3rYu6FCmBdkdbhc9EPlwyGHewaRsvwRMJtAlA=="
  "resolved" "https://registry.npmmirror.com/fraction.js/-/fraction.js-4.2.0.tgz"
  "version" "4.2.0"

"fresh@0.5.2":
  "integrity" "sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q=="
  "resolved" "https://registry.npmmirror.com/fresh/-/fresh-0.5.2.tgz"
  "version" "0.5.2"

"fs-extra@^9.1.0":
  "integrity" "sha512-hcg3ZmepS30/7BSFqRvoo3DOMQu7IjqxO5nCDt+zM9XWjb33Wg7ziNT+Qvqbuc3+gWpzO02JubVyk2G4Zvo1OQ=="
  "resolved" "https://registry.npmmirror.com/fs-extra/-/fs-extra-9.1.0.tgz"
  "version" "9.1.0"
  dependencies:
    "at-least-node" "^1.0.0"
    "graceful-fs" "^4.2.0"
    "jsonfile" "^6.0.1"
    "universalify" "^2.0.0"

"fs-monkey@^1.0.3":
  "integrity" "sha512-cybjIfiiE+pTWicSCLFHSrXZ6EilF30oh91FDP9S2B051prEa7QWfrVTQm10/dDpswBDXZugPa1Ogu8Yh+HV0Q=="
  "resolved" "https://registry.npmmirror.com/fs-monkey/-/fs-monkey-1.0.3.tgz"
  "version" "1.0.3"

"fs.realpath@^1.0.0":
  "integrity" "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw=="
  "resolved" "https://registry.npmmirror.com/fs.realpath/-/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"fsevents@~2.3.2":
  "integrity" "sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA=="
  "resolved" "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.2.tgz"
  "version" "2.3.2"

"function-bind@^1.1.1":
  "integrity" "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="
  "resolved" "https://registry.npmmirror.com/function-bind/-/function-bind-1.1.1.tgz"
  "version" "1.1.1"

"functional-red-black-tree@^1.0.1":
  "integrity" "sha512-dsKNQNdj6xA3T+QlADDA7mOSlX0qiMINjn0cgr+eGHGsbSHzTabcIogz2+p/iqP1Xs6EP/sS2SbqH+brGTbq0g=="
  "resolved" "https://registry.npmmirror.com/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz"
  "version" "1.0.1"

"functions-have-names@^1.2.3":
  "integrity" "sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ=="
  "resolved" "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.3.tgz"
  "version" "1.2.3"

"gensync@^1.0.0-beta.2":
  "integrity" "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg=="
  "resolved" "https://registry.npmmirror.com/gensync/-/gensync-1.0.0-beta.2.tgz"
  "version" "1.0.0-beta.2"

"get-caller-file@^2.0.5":
  "integrity" "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg=="
  "resolved" "https://registry.npmmirror.com/get-caller-file/-/get-caller-file-2.0.5.tgz"
  "version" "2.0.5"

"get-intrinsic@^1.0.2", "get-intrinsic@^1.1.1":
  "integrity" "sha512-L049y6nFOuom5wGyRc3/gdTLO94dySVKRACj1RmJZBQXlbTMhtNIgkWkUHq+jYmZvKf14EW1EoJnnjbmoHij0Q=="
  "resolved" "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "function-bind" "^1.1.1"
    "has" "^1.0.3"
    "has-symbols" "^1.0.3"

"get-own-enumerable-property-symbols@^3.0.0":
  "integrity" "sha512-I0UBV/XOz1XkIJHEUDMZAbzCThU/H8DxmSfmdGcKPnVhu2VfFqr34jr9777IyaTYvxjedWhqVIilEDsCdP5G6g=="
  "resolved" "https://registry.npmmirror.com/get-own-enumerable-property-symbols/-/get-own-enumerable-property-symbols-3.0.2.tgz"
  "version" "3.0.2"

"get-stream@^3.0.0":
  "integrity" "sha512-GlhdIUuVakc8SJ6kK0zAFbiGzRFzNnY4jUuEbV9UROo4Y+0Ny4fjvcZFVTeDA4odpFyOQzaw6hXukJSq/f28sQ=="
  "resolved" "https://registry.npmmirror.com/get-stream/-/get-stream-3.0.0.tgz"
  "version" "3.0.0"

"get-stream@^4.0.0":
  "integrity" "sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w=="
  "resolved" "https://registry.npmmirror.com/get-stream/-/get-stream-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "pump" "^3.0.0"

"get-stream@^6.0.0":
  "integrity" "sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg=="
  "resolved" "https://registry.npmmirror.com/get-stream/-/get-stream-6.0.1.tgz"
  "version" "6.0.1"

"glob-parent@^5.1.2", "glob-parent@~5.1.2":
  "integrity" "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  "resolved" "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob-parent@^6.0.1":
  "integrity" "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A=="
  "resolved" "https://registry.npmmirror.com/glob-parent/-/glob-parent-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "is-glob" "^4.0.3"

"glob-to-regexp@^0.4.1":
  "integrity" "sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw=="
  "resolved" "https://registry.npmmirror.com/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz"
  "version" "0.4.1"

"glob@^7.1.3", "glob@^7.2.0":
  "integrity" "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q=="
  "resolved" "https://registry.npmmirror.com/glob/-/glob-7.2.3.tgz"
  "version" "7.2.3"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.1.1"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"globals@^11.1.0":
  "integrity" "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA=="
  "resolved" "https://registry.npmmirror.com/globals/-/globals-11.12.0.tgz"
  "version" "11.12.0"

"globals@^13.6.0":
  "integrity" "sha512-Qg5QtVkCy/kv3FUSlu4ukeZDVf9ee0iXLAUYX13gbR17bnejFTzr4iS9bY7kwCf1NztRNm1t91fjOiyx4CSwPQ=="
  "resolved" "https://registry.npmmirror.com/globals/-/globals-13.20.0.tgz"
  "version" "13.20.0"
  dependencies:
    "type-fest" "^0.20.2"

"globals@^13.9.0":
  "integrity" "sha512-Qg5QtVkCy/kv3FUSlu4ukeZDVf9ee0iXLAUYX13gbR17bnejFTzr4iS9bY7kwCf1NztRNm1t91fjOiyx4CSwPQ=="
  "resolved" "https://registry.npmmirror.com/globals/-/globals-13.20.0.tgz"
  "version" "13.20.0"
  dependencies:
    "type-fest" "^0.20.2"

"globby@^11.0.2", "globby@^11.0.3":
  "integrity" "sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g=="
  "resolved" "https://registry.npmmirror.com/globby/-/globby-11.1.0.tgz"
  "version" "11.1.0"
  dependencies:
    "array-union" "^2.1.0"
    "dir-glob" "^3.0.1"
    "fast-glob" "^3.2.9"
    "ignore" "^5.2.0"
    "merge2" "^1.4.1"
    "slash" "^3.0.0"

"graceful-fs@^4.1.2", "graceful-fs@^4.1.6", "graceful-fs@^4.2.0", "graceful-fs@^4.2.4", "graceful-fs@^4.2.6", "graceful-fs@^4.2.9":
  "integrity" "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ=="
  "resolved" "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.11.tgz"
  "version" "4.2.11"

"gzip-size@^6.0.0":
  "integrity" "sha512-ax7ZYomf6jqPTQ4+XCpUGyXKHk5WweS+e05MBO4/y3WJ5RkmPXNKvX+bx1behVILVwr6JSQvZAku021CHPXG3Q=="
  "resolved" "https://registry.npmmirror.com/gzip-size/-/gzip-size-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "duplexer" "^0.1.2"

"handle-thing@^2.0.0":
  "integrity" "sha512-9Qn4yBxelxoh2Ow62nP+Ka/kMnOXRi8BXnRaUwezLNhqelnN49xKz4F/dPP8OYLxLxq6JDtZb2i9XznUQbNPTg=="
  "resolved" "https://registry.npmmirror.com/handle-thing/-/handle-thing-2.0.1.tgz"
  "version" "2.0.1"

"has-flag@^3.0.0":
  "integrity" "sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw=="
  "resolved" "https://registry.npmmirror.com/has-flag/-/has-flag-3.0.0.tgz"
  "version" "3.0.0"

"has-flag@^4.0.0":
  "integrity" "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ=="
  "resolved" "https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz"
  "version" "4.0.0"

"has-property-descriptors@^1.0.0":
  "integrity" "sha512-62DVLZGoiEBDHQyqG4w9xCuZ7eJEwNmJRWw2VY84Oedb7WFcA27fiEVe8oUQx9hAUJ4ekurquucTGwsyO1XGdQ=="
  "resolved" "https://registry.npmmirror.com/has-property-descriptors/-/has-property-descriptors-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "get-intrinsic" "^1.1.1"

"has-symbols@^1.0.2", "has-symbols@^1.0.3":
  "integrity" "sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A=="
  "resolved" "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.0.3.tgz"
  "version" "1.0.3"

"has-tostringtag@^1.0.0":
  "integrity" "sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ=="
  "resolved" "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-symbols" "^1.0.2"

"has@^1.0.3":
  "integrity" "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw=="
  "resolved" "https://registry.npmmirror.com/has/-/has-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "function-bind" "^1.1.1"

"hash-sum@^1.0.2":
  "integrity" "sha512-fUs4B4L+mlt8/XAtSOGMUO1TXmAelItBPtJG7CyHJfYTdDjwisntGO2JQz7oUsatOY9o68+57eziUVNw/mRHmA=="
  "resolved" "https://registry.npmmirror.com/hash-sum/-/hash-sum-1.0.2.tgz"
  "version" "1.0.2"

"hash-sum@^2.0.0":
  "integrity" "sha512-WdZTbAByD+pHfl/g9QSsBIIwy8IT+EsPiKDs0KNX+zSHhdDLFKdZu0BQHljvO+0QI/BasbMSUa8wYNCZTvhslg=="
  "resolved" "https://registry.npmmirror.com/hash-sum/-/hash-sum-2.0.0.tgz"
  "version" "2.0.0"

"he@^1.2.0":
  "integrity" "sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw=="
  "resolved" "https://registry.npmmirror.com/he/-/he-1.2.0.tgz"
  "version" "1.2.0"

"highlight.js@^10.7.1":
  "integrity" "sha512-tzcUFauisWKNHaRkN4Wjl/ZA07gENAjFl3J/c480dprkGTg5EQstgaNFqBfUqCq54kZRIEcreTsAgF/m2quD7A=="
  "resolved" "https://registry.npmmirror.com/highlight.js/-/highlight.js-10.7.3.tgz"
  "version" "10.7.3"

"hosted-git-info@^2.1.4":
  "integrity" "sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw=="
  "resolved" "https://registry.npmmirror.com/hosted-git-info/-/hosted-git-info-2.8.9.tgz"
  "version" "2.8.9"

"hpack.js@^2.1.6":
  "integrity" "sha512-zJxVehUdMGIKsRaNt7apO2Gqp0BdqW5yaiGHXXmbpvxgBYVZnAql+BJb4RO5ad2MgpbZKn5G6nMnegrH1FcNYQ=="
  "resolved" "https://registry.npmmirror.com/hpack.js/-/hpack.js-2.1.6.tgz"
  "version" "2.1.6"
  dependencies:
    "inherits" "^2.0.1"
    "obuf" "^1.0.0"
    "readable-stream" "^2.0.1"
    "wbuf" "^1.1.0"

"html-encoding-sniffer@^2.0.1":
  "integrity" "sha512-D5JbOMBIR/TVZkubHT+OyT2705QvogUW4IBn6nHd756OwieSF9aDYFj4dv6HHEVGYbHaLETa3WggZYWWMyy3ZQ=="
  "resolved" "https://registry.npmmirror.com/html-encoding-sniffer/-/html-encoding-sniffer-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "whatwg-encoding" "^1.0.5"

"html-entities@^2.3.2":
  "integrity" "sha512-DV5Ln36z34NNTDgnz0EWGBLZENelNAtkiFA4kyNOG2tDI6Mz1uSWiq1wAKdyjnJwyDiDO7Fa2SO1CTxPXL8VxA=="
  "resolved" "https://registry.npmmirror.com/html-entities/-/html-entities-2.3.3.tgz"
  "version" "2.3.3"

"html-minifier-terser@^6.0.2":
  "integrity" "sha512-YXxSlJBZTP7RS3tWnQw74ooKa6L9b9i9QYXY21eUEvhZ3u9XLfv6OnFsQq6RxkhHygsaUMvYsZRV5rU/OVNZxw=="
  "resolved" "https://registry.npmmirror.com/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "camel-case" "^4.1.2"
    "clean-css" "^5.2.2"
    "commander" "^8.3.0"
    "he" "^1.2.0"
    "param-case" "^3.0.4"
    "relateurl" "^0.2.7"
    "terser" "^5.10.0"

"html-tags@^2.0.0":
  "integrity" "sha512-+Il6N8cCo2wB/Vd3gqy/8TZhTD3QvcVeQLCnZiGkGCH3JP28IgGAY41giccp2W4R3jfyJPAP318FQTa1yU7K7g=="
  "resolved" "https://registry.npmmirror.com/html-tags/-/html-tags-2.0.0.tgz"
  "version" "2.0.0"

"html-tags@^3.1.0":
  "integrity" "sha512-vy7ClnArOZwCnqZgvv+ddgHgJiAFXe3Ge9ML5/mBctVJoUoYPCdxVucOywjDARn6CVoh3dRSFdPHy2sX80L0Wg=="
  "resolved" "https://registry.npmmirror.com/html-tags/-/html-tags-3.2.0.tgz"
  "version" "3.2.0"

"html-webpack-plugin@^5.1.0":
  "integrity" "sha512-sy88PC2cRTVxvETRgUHFrL4No3UxvcH8G1NepGhqaTT+GXN2kTamqasot0inS5hXeg1cMbFDt27zzo9p35lZVw=="
  "resolved" "https://registry.npmmirror.com/html-webpack-plugin/-/html-webpack-plugin-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@types/html-minifier-terser" "^6.0.0"
    "html-minifier-terser" "^6.0.2"
    "lodash" "^4.17.21"
    "pretty-error" "^4.0.0"
    "tapable" "^2.0.0"

"htmlparser2@^6.1.0":
  "integrity" "sha512-gyyPk6rgonLFEDGoeRgQNaEUvdJ4ktTmmUh/h2t7s+M8oPpIPxgNACWa+6ESR57kXstwqPiCut0V8NRpcwgU7A=="
  "resolved" "https://registry.npmmirror.com/htmlparser2/-/htmlparser2-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "domelementtype" "^2.0.1"
    "domhandler" "^4.0.0"
    "domutils" "^2.5.2"
    "entities" "^2.0.0"

"http-deceiver@^1.2.7":
  "integrity" "sha512-LmpOGxTfbpgtGVxJrj5k7asXHCgNZp5nLfp+hWc8QQRqtb7fUy6kRY3BO1h9ddF6yIPYUARgxGOwB42DnxIaNw=="
  "resolved" "https://registry.npmmirror.com/http-deceiver/-/http-deceiver-1.2.7.tgz"
  "version" "1.2.7"

"http-errors@~1.6.2":
  "integrity" "sha512-lks+lVC8dgGyh97jxvxeYTWQFvh4uw4yC12gVl63Cg30sjPX4wuGcdkICVXDAESr6OJGjqGA8Iz5mkeN6zlD7A=="
  "resolved" "https://registry.npmmirror.com/http-errors/-/http-errors-1.6.3.tgz"
  "version" "1.6.3"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.3"
    "setprototypeof" "1.1.0"
    "statuses" ">= 1.4.0 < 2"

"http-errors@2.0.0":
  "integrity" "sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ=="
  "resolved" "https://registry.npmmirror.com/http-errors/-/http-errors-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "depd" "2.0.0"
    "inherits" "2.0.4"
    "setprototypeof" "1.2.0"
    "statuses" "2.0.1"
    "toidentifier" "1.0.1"

"http-parser-js@>=0.5.1":
  "integrity" "sha512-SGeBX54F94Wgu5RH3X5jsDtf4eHyRogWX1XGT3b4HuW3tQPM4AaBzoUji/4AAJNXCEOWZ5O0DgZmJw1947gD5Q=="
  "resolved" "https://registry.npmmirror.com/http-parser-js/-/http-parser-js-0.5.8.tgz"
  "version" "0.5.8"

"http-proxy-agent@^4.0.1":
  "integrity" "sha512-k0zdNgqWTGA6aeIRVpvfVob4fL52dTfaehylg0Y4UvSySvOq/Y+BOyPrgpUrA7HylqvU8vIZGsRuXmspskV0Tg=="
  "resolved" "https://registry.npmmirror.com/http-proxy-agent/-/http-proxy-agent-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "@tootallnate/once" "1"
    "agent-base" "6"
    "debug" "4"

"http-proxy-middleware@^2.0.3":
  "integrity" "sha512-ya/UeJ6HVBYxrgYotAZo1KvPWlgB48kUJLDePFeneHsVujFaW5WNj2NgWCAE//B1Dl02BIfYlpNgBy8Kf8Rjmw=="
  "resolved" "https://registry.npmmirror.com/http-proxy-middleware/-/http-proxy-middleware-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "@types/http-proxy" "^1.17.8"
    "http-proxy" "^1.18.1"
    "is-glob" "^4.0.1"
    "is-plain-obj" "^3.0.0"
    "micromatch" "^4.0.2"

"http-proxy@^1.18.1":
  "integrity" "sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ=="
  "resolved" "https://registry.npmmirror.com/http-proxy/-/http-proxy-1.18.1.tgz"
  "version" "1.18.1"
  dependencies:
    "eventemitter3" "^4.0.0"
    "follow-redirects" "^1.0.0"
    "requires-port" "^1.0.0"

"https-proxy-agent@^5.0.0":
  "integrity" "sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA=="
  "resolved" "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "agent-base" "6"
    "debug" "4"

"human-signals@^2.1.0":
  "integrity" "sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw=="
  "resolved" "https://registry.npmmirror.com/human-signals/-/human-signals-2.1.0.tgz"
  "version" "2.1.0"

"iconv-lite@0.4.24":
  "integrity" "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA=="
  "resolved" "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.24.tgz"
  "version" "0.4.24"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"icss-utils@^5.0.0", "icss-utils@^5.1.0":
  "integrity" "sha512-soFhflCVWLfRNOPU3iv5Z9VUdT44xFRbzjLsEzSr5AQmgqPMTHdU3PMT1Cf1ssx8fLNJDA1juftYl+PUcv3MqA=="
  "resolved" "https://registry.npmmirror.com/icss-utils/-/icss-utils-5.1.0.tgz"
  "version" "5.1.0"

"ieee754@^1.1.13":
  "integrity" "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA=="
  "resolved" "https://registry.npmmirror.com/ieee754/-/ieee754-1.2.1.tgz"
  "version" "1.2.1"

"ignore@^4.0.6":
  "integrity" "sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg=="
  "resolved" "https://registry.npmmirror.com/ignore/-/ignore-4.0.6.tgz"
  "version" "4.0.6"

"ignore@^5.2.0":
  "integrity" "sha512-MAb38BcSbH0eHNBxn7ql2NH/kX33OkB3lZ1BNdh7ENeRChHTYsTvWrMubiIAMNS2llXEEgZ1MUOBtXChP3kaFQ=="
  "resolved" "https://registry.npmmirror.com/ignore/-/ignore-5.2.4.tgz"
  "version" "5.2.4"

"image-size@~0.5.0":
  "integrity" "sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ=="
  "resolved" "https://registry.npmmirror.com/image-size/-/image-size-0.5.5.tgz"
  "version" "0.5.5"

"immutable@^4.0.0":
  "integrity" "sha512-0AOCmOip+xgJwEVTQj1EfiDDOkPmuyllDuTuEX+DDXUgapLAsBIfkg3sxCYyCEA8mQqZrrxPUGjcOQ2JS3WLkg=="
  "resolved" "https://registry.npmmirror.com/immutable/-/immutable-4.3.0.tgz"
  "version" "4.3.0"

"import-fresh@^3.0.0", "import-fresh@^3.2.1":
  "integrity" "sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw=="
  "resolved" "https://registry.npmmirror.com/import-fresh/-/import-fresh-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "parent-module" "^1.0.0"
    "resolve-from" "^4.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA=="
  "resolved" "https://registry.npmmirror.com/imurmurhash/-/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"indent-string@^4.0.0":
  "integrity" "sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg=="
  "resolved" "https://registry.npmmirror.com/indent-string/-/indent-string-4.0.0.tgz"
  "version" "4.0.0"

"inflight@^1.0.4":
  "integrity" "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA=="
  "resolved" "https://registry.npmmirror.com/inflight/-/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "^1.3.0"
    "wrappy" "1"

"inherits@^2.0.1", "inherits@^2.0.3", "inherits@^2.0.4", "inherits@~2.0.3", "inherits@2", "inherits@2.0.4":
  "integrity" "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="
  "resolved" "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz"
  "version" "2.0.4"

"inherits@2.0.3":
  "integrity" "sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw=="
  "resolved" "https://registry.npmmirror.com/inherits/-/inherits-2.0.3.tgz"
  "version" "2.0.3"

"intersperse@^1.0.0":
  "integrity" "sha512-LGcfug7OTeWkaQ8PEq8XbTy9Jl6uCNg8DrPnQUmwxSY8UETj1Y+LLmpdD0qHdEj6KVchuH3BE3ZzIXQ1t3oFUw=="
  "resolved" "https://registry.npmmirror.com/intersperse/-/intersperse-1.0.0.tgz"
  "version" "1.0.0"

"ipaddr.js@^2.0.1":
  "integrity" "sha512-1qTgH9NG+IIJ4yfKs2e6Pp1bZg8wbDbKHT21HrLIeYBTRLgMYKnMTPAuI3Lcs61nfx5h1xlXnbJtH1kX5/d/ng=="
  "resolved" "https://registry.npmmirror.com/ipaddr.js/-/ipaddr.js-2.0.1.tgz"
  "version" "2.0.1"

"ipaddr.js@1.9.1":
  "integrity" "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g=="
  "resolved" "https://registry.npmmirror.com/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
  "version" "1.9.1"

"is-arguments@^1.0.4":
  "integrity" "sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA=="
  "resolved" "https://registry.npmjs.org/is-arguments/-/is-arguments-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-arrayish@^0.2.1":
  "integrity" "sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg=="
  "resolved" "https://registry.npmmirror.com/is-arrayish/-/is-arrayish-0.2.1.tgz"
  "version" "0.2.1"

"is-binary-path@~2.1.0":
  "integrity" "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw=="
  "resolved" "https://registry.npmmirror.com/is-binary-path/-/is-binary-path-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "binary-extensions" "^2.0.0"

"is-ci@^1.0.10":
  "integrity" "sha512-s6tfsaQaQi3JNciBH6shVqEDvhGut0SUXr31ag8Pd8BBbVVlcGfWhpPmEOoM6RJ5TFhbypvf5yyRw/VXW1IiWg=="
  "resolved" "https://registry.npmmirror.com/is-ci/-/is-ci-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "ci-info" "^1.5.0"

"is-core-module@^2.9.0":
  "integrity" "sha512-RRjxlvLDkD1YJwDbroBHMb+cukurkDWNyHx7D3oNB5x9rb5ogcksMC5wHCadcXoo67gVr/+3GFySh3134zi6rw=="
  "resolved" "https://registry.npmmirror.com/is-core-module/-/is-core-module-2.11.0.tgz"
  "version" "2.11.0"
  dependencies:
    "has" "^1.0.3"

"is-date-object@^1.0.1":
  "integrity" "sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ=="
  "resolved" "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-docker@^2.0.0", "is-docker@^2.1.1":
  "integrity" "sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ=="
  "resolved" "https://registry.npmmirror.com/is-docker/-/is-docker-2.2.1.tgz"
  "version" "2.2.1"

"is-extglob@^2.1.1":
  "integrity" "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ=="
  "resolved" "https://registry.npmmirror.com/is-extglob/-/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-file-esm@^1.0.0":
  "integrity" "sha512-rZlaNKb4Mr8WlRu2A9XdeoKgnO5aA53XdPHgCKVyCrQ/rWi89RET1+bq37Ru46obaQXeiX4vmFIm1vks41hoSA=="
  "resolved" "https://registry.npmmirror.com/is-file-esm/-/is-file-esm-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "read-pkg-up" "^7.0.1"

"is-fullwidth-code-point@^2.0.0":
  "integrity" "sha512-VHskAKYM8RfSFXwee5t5cbN5PZeq1Wrh6qd5bkyiXIf6UQcN6w/A0eXM9r6t8d+GYOh+o6ZhiEnb88LN/Y8m2w=="
  "resolved" "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz"
  "version" "2.0.0"

"is-fullwidth-code-point@^3.0.0":
  "integrity" "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg=="
  "resolved" "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  "version" "3.0.0"

"is-glob@^4.0.0", "is-glob@^4.0.1", "is-glob@^4.0.3", "is-glob@~4.0.1":
  "integrity" "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg=="
  "resolved" "https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "is-extglob" "^2.1.1"

"is-interactive@^1.0.0":
  "integrity" "sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w=="
  "resolved" "https://registry.npmmirror.com/is-interactive/-/is-interactive-1.0.0.tgz"
  "version" "1.0.0"

"is-mobile@^2.2.1":
  "integrity" "sha512-wW/SXnYJkTjs++tVK5b6kVITZpAZPtUrt9SF80vvxGiF/Oywal+COk1jlRkiVq15RFNEQKQY31TkV24/1T5cVg=="
  "resolved" "https://registry.npmmirror.com/is-mobile/-/is-mobile-2.2.2.tgz"
  "version" "2.2.2"

"is-negative-zero@^2.0.0":
  "integrity" "sha512-dqJvarLawXsFbNDeJW7zAz8ItJ9cd28YufuuFzh0G8pNHjJMnY08Dv7sYX2uF5UpQOwieAeOExEYAWWfu7ZZUA=="
  "resolved" "https://registry.npmmirror.com/is-negative-zero/-/is-negative-zero-2.0.2.tgz"
  "version" "2.0.2"

"is-number@^7.0.0":
  "integrity" "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng=="
  "resolved" "https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz"
  "version" "7.0.0"

"is-obj@^1.0.1":
  "integrity" "sha512-l4RyHgRqGN4Y3+9JHVrNqO+tN0rV5My76uW5/nuO4K1b6vw5G8d/cmFjP9tRfEsdhZNt0IFdZuK/c2Vr4Nb+Qg=="
  "resolved" "https://registry.npmmirror.com/is-obj/-/is-obj-1.0.1.tgz"
  "version" "1.0.1"

"is-plain-obj@^3.0.0":
  "integrity" "sha512-gwsOE28k+23GP1B6vFl1oVh/WOzmawBrKwo5Ev6wMKzPkaXaCDIQKzLnvsA42DRlbVTWorkgTKIviAKCWkfUwA=="
  "resolved" "https://registry.npmmirror.com/is-plain-obj/-/is-plain-obj-3.0.0.tgz"
  "version" "3.0.0"

"is-plain-object@^2.0.4":
  "integrity" "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og=="
  "resolved" "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "isobject" "^3.0.1"

"is-potential-custom-element-name@^1.0.1":
  "integrity" "sha512-bCYeRA2rVibKZd+s2625gGnGF/t7DSqDs4dP7CrLA1m7jKWz6pps0LpYLJN8Q64HtmPKJ1hrN3nzPNKFEKOUiQ=="
  "resolved" "https://registry.npmmirror.com/is-potential-custom-element-name/-/is-potential-custom-element-name-1.0.1.tgz"
  "version" "1.0.1"

"is-regex@^1.0.4":
  "integrity" "sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg=="
  "resolved" "https://registry.npmjs.org/is-regex/-/is-regex-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-regexp@^1.0.0":
  "integrity" "sha512-7zjFAPO4/gwyQAAgRRmqeEeyIICSdmCqa3tsVHMdBzaXXRiqopZL4Cyghg/XulGWrtABTpbnYYzzIRffLkP4oA=="
  "resolved" "https://registry.npmmirror.com/is-regexp/-/is-regexp-1.0.0.tgz"
  "version" "1.0.0"

"is-stream@^1.1.0":
  "integrity" "sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ=="
  "resolved" "https://registry.npmmirror.com/is-stream/-/is-stream-1.1.0.tgz"
  "version" "1.1.0"

"is-stream@^2.0.0":
  "integrity" "sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg=="
  "resolved" "https://registry.npmmirror.com/is-stream/-/is-stream-2.0.1.tgz"
  "version" "2.0.1"

"is-unicode-supported@^0.1.0":
  "integrity" "sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw=="
  "resolved" "https://registry.npmmirror.com/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz"
  "version" "0.1.0"

"is-what@^3.14.1":
  "integrity" "sha512-sNxgpk9793nzSs7bA6JQJGeIuRBQhAaNGG77kzYQgMkrID+lS6SlK07K5LaptscDlSaIgH+GPFzf+d75FVxozA=="
  "resolved" "https://registry.npmmirror.com/is-what/-/is-what-3.14.1.tgz"
  "version" "3.14.1"

"is-wsl@^2.1.1", "is-wsl@^2.2.0":
  "integrity" "sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww=="
  "resolved" "https://registry.npmmirror.com/is-wsl/-/is-wsl-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "is-docker" "^2.0.0"

"isarray@~1.0.0":
  "integrity" "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ=="
  "resolved" "https://registry.npmmirror.com/isarray/-/isarray-1.0.0.tgz"
  "version" "1.0.0"

"isexe@^2.0.0":
  "integrity" "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw=="
  "resolved" "https://registry.npmmirror.com/isexe/-/isexe-2.0.0.tgz"
  "version" "2.0.0"

"ismobilejs@^1.0.0":
  "integrity" "sha512-VaFW53yt8QO61k2WJui0dHf4SlL8lxBofUuUmwBo0ljPk0Drz2TiuDW4jo3wDcv41qy/SxrJ+VAzJ/qYqsmzRw=="
  "resolved" "https://registry.npmmirror.com/ismobilejs/-/ismobilejs-1.1.1.tgz"
  "version" "1.1.1"

"isobject@^3.0.1":
  "integrity" "sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg=="
  "resolved" "https://registry.npmmirror.com/isobject/-/isobject-3.0.1.tgz"
  "version" "3.0.1"

"javascript-stringify@^2.0.1":
  "integrity" "sha512-JVAfqNPTvNq3sB/VHQJAFxN/sPgKnsKrCwyRt15zwNCdrMMJDdcEOdubuy+DuJYYdm0ox1J4uzEuYKkN+9yhVg=="
  "resolved" "https://registry.npmmirror.com/javascript-stringify/-/javascript-stringify-2.1.0.tgz"
  "version" "2.1.0"

"jest-worker@^27.0.2", "jest-worker@^27.4.5":
  "integrity" "sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg=="
  "resolved" "https://registry.npmmirror.com/jest-worker/-/jest-worker-27.5.1.tgz"
  "version" "27.5.1"
  dependencies:
    "@types/node" "*"
    "merge-stream" "^2.0.0"
    "supports-color" "^8.0.0"

"jest-worker@^28.0.2":
  "integrity" "sha512-CqRA220YV/6jCo8VWvAt1KKx6eek1VIHMPeLEbpcfSfkEeWyBNppynM/o6q+Wmw+sOhos2ml34wZbSX3G13//g=="
  "resolved" "https://registry.npmmirror.com/jest-worker/-/jest-worker-28.1.3.tgz"
  "version" "28.1.3"
  dependencies:
    "@types/node" "*"
    "merge-stream" "^2.0.0"
    "supports-color" "^8.0.0"

"joi@^17.4.0":
  "integrity" "sha512-FariIi9j6QODKATGBrEX7HZcja8Bsh3rfdGYy/Sb65sGlZWK/QWesU1ghk7aJWDj95knjXlQfSmzFSPPkLVsfw=="
  "resolved" "https://registry.npmmirror.com/joi/-/joi-17.9.1.tgz"
  "version" "17.9.1"
  dependencies:
    "@hapi/hoek" "^9.0.0"
    "@hapi/topo" "^5.0.0"
    "@sideway/address" "^4.1.3"
    "@sideway/formula" "^3.0.1"
    "@sideway/pinpoint" "^2.0.0"

"js-cookie@2.2.1":
  "integrity" "sha512-HvdH2LzI/EAZcUwA8+0nKNtWHqS+ZmijLA30RwZA0bo7ToCckjK5MkGhjED9KoRcXO6BaGI3I9UIzSA1FKFPOQ=="
  "resolved" "https://registry.npmmirror.com/js-cookie/-/js-cookie-2.2.1.tgz"
  "version" "2.2.1"

"js-message@1.0.7":
  "integrity" "sha512-efJLHhLjIyKRewNS9EGZ4UpI8NguuL6fKkhRxVuMmrGV2xN/0APGdQYwLFky5w9naebSZ0OwAGp0G6/2Cg90rA=="
  "resolved" "https://registry.npmmirror.com/js-message/-/js-message-1.0.7.tgz"
  "version" "1.0.7"

"js-tokens@^3.0.0 || ^4.0.0", "js-tokens@^4.0.0":
  "integrity" "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="
  "resolved" "https://registry.npmmirror.com/js-tokens/-/js-tokens-4.0.0.tgz"
  "version" "4.0.0"

"js-yaml@^3.13.1":
  "integrity" "sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g=="
  "resolved" "https://registry.npmmirror.com/js-yaml/-/js-yaml-3.14.1.tgz"
  "version" "3.14.1"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^4.0.0"

"jsdom@^16.2.2":
  "integrity" "sha512-u9Smc2G1USStM+s/x1ru5Sxrl6mPYCbByG1U/hUmqaVsm4tbNyS7CicOSRyuGQYZhTu0h84qkZZQ/I+dzizSVw=="
  "resolved" "https://registry.npmmirror.com/jsdom/-/jsdom-16.7.0.tgz"
  "version" "16.7.0"
  dependencies:
    "abab" "^2.0.5"
    "acorn" "^8.2.4"
    "acorn-globals" "^6.0.0"
    "cssom" "^0.4.4"
    "cssstyle" "^2.3.0"
    "data-urls" "^2.0.0"
    "decimal.js" "^10.2.1"
    "domexception" "^2.0.1"
    "escodegen" "^2.0.0"
    "form-data" "^3.0.0"
    "html-encoding-sniffer" "^2.0.1"
    "http-proxy-agent" "^4.0.1"
    "https-proxy-agent" "^5.0.0"
    "is-potential-custom-element-name" "^1.0.1"
    "nwsapi" "^2.2.0"
    "parse5" "6.0.1"
    "saxes" "^5.0.1"
    "symbol-tree" "^3.2.4"
    "tough-cookie" "^4.0.0"
    "w3c-hr-time" "^1.0.2"
    "w3c-xmlserializer" "^2.0.0"
    "webidl-conversions" "^6.1.0"
    "whatwg-encoding" "^1.0.5"
    "whatwg-mimetype" "^2.3.0"
    "whatwg-url" "^8.5.0"
    "ws" "^7.4.6"
    "xml-name-validator" "^3.0.0"

"jsencrypt@3.2.1":
  "integrity" "sha512-k1sD5QV0KPn+D8uG9AdGzTQuamt82QZ3A3l6f7TRwMU6Oi2Vg0BsL+wZIQBONcraO1pc78ExMdvmBBJ8WhNYUA=="
  "resolved" "https://registry.npmmirror.com/jsencrypt/-/jsencrypt-3.2.1.tgz"
  "version" "3.2.1"

"jsesc@^2.5.1":
  "integrity" "sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA=="
  "resolved" "https://registry.npmmirror.com/jsesc/-/jsesc-2.5.2.tgz"
  "version" "2.5.2"

"jsesc@~0.5.0":
  "integrity" "sha512-uZz5UnB7u4T9LvwmFqXii7pZSouaRPorGs5who1Ip7VO0wxanFvBL7GkM6dTHlgX+jhBApRetaWpnDabOeTcnA=="
  "resolved" "https://registry.npmmirror.com/jsesc/-/jsesc-0.5.0.tgz"
  "version" "0.5.0"

"json-parse-better-errors@^1.0.2":
  "integrity" "sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw=="
  "resolved" "https://registry.npmmirror.com/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz"
  "version" "1.0.2"

"json-parse-even-better-errors@^2.3.0", "json-parse-even-better-errors@^2.3.1":
  "integrity" "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w=="
  "resolved" "https://registry.npmmirror.com/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  "version" "2.3.1"

"json-schema-traverse@^0.4.1":
  "integrity" "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="
  "resolved" "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-schema-traverse@^1.0.0":
  "integrity" "sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug=="
  "resolved" "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz"
  "version" "1.0.0"

"json-stable-stringify-without-jsonify@^1.0.1":
  "integrity" "sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw=="
  "resolved" "https://registry.npmmirror.com/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  "version" "1.0.1"

"json2mq@^0.2.0":
  "integrity" "sha512-SzoRg7ux5DWTII9J2qkrZrqV1gt+rTaoufMxEzXbS26Uid0NwaJd123HcoB80TgubEppxxIGdNxCx50fEoEWQA=="
  "resolved" "https://registry.npmmirror.com/json2mq/-/json2mq-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "string-convert" "^0.2.0"

"json5@^1.0.1":
  "integrity" "sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA=="
  "resolved" "https://registry.npmmirror.com/json5/-/json5-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "minimist" "^1.2.0"

"json5@^2.1.2", "json5@^2.2.2":
  "integrity" "sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg=="
  "resolved" "https://registry.npmmirror.com/json5/-/json5-2.2.3.tgz"
  "version" "2.2.3"

"jsonfile@^6.0.1":
  "integrity" "sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ=="
  "resolved" "https://registry.npmmirror.com/jsonfile/-/jsonfile-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "universalify" "^2.0.0"
  optionalDependencies:
    "graceful-fs" "^4.1.6"

"kind-of@^6.0.2":
  "integrity" "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw=="
  "resolved" "https://registry.npmmirror.com/kind-of/-/kind-of-6.0.3.tgz"
  "version" "6.0.3"

"klona@^2.0.5", "klona@^2.0.6":
  "integrity" "sha512-dhG34DXATL5hSxJbIexCft8FChFXtmskoZYnoPWjXQuebWYCNkVeV3KkGegCK9CP1oswI/vQibS2GY7Em/sJJA=="
  "resolved" "https://registry.npmmirror.com/klona/-/klona-2.0.6.tgz"
  "version" "2.0.6"

"launch-editor-middleware@^2.2.1":
  "integrity" "sha512-K2yxgljj5TdCeRN1lBtO3/J26+AIDDDw+04y6VAiZbWcTdBwsYN6RrZBnW5DN/QiSIdKNjKdATLUUluWWFYTIA=="
  "resolved" "https://registry.npmmirror.com/launch-editor-middleware/-/launch-editor-middleware-2.6.0.tgz"
  "version" "2.6.0"
  dependencies:
    "launch-editor" "^2.6.0"

"launch-editor@^2.2.1", "launch-editor@^2.6.0":
  "integrity" "sha512-JpDCcQnyAAzZZaZ7vEiSqL690w7dAEyLao+KC96zBplnYbJS7TYNjvM3M7y3dGz+v7aIsJk3hllWuc0kWAjyRQ=="
  "resolved" "https://registry.npmmirror.com/launch-editor/-/launch-editor-2.6.0.tgz"
  "version" "2.6.0"
  dependencies:
    "picocolors" "^1.0.0"
    "shell-quote" "^1.7.3"

"less-loader@6.2.0":
  "integrity" "sha512-Cl5h95/Pz/PWub/tCBgT1oNMFeH1WTD33piG80jn5jr12T4XbxZcjThwNXDQ7AG649WEynuIzO4b0+2Tn9Qolg=="
  "resolved" "https://registry.npmmirror.com/less-loader/-/less-loader-6.2.0.tgz"
  "version" "6.2.0"
  dependencies:
    "clone" "^2.1.2"
    "less" "^3.11.3"
    "loader-utils" "^2.0.0"
    "schema-utils" "^2.7.0"

"less@^3.11.3", "less@3.13.1":
  "integrity" "sha512-SwA1aQXGUvp+P5XdZslUOhhLnClSLIjWvJhmd+Vgib5BFIr9lMNlQwmwUNOjXThF/A0x+MCYYPeWEfeWiLRnTw=="
  "resolved" "https://registry.npmmirror.com/less/-/less-3.13.1.tgz"
  "version" "3.13.1"
  dependencies:
    "copy-anything" "^2.0.1"
    "tslib" "^1.10.0"
  optionalDependencies:
    "errno" "^0.1.1"
    "graceful-fs" "^4.1.2"
    "image-size" "~0.5.0"
    "make-dir" "^2.1.0"
    "mime" "^1.4.1"
    "native-request" "^1.0.5"
    "source-map" "~0.6.0"

"levn@^0.4.1":
  "integrity" "sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ=="
  "resolved" "https://registry.npmmirror.com/levn/-/levn-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "prelude-ls" "^1.2.1"
    "type-check" "~0.4.0"

"levn@~0.3.0":
  "integrity" "sha512-0OO4y2iOHix2W6ujICbKIaEQXvFQHue65vUG3pb5EUomzPI90z9hsA1VsO/dbIIpC53J8gxM9Q4Oho0jrCM/yA=="
  "resolved" "https://registry.npmmirror.com/levn/-/levn-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "prelude-ls" "~1.1.2"
    "type-check" "~0.3.2"

"lilconfig@^2.0.3":
  "integrity" "sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ=="
  "resolved" "https://registry.npmmirror.com/lilconfig/-/lilconfig-2.1.0.tgz"
  "version" "2.1.0"

"lines-and-columns@^1.1.6":
  "integrity" "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg=="
  "resolved" "https://registry.npmmirror.com/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  "version" "1.2.4"

"lint-staged@11.2.6":
  "integrity" "sha512-Vti55pUnpvPE0J9936lKl0ngVeTdSZpEdTNhASbkaWX7J5R9OEifo1INBGQuGW4zmy6OG+TcWPJ3m5yuy5Q8Tg=="
  "resolved" "https://registry.npmmirror.com/lint-staged/-/lint-staged-11.2.6.tgz"
  "version" "11.2.6"
  dependencies:
    "cli-truncate" "2.1.0"
    "colorette" "^1.4.0"
    "commander" "^8.2.0"
    "cosmiconfig" "^7.0.1"
    "debug" "^4.3.2"
    "enquirer" "^2.3.6"
    "execa" "^5.1.1"
    "listr2" "^3.12.2"
    "micromatch" "^4.0.4"
    "normalize-path" "^3.0.0"
    "please-upgrade-node" "^3.2.0"
    "string-argv" "0.3.1"
    "stringify-object" "3.3.0"
    "supports-color" "8.1.1"

"listr2@^3.12.2":
  "integrity" "sha512-TyWI8G99GX9GjE54cJ+RrNMcIFBfwMPxc3XTFiAYGN4s10hWROGtOg7+O6u6LE3mNkyld7RSLE6nrKBvTfcs3g=="
  "resolved" "https://registry.npmmirror.com/listr2/-/listr2-3.14.0.tgz"
  "version" "3.14.0"
  dependencies:
    "cli-truncate" "^2.1.0"
    "colorette" "^2.0.16"
    "log-update" "^4.0.0"
    "p-map" "^4.0.0"
    "rfdc" "^1.3.0"
    "rxjs" "^7.5.1"
    "through" "^2.3.8"
    "wrap-ansi" "^7.0.0"

"loader-runner@^4.1.0", "loader-runner@^4.2.0":
  "integrity" "sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg=="
  "resolved" "https://registry.npmmirror.com/loader-runner/-/loader-runner-4.3.0.tgz"
  "version" "4.3.0"

"loader-utils@^1.0.2":
  "integrity" "sha512-I5d00Pd/jwMD2QCduo657+YM/6L3KZu++pmX9VFncxaxvHcru9jx1lBaFft+r4Mt2jK0Yhp41XlRAihzPxHNCg=="
  "resolved" "https://registry.npmmirror.com/loader-utils/-/loader-utils-1.4.2.tgz"
  "version" "1.4.2"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^1.0.1"

"loader-utils@^1.1.0":
  "integrity" "sha512-I5d00Pd/jwMD2QCduo657+YM/6L3KZu++pmX9VFncxaxvHcru9jx1lBaFft+r4Mt2jK0Yhp41XlRAihzPxHNCg=="
  "resolved" "https://registry.npmmirror.com/loader-utils/-/loader-utils-1.4.2.tgz"
  "version" "1.4.2"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^1.0.1"

"loader-utils@^2.0.0":
  "integrity" "sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw=="
  "resolved" "https://registry.npmmirror.com/loader-utils/-/loader-utils-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^2.1.2"

"locate-path@^5.0.0":
  "integrity" "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g=="
  "resolved" "https://registry.npmmirror.com/locate-path/-/locate-path-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-locate" "^4.1.0"

"lodash.debounce@^4.0.8":
  "integrity" "sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow=="
  "resolved" "https://registry.npmmirror.com/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  "version" "4.0.8"

"lodash.defaultsdeep@^4.6.1":
  "integrity" "sha512-3j8wdDzYuWO3lM3Reg03MuQR957t287Rpcxp1njpEa8oDrikb+FwGdW3n+FELh/A6qib6yPit0j/pv9G/yeAqA=="
  "resolved" "https://registry.npmmirror.com/lodash.defaultsdeep/-/lodash.defaultsdeep-4.6.1.tgz"
  "version" "4.6.1"

"lodash.kebabcase@^4.1.1":
  "integrity" "sha512-N8XRTIMMqqDgSy4VLKPnJ/+hpGZN+PHQiJnSenYqPaVV/NCqEogTnAdZLQiGKhxX+JCs8waWq2t1XHWKOmlY8g=="
  "resolved" "https://registry.npmmirror.com/lodash.kebabcase/-/lodash.kebabcase-4.1.1.tgz"
  "version" "4.1.1"

"lodash.mapvalues@^4.6.0":
  "integrity" "sha512-JPFqXFeZQ7BfS00H58kClY7SPVeHertPE0lNuCyZ26/XlN8TvakYD7b9bGyNmXbT/D3BbtPAAmq90gPWqLkxlQ=="
  "resolved" "https://registry.npmmirror.com/lodash.mapvalues/-/lodash.mapvalues-4.6.0.tgz"
  "version" "4.6.0"

"lodash.memoize@^4.1.2":
  "integrity" "sha512-t7j+NzmgnQzTAYXcsHYLgimltOV1MXHtlOWf6GjL9Kj8GK5FInw5JotxvbOs+IvV1/Dzo04/fCGfLVs7aXb4Ag=="
  "resolved" "https://registry.npmmirror.com/lodash.memoize/-/lodash.memoize-4.1.2.tgz"
  "version" "4.1.2"

"lodash.merge@^4.6.2":
  "integrity" "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ=="
  "resolved" "https://registry.npmmirror.com/lodash.merge/-/lodash.merge-4.6.2.tgz"
  "version" "4.6.2"

"lodash.truncate@^4.4.2":
  "integrity" "sha512-jttmRe7bRse52OsWIMDLaXxWqRAmtIUccAQ3garviCqJjafXOfNMO0yMfNpdD6zbGaTU0P5Nz7e7gAT6cKmJRw=="
  "resolved" "https://registry.npmmirror.com/lodash.truncate/-/lodash.truncate-4.4.2.tgz"
  "version" "4.4.2"

"lodash.uniq@^4.5.0":
  "integrity" "sha512-xfBaXQd9ryd9dlSDvnvI0lvxfLJlYAZzXomUYzLKtUeOQvOP5piqAWuGtrhWeqaXK9hhoM/iyJc5AV+XfsX3HQ=="
  "resolved" "https://registry.npmmirror.com/lodash.uniq/-/lodash.uniq-4.5.0.tgz"
  "version" "4.5.0"

"lodash@^4.17.14", "lodash@^4.17.20", "lodash@^4.17.21", "lodash@^4.17.4", "lodash@^4.17.5", "lodash@^4.7.0":
  "integrity" "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="
  "resolved" "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz"
  "version" "4.17.21"

"log-symbols@^4.1.0":
  "integrity" "sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg=="
  "resolved" "https://registry.npmmirror.com/log-symbols/-/log-symbols-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "chalk" "^4.1.0"
    "is-unicode-supported" "^0.1.0"

"log-update@^2.3.0":
  "integrity" "sha512-vlP11XfFGyeNQlmEn9tJ66rEW1coA/79m5z6BCkudjbAGE83uhAcGYrBFwfs3AdLiLzGRusRPAbSPK9xZteCmg=="
  "resolved" "https://registry.npmmirror.com/log-update/-/log-update-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "ansi-escapes" "^3.0.0"
    "cli-cursor" "^2.0.0"
    "wrap-ansi" "^3.0.1"

"log-update@^4.0.0":
  "integrity" "sha512-9fkkDevMefjg0mmzWFBW8YkFP91OrizzkW3diF7CpG+S2EYdy4+TVfGwz1zeF8x7hCx1ovSPTOE9Ngib74qqUg=="
  "resolved" "https://registry.npmmirror.com/log-update/-/log-update-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-escapes" "^4.3.0"
    "cli-cursor" "^3.1.0"
    "slice-ansi" "^4.0.0"
    "wrap-ansi" "^6.2.0"

"loose-envify@^1.0.0":
  "integrity" "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q=="
  "resolved" "https://registry.npmmirror.com/loose-envify/-/loose-envify-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "js-tokens" "^3.0.0 || ^4.0.0"

"lower-case@^2.0.2":
  "integrity" "sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg=="
  "resolved" "https://registry.npmmirror.com/lower-case/-/lower-case-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "tslib" "^2.0.3"

"lru-cache@^4.0.1", "lru-cache@^4.1.2":
  "integrity" "sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g=="
  "resolved" "https://registry.npmmirror.com/lru-cache/-/lru-cache-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "pseudomap" "^1.0.2"
    "yallist" "^2.1.2"

"lru-cache@^5.1.1":
  "integrity" "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w=="
  "resolved" "https://registry.npmmirror.com/lru-cache/-/lru-cache-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "yallist" "^3.0.2"

"lru-cache@^6.0.0":
  "integrity" "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA=="
  "resolved" "https://registry.npmmirror.com/lru-cache/-/lru-cache-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "yallist" "^4.0.0"

"make-dir@^2.1.0":
  "integrity" "sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA=="
  "resolved" "https://registry.npmmirror.com/make-dir/-/make-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pify" "^4.0.1"
    "semver" "^5.6.0"

"make-dir@^3.0.2", "make-dir@^3.1.0":
  "integrity" "sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw=="
  "resolved" "https://registry.npmmirror.com/make-dir/-/make-dir-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "semver" "^6.0.0"

"mdn-data@2.0.14":
  "integrity" "sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow=="
  "resolved" "https://registry.npmmirror.com/mdn-data/-/mdn-data-2.0.14.tgz"
  "version" "2.0.14"

"media-typer@0.3.0":
  "integrity" "sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ=="
  "resolved" "https://registry.npmmirror.com/media-typer/-/media-typer-0.3.0.tgz"
  "version" "0.3.0"

"memfs@^3.4.3":
  "integrity" "sha512-omTM41g3Skpvx5dSYeZIbXKcXoAVc/AoMNwn9TKx++L/gaen/+4TTttmu8ZSch5vfVJ8uJvGbroTsIlslRg6lg=="
  "resolved" "https://registry.npmmirror.com/memfs/-/memfs-3.4.13.tgz"
  "version" "3.4.13"
  dependencies:
    "fs-monkey" "^1.0.3"

"merge-descriptors@1.0.1":
  "integrity" "sha512-cCi6g3/Zr1iqQi6ySbseM1Xvooa98N0w31jzUYrXPX2xqObmFGHJ0tQ5u74H3mVh7wLouTseZyYIq39g8cNp1w=="
  "resolved" "https://registry.npmmirror.com/merge-descriptors/-/merge-descriptors-1.0.1.tgz"
  "version" "1.0.1"

"merge-source-map@^1.1.0":
  "integrity" "sha512-Qkcp7P2ygktpMPh2mCQZaf3jhN6D3Z/qVZHSdWvQ+2Ef5HgRAPBO57A77+ENm0CPx2+1Ce/MYKi3ymqdfuqibw=="
  "resolved" "https://registry.npmmirror.com/merge-source-map/-/merge-source-map-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "source-map" "^0.6.1"

"merge-stream@^2.0.0":
  "integrity" "sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w=="
  "resolved" "https://registry.npmmirror.com/merge-stream/-/merge-stream-2.0.0.tgz"
  "version" "2.0.0"

"merge2@^1.3.0", "merge2@^1.4.1":
  "integrity" "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg=="
  "resolved" "https://registry.npmmirror.com/merge2/-/merge2-1.4.1.tgz"
  "version" "1.4.1"

"methods@~1.1.2":
  "integrity" "sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w=="
  "resolved" "https://registry.npmmirror.com/methods/-/methods-1.1.2.tgz"
  "version" "1.1.2"

"micromatch@^4.0.2", "micromatch@^4.0.4", "micromatch@^4.0.5":
  "integrity" "sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA=="
  "resolved" "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.5.tgz"
  "version" "4.0.5"
  dependencies:
    "braces" "^3.0.2"
    "picomatch" "^2.3.1"

"mime-db@>= 1.43.0 < 2", "mime-db@1.52.0":
  "integrity" "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="
  "resolved" "https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz"
  "version" "1.52.0"

"mime-types@^2.1.12", "mime-types@^2.1.27", "mime-types@^2.1.31", "mime-types@~2.1.17", "mime-types@~2.1.24", "mime-types@~2.1.34":
  "integrity" "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw=="
  "resolved" "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz"
  "version" "2.1.35"
  dependencies:
    "mime-db" "1.52.0"

"mime@^1.4.1", "mime@1.6.0":
  "integrity" "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg=="
  "resolved" "https://registry.npmmirror.com/mime/-/mime-1.6.0.tgz"
  "version" "1.6.0"

"mimic-fn@^1.0.0":
  "integrity" "sha512-jf84uxzwiuiIVKiOLpfYk7N46TSy8ubTonmneY9vrpHNAnp0QBt2BxWV9dO3/j+BoVAb+a5G6YDPW3M5HOdMWQ=="
  "resolved" "https://registry.npmmirror.com/mimic-fn/-/mimic-fn-1.2.0.tgz"
  "version" "1.2.0"

"mimic-fn@^2.1.0":
  "integrity" "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg=="
  "resolved" "https://registry.npmmirror.com/mimic-fn/-/mimic-fn-2.1.0.tgz"
  "version" "2.1.0"

"mini-css-extract-plugin@^2.5.3":
  "integrity" "sha512-9HaR++0mlgom81s95vvNjxkg52n2b5s//3ZTI1EtzFb98awsLSivs2LMsVqnQ3ay0PVhqWcGNyDaTE961FOcjQ=="
  "resolved" "https://registry.npmmirror.com/mini-css-extract-plugin/-/mini-css-extract-plugin-2.7.5.tgz"
  "version" "2.7.5"
  dependencies:
    "schema-utils" "^4.0.0"

"minimalistic-assert@^1.0.0":
  "integrity" "sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A=="
  "resolved" "https://registry.npmmirror.com/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz"
  "version" "1.0.1"

"minimatch@^3.0.4", "minimatch@^3.1.1":
  "integrity" "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw=="
  "resolved" "https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimist@^1.2.0", "minimist@^1.2.5", "minimist@^1.2.6":
  "integrity" "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA=="
  "resolved" "https://registry.npmmirror.com/minimist/-/minimist-1.2.8.tgz"
  "version" "1.2.8"

"minipass@^3.1.1":
  "integrity" "sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw=="
  "resolved" "https://registry.npmmirror.com/minipass/-/minipass-3.3.6.tgz"
  "version" "3.3.6"
  dependencies:
    "yallist" "^4.0.0"

"mkdirp@^0.5.6":
  "integrity" "sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw=="
  "resolved" "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.5.6.tgz"
  "version" "0.5.6"
  dependencies:
    "minimist" "^1.2.6"

"module-alias@^2.2.2":
  "integrity" "sha512-A/78XjoX2EmNvppVWEhM2oGk3x4lLxnkEA4jTbaK97QKSDjkIoOsKQlfylt/d3kKKi596Qy3NP5XrXJ6fZIC9Q=="
  "resolved" "https://registry.npmmirror.com/module-alias/-/module-alias-2.2.2.tgz"
  "version" "2.2.2"

"moment@^2.21.0", "moment@^2.29.4":
  "integrity" "sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w=="
  "resolved" "https://registry.npmmirror.com/moment/-/moment-2.29.4.tgz"
  "version" "2.29.4"

"mrmime@^1.0.0":
  "integrity" "sha512-hzzEagAgDyoU1Q6yg5uI+AorQgdvMCur3FcKf7NhMKWsaYg+RnbTyHRa/9IlLF9rf455MOCtcqqrQQ83pPP7Uw=="
  "resolved" "https://registry.npmmirror.com/mrmime/-/mrmime-1.0.1.tgz"
  "version" "1.0.1"

"ms@^2.1.1", "ms@2.1.3":
  "integrity" "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="
  "resolved" "https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz"
  "version" "2.1.3"

"ms@2.0.0":
  "integrity" "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="
  "resolved" "https://registry.npmmirror.com/ms/-/ms-2.0.0.tgz"
  "version" "2.0.0"

"ms@2.1.2":
  "integrity" "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="
  "resolved" "https://registry.npmmirror.com/ms/-/ms-2.1.2.tgz"
  "version" "2.1.2"

"multicast-dns@^7.2.5":
  "integrity" "sha512-2eznPJP8z2BFLX50tf0LuODrpINqP1RVIm/CObbTcBRITQgmC/TjcREF1NeTBzIcR5XO/ukWo+YHOjBbFwIupg=="
  "resolved" "https://registry.npmmirror.com/multicast-dns/-/multicast-dns-7.2.5.tgz"
  "version" "7.2.5"
  dependencies:
    "dns-packet" "^5.2.2"
    "thunky" "^1.0.2"

"mutationobserver-shim@^0.3.2":
  "integrity" "sha512-oRIDTyZQU96nAiz2AQyngwx1e89iApl2hN5AOYwyxLUB47UYsU3Wv9lJWqH5y/QdiYkc5HQLi23ZNB3fELdHcQ=="
  "resolved" "https://registry.npmmirror.com/mutationobserver-shim/-/mutationobserver-shim-0.3.7.tgz"
  "version" "0.3.7"

"mz@^2.4.0":
  "integrity" "sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q=="
  "resolved" "https://registry.npmmirror.com/mz/-/mz-2.7.0.tgz"
  "version" "2.7.0"
  dependencies:
    "any-promise" "^1.0.0"
    "object-assign" "^4.0.1"
    "thenify-all" "^1.0.0"

"nanoid@^3.3.4":
  "integrity" "sha512-BGcqMMJuToF7i1rt+2PWSNVnWIkGCU78jBG3RxO/bZlnZPK2Cmi2QaffxGO/2RvWi9sL+FAiRiXMgsyxQ1DIDA=="
  "resolved" "https://registry.npmmirror.com/nanoid/-/nanoid-3.3.6.tgz"
  "version" "3.3.6"

"nanopop@^2.1.0":
  "integrity" "sha512-E9JaHcxh3ere8/BEZHAcnuD10RluTSPyTToBvoFWS9/7DcCx6gyKjbn7M7Bx7E1veCxCuY1iO6h4+gdAf1j73Q=="
  "resolved" "https://registry.npmmirror.com/nanopop/-/nanopop-2.2.0.tgz"
  "version" "2.2.0"

"native-request@^1.0.5":
  "integrity" "sha512-uZ5rQaeRn15XmpgE0xoPL8YWqcX90VtCFglYwAgkvKM5e8fog+vePLAhHxuuv/gRkrQxIeh5U3q9sMNUrENqWw=="
  "resolved" "https://registry.npmmirror.com/native-request/-/native-request-1.1.0.tgz"
  "version" "1.1.0"

"natural-compare@^1.4.0":
  "integrity" "sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw=="
  "resolved" "https://registry.npmmirror.com/natural-compare/-/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"negotiator@0.6.3":
  "integrity" "sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg=="
  "resolved" "https://registry.npmmirror.com/negotiator/-/negotiator-0.6.3.tgz"
  "version" "0.6.3"

"neo-async@^2.6.2":
  "integrity" "sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw=="
  "resolved" "https://registry.npmmirror.com/neo-async/-/neo-async-2.6.2.tgz"
  "version" "2.6.2"

"nice-try@^1.0.4":
  "integrity" "sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ=="
  "resolved" "https://registry.npmmirror.com/nice-try/-/nice-try-1.0.5.tgz"
  "version" "1.0.5"

"no-case@^3.0.4":
  "integrity" "sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg=="
  "resolved" "https://registry.npmmirror.com/no-case/-/no-case-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "lower-case" "^2.0.2"
    "tslib" "^2.0.3"

"node-emoji@^1.10.0":
  "integrity" "sha512-wo2DpQkQp7Sjm2A0cq+sN7EHKO6Sl0ctXeBdFZrL9T9+UywORbufTcTZxom8YqpLQt/FqNMUkOpkZrJVYSKD3A=="
  "resolved" "https://registry.npmmirror.com/node-emoji/-/node-emoji-1.11.0.tgz"
  "version" "1.11.0"
  dependencies:
    "lodash" "^4.17.21"

"node-fetch@^2.6.7":
  "integrity" "sha512-DJm/CJkZkRjKKj4Zi4BsKVZh3ValV5IR5s7LVZnW+6YMh0W1BfNA8XSs6DLMGYlId5F3KnA70uu2qepcR08Qqg=="
  "resolved" "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "whatwg-url" "^5.0.0"

"node-forge@^1":
  "integrity" "sha512-dPEtOeMvF9VMcYV/1Wb8CPoVAXtp6MKMlcbAt4ddqmGqUJ6fQZFXkNZNkNlfevtNkGtaSoXf/vNNNSvgrdXwtA=="
  "resolved" "https://registry.npmmirror.com/node-forge/-/node-forge-1.3.1.tgz"
  "version" "1.3.1"

"node-releases@^2.0.8":
  "integrity" "sha512-5GFldHPXVG/YZmFzJvKK2zDSzPKhEp0+ZR5SVaoSag9fsL5YgHbUHDfnG5494ISANDcK4KwPXAx2xqVEydmd7w=="
  "resolved" "https://registry.npmmirror.com/node-releases/-/node-releases-2.0.10.tgz"
  "version" "2.0.10"

"normalize-package-data@^2.5.0":
  "integrity" "sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA=="
  "resolved" "https://registry.npmmirror.com/normalize-package-data/-/normalize-package-data-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "hosted-git-info" "^2.1.4"
    "resolve" "^1.10.0"
    "semver" "2 || 3 || 4 || 5"
    "validate-npm-package-license" "^3.0.1"

"normalize-path@^1.0.0":
  "integrity" "sha512-7WyT0w8jhpDStXRq5836AMmihQwq2nrUVQrgjvUo/p/NZf9uy/MeJ246lBJVmWuYXMlJuG9BNZHF0hWjfTbQUA=="
  "resolved" "https://registry.npmmirror.com/normalize-path/-/normalize-path-1.0.0.tgz"
  "version" "1.0.0"

"normalize-path@^3.0.0", "normalize-path@~3.0.0":
  "integrity" "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA=="
  "resolved" "https://registry.npmmirror.com/normalize-path/-/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"normalize-range@^0.1.2":
  "integrity" "sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA=="
  "resolved" "https://registry.npmmirror.com/normalize-range/-/normalize-range-0.1.2.tgz"
  "version" "0.1.2"

"normalize-url@^6.0.1":
  "integrity" "sha512-DlL+XwOy3NxAQ8xuC0okPgK46iuVNAK01YN7RueYBqqFeGsBjV9XmCAzAdgt+667bCl5kPh9EqKKDwnaPG1I7A=="
  "resolved" "https://registry.npmmirror.com/normalize-url/-/normalize-url-6.1.0.tgz"
  "version" "6.1.0"

"npm-run-path@^2.0.0":
  "integrity" "sha512-lJxZYlT4DW/bRUtFh1MQIWqmLwQfAxnqWG4HhEdjMlkrJYnJn0Jrr2u3mgxqaWsdiBc76TYkTG/mhrnYTuzfHw=="
  "resolved" "https://registry.npmmirror.com/npm-run-path/-/npm-run-path-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "path-key" "^2.0.0"

"npm-run-path@^4.0.1":
  "integrity" "sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw=="
  "resolved" "https://registry.npmmirror.com/npm-run-path/-/npm-run-path-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "path-key" "^3.0.0"

"nprogress@0.2.0":
  "integrity" "sha512-I19aIingLgR1fmhftnbWWO3dXc0hSxqHQHQb3H8m+K3TnEn/iSeTZZOyvKXWqQESMwuUVnatlCnZdLBZZt2VSA=="
  "resolved" "https://registry.npmmirror.com/nprogress/-/nprogress-0.2.0.tgz"
  "version" "0.2.0"

"nth-check@^2.0.1":
  "integrity" "sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w=="
  "resolved" "https://registry.npmmirror.com/nth-check/-/nth-check-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "boolbase" "^1.0.0"

"nwsapi@^2.2.0":
  "integrity" "sha512-90yv+6538zuvUMnN+zCr8LuV6bPFdq50304114vJYJ8RDyK8D5O9Phpbd6SZWgI7PwzmmfN1upeOJlvybDSgCw=="
  "resolved" "https://registry.npmmirror.com/nwsapi/-/nwsapi-2.2.2.tgz"
  "version" "2.2.2"

"object-assign@^4.0.1", "object-assign@4.x":
  "integrity" "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg=="
  "resolved" "https://registry.npmmirror.com/object-assign/-/object-assign-4.1.1.tgz"
  "version" "4.1.1"

"object-inspect@^1.9.0":
  "integrity" "sha512-geUvdk7c+eizMNUDkRpW1wJwgfOiOeHbxBR/hLXK1aT6zmVSO0jsQcs7fj6MGw89jC/cjGfLcNOrtMYtGqm81g=="
  "resolved" "https://registry.npmmirror.com/object-inspect/-/object-inspect-1.12.3.tgz"
  "version" "1.12.3"

"object-is@^1.0.1":
  "integrity" "sha512-3cyDsyHgtmi7I7DfSSI2LDp6SK2lwvtbg0p0R1e0RvTqF5ceGx+K2dfSjm1bKDMVCFEDAQvy+o8c6a7VujOddw=="
  "resolved" "https://registry.npmjs.org/object-is/-/object-is-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"object-keys@^1.1.1":
  "integrity" "sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA=="
  "resolved" "https://registry.npmmirror.com/object-keys/-/object-keys-1.1.1.tgz"
  "version" "1.1.1"

"object.assign@^4.1.0":
  "integrity" "sha512-1mxKf0e58bvyjSCtKYY4sRe9itRk3PJpquJOjeIkz885CczcI4IvJJDLPS72oowuSh+pBxUFROpX+TU++hxhZQ=="
  "resolved" "https://registry.npmmirror.com/object.assign/-/object.assign-4.1.4.tgz"
  "version" "4.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "has-symbols" "^1.0.3"
    "object-keys" "^1.1.1"

"obuf@^1.0.0", "obuf@^1.1.2":
  "integrity" "sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg=="
  "resolved" "https://registry.npmmirror.com/obuf/-/obuf-1.1.2.tgz"
  "version" "1.1.2"

"omit.js@^1.0.0":
  "integrity" "sha512-/QPc6G2NS+8d4L/cQhbk6Yit1WTB6Us2g84A7A/1+w9d/eRGHyEqC5kkQtHVoHZ5NFWGG7tUGgrhVZwgZanKrQ=="
  "resolved" "https://registry.npmmirror.com/omit.js/-/omit.js-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "babel-runtime" "^6.23.0"

"on-finished@2.4.1":
  "integrity" "sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg=="
  "resolved" "https://registry.npmmirror.com/on-finished/-/on-finished-2.4.1.tgz"
  "version" "2.4.1"
  dependencies:
    "ee-first" "1.1.1"

"on-headers@~1.0.2":
  "integrity" "sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA=="
  "resolved" "https://registry.npmmirror.com/on-headers/-/on-headers-1.0.2.tgz"
  "version" "1.0.2"

"once@^1.3.0", "once@^1.3.1", "once@^1.4.0":
  "integrity" "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w=="
  "resolved" "https://registry.npmmirror.com/once/-/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"onetime@^2.0.0":
  "integrity" "sha512-oyyPpiMaKARvvcgip+JV+7zci5L8D1W9RZIz2l1o08AM3pfspitVWnPt3mzHcBPp12oYMTy0pqrFs/C+m3EwsQ=="
  "resolved" "https://registry.npmmirror.com/onetime/-/onetime-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "mimic-fn" "^1.0.0"

"onetime@^5.1.0", "onetime@^5.1.2":
  "integrity" "sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg=="
  "resolved" "https://registry.npmmirror.com/onetime/-/onetime-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "mimic-fn" "^2.1.0"

"open@^8.0.2", "open@^8.0.9":
  "integrity" "sha512-7x81NCL719oNbsq/3mh+hVrAWmFuEYUqrq/Iw3kUzH8ReypT9QQ0BLoJS7/G9k6N81XjW4qHWtjWwe/9eLy1EQ=="
  "resolved" "https://registry.npmmirror.com/open/-/open-8.4.2.tgz"
  "version" "8.4.2"
  dependencies:
    "define-lazy-prop" "^2.0.0"
    "is-docker" "^2.1.1"
    "is-wsl" "^2.2.0"

"opener@^1.5.2":
  "integrity" "sha512-ur5UIdyw5Y7yEj9wLzhqXiy6GZ3Mwx0yGI+5sMn2r0N0v3cKJvUmFH5yPP+WXh9e0xfyzyJX95D8l088DNFj7A=="
  "resolved" "https://registry.npmmirror.com/opener/-/opener-1.5.2.tgz"
  "version" "1.5.2"

"optionator@^0.8.1":
  "integrity" "sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA=="
  "resolved" "https://registry.npmmirror.com/optionator/-/optionator-0.8.3.tgz"
  "version" "0.8.3"
  dependencies:
    "deep-is" "~0.1.3"
    "fast-levenshtein" "~2.0.6"
    "levn" "~0.3.0"
    "prelude-ls" "~1.1.2"
    "type-check" "~0.3.2"
    "word-wrap" "~1.2.3"

"optionator@^0.9.1":
  "integrity" "sha512-74RlY5FCnhq4jRxVUPKDaRwrVNXMqsGsiW6AJw4XK8hmtm10wC0ypZBLw5IIp85NZMr91+qd1RvvENwg7jjRFw=="
  "resolved" "https://registry.npmmirror.com/optionator/-/optionator-0.9.1.tgz"
  "version" "0.9.1"
  dependencies:
    "deep-is" "^0.1.3"
    "fast-levenshtein" "^2.0.6"
    "levn" "^0.4.1"
    "prelude-ls" "^1.2.1"
    "type-check" "^0.4.0"
    "word-wrap" "^1.2.3"

"ora@^5.3.0":
  "integrity" "sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ=="
  "resolved" "https://registry.npmmirror.com/ora/-/ora-5.4.1.tgz"
  "version" "5.4.1"
  dependencies:
    "bl" "^4.1.0"
    "chalk" "^4.1.0"
    "cli-cursor" "^3.1.0"
    "cli-spinners" "^2.5.0"
    "is-interactive" "^1.0.0"
    "is-unicode-supported" "^0.1.0"
    "log-symbols" "^4.1.0"
    "strip-ansi" "^6.0.0"
    "wcwidth" "^1.0.1"

"p-finally@^1.0.0":
  "integrity" "sha512-LICb2p9CB7FS+0eR1oqWnHhp0FljGLZCWBE9aix0Uye9W8LTQPwMTYVGWQWIw9RdQiDg4+epXQODwIYJtSJaow=="
  "resolved" "https://registry.npmmirror.com/p-finally/-/p-finally-1.0.0.tgz"
  "version" "1.0.0"

"p-limit@^2.2.0":
  "integrity" "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w=="
  "resolved" "https://registry.npmmirror.com/p-limit/-/p-limit-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "p-try" "^2.0.0"

"p-locate@^4.1.0":
  "integrity" "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A=="
  "resolved" "https://registry.npmmirror.com/p-locate/-/p-locate-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "p-limit" "^2.2.0"

"p-map@^4.0.0":
  "integrity" "sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ=="
  "resolved" "https://registry.npmmirror.com/p-map/-/p-map-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "aggregate-error" "^3.0.0"

"p-retry@^4.5.0":
  "integrity" "sha512-312Id396EbJdvRONlngUx0NydfrIQ5lsYu0znKVUzVvArzEIt08V1qhtyESbGVd1FGX7UKtiFp5uwKZdM8wIuQ=="
  "resolved" "https://registry.npmmirror.com/p-retry/-/p-retry-4.6.2.tgz"
  "version" "4.6.2"
  dependencies:
    "@types/retry" "0.12.0"
    "retry" "^0.13.1"

"p-try@^2.0.0":
  "integrity" "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ=="
  "resolved" "https://registry.npmmirror.com/p-try/-/p-try-2.2.0.tgz"
  "version" "2.2.0"

"param-case@^3.0.4":
  "integrity" "sha512-RXlj7zCYokReqWpOPH9oYivUzLYZ5vAPIfEmCTNViosC78F8F0H9y7T7gG2M39ymgutxF5gcFEsyZQSph9Bp3A=="
  "resolved" "https://registry.npmmirror.com/param-case/-/param-case-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "dot-case" "^3.0.4"
    "tslib" "^2.0.3"

"parchment@^1.1.4":
  "integrity" "sha512-J5FBQt/pM2inLzg4hEWmzQx/8h8D0CiDxaG3vyp9rKrQRSDgBlhjdP5jQGgosEajXPSQouXGHOmVdgo7QmJuOg=="
  "resolved" "https://registry.npmjs.org/parchment/-/parchment-1.1.4.tgz"
  "version" "1.1.4"

"parent-module@^1.0.0":
  "integrity" "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g=="
  "resolved" "https://registry.npmmirror.com/parent-module/-/parent-module-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "callsites" "^3.0.0"

"parse-json@^5.0.0":
  "integrity" "sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg=="
  "resolved" "https://registry.npmmirror.com/parse-json/-/parse-json-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "error-ex" "^1.3.1"
    "json-parse-even-better-errors" "^2.3.0"
    "lines-and-columns" "^1.1.6"

"parse5-htmlparser2-tree-adapter@^6.0.0":
  "integrity" "sha512-qPuWvbLgvDGilKc5BoicRovlT4MtYT6JfJyBOMDsKoiT+GiuP5qyrPCnR9HcPECIJJmZh5jRndyNThnhhb/vlA=="
  "resolved" "https://registry.npmmirror.com/parse5-htmlparser2-tree-adapter/-/parse5-htmlparser2-tree-adapter-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "parse5" "^6.0.1"

"parse5@^5.1.1":
  "integrity" "sha512-ugq4DFI0Ptb+WWjAdOK16+u/nHfiIrcE+sh8kZMaM0WllQKLI9rOUq6c2b7cwPkXdzfQESqvoqK6ug7U/Yyzug=="
  "resolved" "https://registry.npmmirror.com/parse5/-/parse5-5.1.1.tgz"
  "version" "5.1.1"

"parse5@^6.0.1", "parse5@6.0.1":
  "integrity" "sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw=="
  "resolved" "https://registry.npmmirror.com/parse5/-/parse5-6.0.1.tgz"
  "version" "6.0.1"

"parseurl@~1.3.2", "parseurl@~1.3.3":
  "integrity" "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ=="
  "resolved" "https://registry.npmmirror.com/parseurl/-/parseurl-1.3.3.tgz"
  "version" "1.3.3"

"pascal-case@^3.1.2":
  "integrity" "sha512-uWlGT3YSnK9x3BQJaOdcZwrnV6hPpd8jFH1/ucpiLRPh/2zCVJKS19E4GvYHvaCcACn3foXZ0cLB9Wrx1KGe5g=="
  "resolved" "https://registry.npmmirror.com/pascal-case/-/pascal-case-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "no-case" "^3.0.4"
    "tslib" "^2.0.3"

"path-exists@^4.0.0":
  "integrity" "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w=="
  "resolved" "https://registry.npmmirror.com/path-exists/-/path-exists-4.0.0.tgz"
  "version" "4.0.0"

"path-is-absolute@^1.0.0":
  "integrity" "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg=="
  "resolved" "https://registry.npmmirror.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-key@^2.0.0", "path-key@^2.0.1":
  "integrity" "sha512-fEHGKCSmUSDPv4uoj8AlD+joPlq3peND+HRYyxFz4KPw4z926S/b8rIuFs2FYJg3BwsxJf6A9/3eIdLaYC+9Dw=="
  "resolved" "https://registry.npmmirror.com/path-key/-/path-key-2.0.1.tgz"
  "version" "2.0.1"

"path-key@^3.0.0":
  "integrity" "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q=="
  "resolved" "https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz"
  "version" "3.1.1"

"path-key@^3.1.0":
  "integrity" "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q=="
  "resolved" "https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz"
  "version" "3.1.1"

"path-parse@^1.0.7":
  "integrity" "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw=="
  "resolved" "https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz"
  "version" "1.0.7"

"path-to-regexp@0.1.7":
  "integrity" "sha512-5DFkuoqlv1uYQKxy8omFBeJPQcdoE07Kv2sferDCrAq1ohOU+MSDswDIbnx3YAM60qIOnYa53wBhXW0EbMonrQ=="
  "resolved" "https://registry.npmmirror.com/path-to-regexp/-/path-to-regexp-0.1.7.tgz"
  "version" "0.1.7"

"path-type@^4.0.0":
  "integrity" "sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw=="
  "resolved" "https://registry.npmmirror.com/path-type/-/path-type-4.0.0.tgz"
  "version" "4.0.0"

"performance-now@^2.1.0":
  "integrity" "sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow=="
  "resolved" "https://registry.npmmirror.com/performance-now/-/performance-now-2.1.0.tgz"
  "version" "2.1.0"

"picocolors@^0.2.1":
  "integrity" "sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA=="
  "resolved" "https://registry.npmmirror.com/picocolors/-/picocolors-0.2.1.tgz"
  "version" "0.2.1"

"picocolors@^1.0.0":
  "integrity" "sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ=="
  "resolved" "https://registry.npmmirror.com/picocolors/-/picocolors-1.0.0.tgz"
  "version" "1.0.0"

"picomatch@^2.0.4", "picomatch@^2.2.1", "picomatch@^2.3.1":
  "integrity" "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="
  "resolved" "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz"
  "version" "2.3.1"

"pify@^4.0.1":
  "integrity" "sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g=="
  "resolved" "https://registry.npmmirror.com/pify/-/pify-4.0.1.tgz"
  "version" "4.0.1"

"pkg-dir@^4.1.0":
  "integrity" "sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ=="
  "resolved" "https://registry.npmmirror.com/pkg-dir/-/pkg-dir-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "find-up" "^4.0.0"

"please-upgrade-node@^3.2.0":
  "integrity" "sha512-gQR3WpIgNIKwBMVLkpMUeR3e1/E1y42bqDQZfql+kDeXd8COYfM8PQA4X6y7a8u9Ua9FHmsrrmirW2vHs45hWg=="
  "resolved" "https://registry.npmmirror.com/please-upgrade-node/-/please-upgrade-node-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "semver-compare" "^1.0.0"

"portfinder@^1.0.26":
  "integrity" "sha512-on2ZJVVDXRADWE6jnQaX0ioEylzgBpQk8r55NE4wjXW1ZxO+BgDlY6DXwj20i0V8eB4SenDQ00WEaxfiIQPcxg=="
  "resolved" "https://registry.npmmirror.com/portfinder/-/portfinder-1.0.32.tgz"
  "version" "1.0.32"
  dependencies:
    "async" "^2.6.4"
    "debug" "^3.2.7"
    "mkdirp" "^0.5.6"

"postcss-calc@^8.2.3":
  "integrity" "sha512-SmWMSJmB8MRnnULldx0lQIyhSNvuDl9HfrZkaqqE/WHAhToYsAvDq+yAsA/kIyINDszOp3Rh0GFoNuH5Ypsm3Q=="
  "resolved" "https://registry.npmmirror.com/postcss-calc/-/postcss-calc-8.2.4.tgz"
  "version" "8.2.4"
  dependencies:
    "postcss-selector-parser" "^6.0.9"
    "postcss-value-parser" "^4.2.0"

"postcss-colormin@^5.3.1":
  "integrity" "sha512-UsWQG0AqTFQmpBegeLLc1+c3jIqBNB0zlDGRWR+dQ3pRKJL1oeMzyqmH3o2PIfn9MBdNrVPWhDbT769LxCTLJQ=="
  "resolved" "https://registry.npmmirror.com/postcss-colormin/-/postcss-colormin-5.3.1.tgz"
  "version" "5.3.1"
  dependencies:
    "browserslist" "^4.21.4"
    "caniuse-api" "^3.0.0"
    "colord" "^2.9.1"
    "postcss-value-parser" "^4.2.0"

"postcss-convert-values@^5.1.3":
  "integrity" "sha512-82pC1xkJZtcJEfiLw6UXnXVXScgtBrjlO5CBmuDQc+dlb88ZYheFsjTn40+zBVi3DkfF7iezO0nJUPLcJK3pvA=="
  "resolved" "https://registry.npmmirror.com/postcss-convert-values/-/postcss-convert-values-5.1.3.tgz"
  "version" "5.1.3"
  dependencies:
    "browserslist" "^4.21.4"
    "postcss-value-parser" "^4.2.0"

"postcss-discard-comments@^5.1.2":
  "integrity" "sha512-+L8208OVbHVF2UQf1iDmRcbdjJkuBF6IS29yBDSiWUIzpYaAhtNl6JYnYm12FnkeCwQqF5LeklOu6rAqgfBZqQ=="
  "resolved" "https://registry.npmmirror.com/postcss-discard-comments/-/postcss-discard-comments-5.1.2.tgz"
  "version" "5.1.2"

"postcss-discard-duplicates@^5.1.0":
  "integrity" "sha512-zmX3IoSI2aoenxHV6C7plngHWWhUOV3sP1T8y2ifzxzbtnuhk1EdPwm0S1bIUNaJ2eNbWeGLEwzw8huPD67aQw=="
  "resolved" "https://registry.npmmirror.com/postcss-discard-duplicates/-/postcss-discard-duplicates-5.1.0.tgz"
  "version" "5.1.0"

"postcss-discard-empty@^5.1.1":
  "integrity" "sha512-zPz4WljiSuLWsI0ir4Mcnr4qQQ5e1Ukc3i7UfE2XcrwKK2LIPIqE5jxMRxO6GbI3cv//ztXDsXwEWT3BHOGh3A=="
  "resolved" "https://registry.npmmirror.com/postcss-discard-empty/-/postcss-discard-empty-5.1.1.tgz"
  "version" "5.1.1"

"postcss-discard-overridden@^5.1.0":
  "integrity" "sha512-21nOL7RqWR1kasIVdKs8HNqQJhFxLsyRfAnUDm4Fe4t4mCWL9OJiHvlHPjcd8zc5Myu89b/7wZDnOSjFgeWRtw=="
  "resolved" "https://registry.npmmirror.com/postcss-discard-overridden/-/postcss-discard-overridden-5.1.0.tgz"
  "version" "5.1.0"

"postcss-loader@^6.1.1":
  "integrity" "sha512-WbbYpmAaKcux/P66bZ40bpWsBucjx/TTgVVzRZ9yUO8yQfVBlameJ0ZGVaPfH64hNSBh63a+ICP5nqOpBA0w+Q=="
  "resolved" "https://registry.npmmirror.com/postcss-loader/-/postcss-loader-6.2.1.tgz"
  "version" "6.2.1"
  dependencies:
    "cosmiconfig" "^7.0.0"
    "klona" "^2.0.5"
    "semver" "^7.3.5"

"postcss-merge-longhand@^5.1.7":
  "integrity" "sha512-YCI9gZB+PLNskrK0BB3/2OzPnGhPkBEwmwhfYk1ilBHYVAZB7/tkTHFBAnCrvBBOmeYyMYw3DMjT55SyxMBzjQ=="
  "resolved" "https://registry.npmmirror.com/postcss-merge-longhand/-/postcss-merge-longhand-5.1.7.tgz"
  "version" "5.1.7"
  dependencies:
    "postcss-value-parser" "^4.2.0"
    "stylehacks" "^5.1.1"

"postcss-merge-rules@^5.1.4":
  "integrity" "sha512-0R2IuYpgU93y9lhVbO/OylTtKMVcHb67zjWIfCiKR9rWL3GUk1677LAqD/BcHizukdZEjT8Ru3oHRoAYoJy44g=="
  "resolved" "https://registry.npmmirror.com/postcss-merge-rules/-/postcss-merge-rules-5.1.4.tgz"
  "version" "5.1.4"
  dependencies:
    "browserslist" "^4.21.4"
    "caniuse-api" "^3.0.0"
    "cssnano-utils" "^3.1.0"
    "postcss-selector-parser" "^6.0.5"

"postcss-minify-font-values@^5.1.0":
  "integrity" "sha512-el3mYTgx13ZAPPirSVsHqFzl+BBBDrXvbySvPGFnQcTI4iNslrPaFq4muTkLZmKlGk4gyFAYUBMH30+HurREyA=="
  "resolved" "https://registry.npmmirror.com/postcss-minify-font-values/-/postcss-minify-font-values-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-minify-gradients@^5.1.1":
  "integrity" "sha512-VGvXMTpCEo4qHTNSa9A0a3D+dxGFZCYwR6Jokk+/3oB6flu2/PnPXAh2x7x52EkY5xlIHLm+Le8tJxe/7TNhzw=="
  "resolved" "https://registry.npmmirror.com/postcss-minify-gradients/-/postcss-minify-gradients-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "colord" "^2.9.1"
    "cssnano-utils" "^3.1.0"
    "postcss-value-parser" "^4.2.0"

"postcss-minify-params@^5.1.4":
  "integrity" "sha512-+mePA3MgdmVmv6g+30rn57USjOGSAyuxUmkfiWpzalZ8aiBkdPYjXWtHuwJGm1v5Ojy0Z0LaSYhHaLJQB0P8Jw=="
  "resolved" "https://registry.npmmirror.com/postcss-minify-params/-/postcss-minify-params-5.1.4.tgz"
  "version" "5.1.4"
  dependencies:
    "browserslist" "^4.21.4"
    "cssnano-utils" "^3.1.0"
    "postcss-value-parser" "^4.2.0"

"postcss-minify-selectors@^5.2.1":
  "integrity" "sha512-nPJu7OjZJTsVUmPdm2TcaiohIwxP+v8ha9NehQ2ye9szv4orirRU3SDdtUmKH+10nzn0bAyOXZ0UEr7OpvLehg=="
  "resolved" "https://registry.npmmirror.com/postcss-minify-selectors/-/postcss-minify-selectors-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "postcss-selector-parser" "^6.0.5"

"postcss-modules-extract-imports@^3.0.0":
  "integrity" "sha512-bdHleFnP3kZ4NYDhuGlVK+CMrQ/pqUm8bx/oGL93K6gVwiclvX5x0n76fYMKuIGKzlABOy13zsvqjb0f92TEXw=="
  "resolved" "https://registry.npmmirror.com/postcss-modules-extract-imports/-/postcss-modules-extract-imports-3.0.0.tgz"
  "version" "3.0.0"

"postcss-modules-local-by-default@^4.0.0":
  "integrity" "sha512-sT7ihtmGSF9yhm6ggikHdV0hlziDTX7oFoXtuVWeDd3hHObNkcHRo9V3yg7vCAY7cONyxJC/XXCmmiHHcvX7bQ=="
  "resolved" "https://registry.npmmirror.com/postcss-modules-local-by-default/-/postcss-modules-local-by-default-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "icss-utils" "^5.0.0"
    "postcss-selector-parser" "^6.0.2"
    "postcss-value-parser" "^4.1.0"

"postcss-modules-scope@^3.0.0":
  "integrity" "sha512-hncihwFA2yPath8oZ15PZqvWGkWf+XUfQgUGamS4LqoP1anQLOsOJw0vr7J7IwLpoY9fatA2qiGUGmuZL0Iqlg=="
  "resolved" "https://registry.npmmirror.com/postcss-modules-scope/-/postcss-modules-scope-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "postcss-selector-parser" "^6.0.4"

"postcss-modules-values@^4.0.0":
  "integrity" "sha512-RDxHkAiEGI78gS2ofyvCsu7iycRv7oqw5xMWn9iMoR0N/7mf9D50ecQqUo5BZ9Zh2vH4bCUR/ktCqbB9m8vJjQ=="
  "resolved" "https://registry.npmmirror.com/postcss-modules-values/-/postcss-modules-values-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "icss-utils" "^5.0.0"

"postcss-normalize-charset@^5.1.0":
  "integrity" "sha512-mSgUJ+pd/ldRGVx26p2wz9dNZ7ji6Pn8VWBajMXFf8jk7vUoSrZ2lt/wZR7DtlZYKesmZI680qjr2CeFF2fbUg=="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-charset/-/postcss-normalize-charset-5.1.0.tgz"
  "version" "5.1.0"

"postcss-normalize-display-values@^5.1.0":
  "integrity" "sha512-WP4KIM4o2dazQXWmFaqMmcvsKmhdINFblgSeRgn8BJ6vxaMyaJkwAzpPpuvSIoG/rmX3M+IrRZEz2H0glrQNEA=="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-display-values/-/postcss-normalize-display-values-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-normalize-positions@^5.1.1":
  "integrity" "sha512-6UpCb0G4eofTCQLFVuI3EVNZzBNPiIKcA1AKVka+31fTVySphr3VUgAIULBhxZkKgwLImhzMR2Bw1ORK+37INg=="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-positions/-/postcss-normalize-positions-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-normalize-repeat-style@^5.1.1":
  "integrity" "sha512-mFpLspGWkQtBcWIRFLmewo8aC3ImN2i/J3v8YCFUwDnPu3Xz4rLohDO26lGjwNsQxB3YF0KKRwspGzE2JEuS0g=="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-repeat-style/-/postcss-normalize-repeat-style-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-normalize-string@^5.1.0":
  "integrity" "sha512-oYiIJOf4T9T1N4i+abeIc7Vgm/xPCGih4bZz5Nm0/ARVJ7K6xrDlLwvwqOydvyL3RHNf8qZk6vo3aatiw/go3w=="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-string/-/postcss-normalize-string-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-normalize-timing-functions@^5.1.0":
  "integrity" "sha512-DOEkzJ4SAXv5xkHl0Wa9cZLF3WCBhF3o1SKVxKQAa+0pYKlueTpCgvkFAHfk+Y64ezX9+nITGrDZeVGgITJXjg=="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-timing-functions/-/postcss-normalize-timing-functions-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-normalize-unicode@^5.1.1":
  "integrity" "sha512-qnCL5jzkNUmKVhZoENp1mJiGNPcsJCs1aaRmURmeJGES23Z/ajaln+EPTD+rBeNkSryI+2WTdW+lwcVdOikrpA=="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-unicode/-/postcss-normalize-unicode-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "browserslist" "^4.21.4"
    "postcss-value-parser" "^4.2.0"

"postcss-normalize-url@^5.1.0":
  "integrity" "sha512-5upGeDO+PVthOxSmds43ZeMeZfKH+/DKgGRD7TElkkyS46JXAUhMzIKiCa7BabPeIy3AQcTkXwVVN7DbqsiCew=="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-url/-/postcss-normalize-url-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "normalize-url" "^6.0.1"
    "postcss-value-parser" "^4.2.0"

"postcss-normalize-whitespace@^5.1.1":
  "integrity" "sha512-83ZJ4t3NUDETIHTa3uEg6asWjSBYL5EdkVB0sDncx9ERzOKBVJIUeDO9RyA9Zwtig8El1d79HBp0JEi8wvGQnA=="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-whitespace/-/postcss-normalize-whitespace-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-ordered-values@^5.1.3":
  "integrity" "sha512-9UO79VUhPwEkzbb3RNpqqghc6lcYej1aveQteWY+4POIwlqkYE21HKWaLDF6lWNuqCobEAyTovVhtI32Rbv2RQ=="
  "resolved" "https://registry.npmmirror.com/postcss-ordered-values/-/postcss-ordered-values-5.1.3.tgz"
  "version" "5.1.3"
  dependencies:
    "cssnano-utils" "^3.1.0"
    "postcss-value-parser" "^4.2.0"

"postcss-reduce-initial@^5.1.2":
  "integrity" "sha512-dE/y2XRaqAi6OvjzD22pjTUQ8eOfc6m/natGHgKFBK9DxFmIm69YmaRVQrGgFlEfc1HePIurY0TmDeROK05rIg=="
  "resolved" "https://registry.npmmirror.com/postcss-reduce-initial/-/postcss-reduce-initial-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "browserslist" "^4.21.4"
    "caniuse-api" "^3.0.0"

"postcss-reduce-transforms@^5.1.0":
  "integrity" "sha512-2fbdbmgir5AvpW9RLtdONx1QoYG2/EtqpNQbFASDlixBbAYuTcJ0dECwlqNqH7VbaUnEnh8SrxOe2sRIn24XyQ=="
  "resolved" "https://registry.npmmirror.com/postcss-reduce-transforms/-/postcss-reduce-transforms-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-selector-parser@^6.0.2", "postcss-selector-parser@^6.0.4", "postcss-selector-parser@^6.0.5", "postcss-selector-parser@^6.0.9":
  "integrity" "sha512-zbARubNdogI9j7WY4nQJBiNqQf3sLS3wCP4WfOidu+p28LofJqDH1tcXypGrcmMHhDk2t9wGhCsYe/+szLTy1g=="
  "resolved" "https://registry.npmmirror.com/postcss-selector-parser/-/postcss-selector-parser-6.0.11.tgz"
  "version" "6.0.11"
  dependencies:
    "cssesc" "^3.0.0"
    "util-deprecate" "^1.0.2"

"postcss-svgo@^5.1.0":
  "integrity" "sha512-D75KsH1zm5ZrHyxPakAxJWtkyXew5qwS70v56exwvw542d9CRtTo78K0WeFxZB4G7JXKKMbEZtZayTGdIky/eA=="
  "resolved" "https://registry.npmmirror.com/postcss-svgo/-/postcss-svgo-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "postcss-value-parser" "^4.2.0"
    "svgo" "^2.7.0"

"postcss-unique-selectors@^5.1.1":
  "integrity" "sha512-5JiODlELrz8L2HwxfPnhOWZYWDxVHWL83ufOv84NrcgipI7TaeRsatAhK4Tr2/ZiYldpK/wBvw5BD3qfaK96GA=="
  "resolved" "https://registry.npmmirror.com/postcss-unique-selectors/-/postcss-unique-selectors-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "postcss-selector-parser" "^6.0.5"

"postcss-value-parser@^4.1.0", "postcss-value-parser@^4.2.0":
  "integrity" "sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ=="
  "resolved" "https://registry.npmmirror.com/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  "version" "4.2.0"

"postcss@^7.0.0 || ^8.0.1", "postcss@^8.0.9", "postcss@^8.1.0", "postcss@^8.2.15", "postcss@^8.2.2", "postcss@^8.2.6", "postcss@^8.3.5", "postcss@^8.4.14", "postcss@^8.4.19":
  "integrity" "sha512-tP7u/Sn/dVxK2NnruI4H9BG+x+Wxz6oeZ1cJ8P6G/PZY0IKk4k/63TDsQf2kQq3+qoJeLm2kIBUNlZe3zgb4Zg=="
  "resolved" "https://registry.npmmirror.com/postcss/-/postcss-8.4.21.tgz"
  "version" "8.4.21"
  dependencies:
    "nanoid" "^3.3.4"
    "picocolors" "^1.0.0"
    "source-map-js" "^1.0.2"

"postcss@^7.0.36":
  "integrity" "sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA=="
  "resolved" "https://registry.npmmirror.com/postcss/-/postcss-7.0.39.tgz"
  "version" "7.0.39"
  dependencies:
    "picocolors" "^0.2.1"
    "source-map" "^0.6.1"

"prelude-ls@^1.2.1":
  "integrity" "sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g=="
  "resolved" "https://registry.npmmirror.com/prelude-ls/-/prelude-ls-1.2.1.tgz"
  "version" "1.2.1"

"prelude-ls@~1.1.2":
  "integrity" "sha512-ESF23V4SKG6lVSGZgYNpbsiaAkdab6ZgOxe52p7+Kid3W3u3bxR4Vfd/o21dmN7jSt0IwgZ4v5MUd26FEtXE9w=="
  "resolved" "https://registry.npmmirror.com/prelude-ls/-/prelude-ls-1.1.2.tgz"
  "version" "1.1.2"

"prettier-linter-helpers@^1.0.0":
  "integrity" "sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w=="
  "resolved" "https://registry.npmmirror.com/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "fast-diff" "^1.1.2"

"prettier@^1.18.2 || ^2.0.0", "prettier@>=2.0.0", "prettier@2.8.7":
  "integrity" "sha512-yPngTo3aXUUmyuTjeTUT75txrf+aMh9FiD7q9ZE/i6r0bPb22g4FsE6Y338PQX1bmfy08i9QQCB7/rcUAVntfw=="
  "resolved" "https://registry.npmmirror.com/prettier/-/prettier-2.8.7.tgz"
  "version" "2.8.7"

"pretty-error@^4.0.0":
  "integrity" "sha512-AoJ5YMAcXKYxKhuJGdcvse+Voc6v1RgnsR3nWcYU7q4t6z0Q6T86sv5Zq8VIRbOWWFpvdGE83LtdSMNd+6Y0xw=="
  "resolved" "https://registry.npmmirror.com/pretty-error/-/pretty-error-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "lodash" "^4.17.20"
    "renderkid" "^3.0.0"

"printj@~1.1.0":
  "integrity" "sha512-zA2SmoLaxZyArQTOPj5LXecR+RagfPSU5Kw1qP+jkWeNlrq+eJZyY2oS68SU1Z/7/myXM4lo9716laOFAVStCQ=="
  "resolved" "https://registry.npmmirror.com/printj/-/printj-1.1.2.tgz"
  "version" "1.1.2"

"process-nextick-args@~2.0.0":
  "integrity" "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag=="
  "resolved" "https://registry.npmmirror.com/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  "version" "2.0.1"

"progress-webpack-plugin@^1.0.12":
  "integrity" "sha512-sdiHuuKOzELcBANHfrupYo+r99iPRyOnw15qX+rNlVUqXGfjXdH4IgxriKwG1kNJwVswKQHMdj1hYZMcb9jFaA=="
  "resolved" "https://registry.npmmirror.com/progress-webpack-plugin/-/progress-webpack-plugin-1.0.16.tgz"
  "version" "1.0.16"
  dependencies:
    "chalk" "^2.1.0"
    "figures" "^2.0.0"
    "log-update" "^2.3.0"

"progress@^2.0.0":
  "integrity" "sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA=="
  "resolved" "https://registry.npmmirror.com/progress/-/progress-2.0.3.tgz"
  "version" "2.0.3"

"proxy-addr@~2.0.7":
  "integrity" "sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg=="
  "resolved" "https://registry.npmmirror.com/proxy-addr/-/proxy-addr-2.0.7.tgz"
  "version" "2.0.7"
  dependencies:
    "forwarded" "0.2.0"
    "ipaddr.js" "1.9.1"

"proxy-from-env@^1.1.0":
  "integrity" "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg=="
  "resolved" "https://registry.npmmirror.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz"
  "version" "1.1.0"

"prr@~1.0.1":
  "integrity" "sha512-yPw4Sng1gWghHQWj0B3ZggWUm4qVbPwPFcRG8KyxiU7J2OHFSoEHKS+EZ3fv5l1t9CyCiop6l/ZYeWbrgoQejw=="
  "resolved" "https://registry.npmmirror.com/prr/-/prr-1.0.1.tgz"
  "version" "1.0.1"

"pseudomap@^1.0.2":
  "integrity" "sha512-b/YwNhb8lk1Zz2+bXXpS/LK9OisiZZ1SNsSLxN1x2OXVEhW2Ckr/7mWE5vrC1ZTiJlD9g19jWszTmJsB+oEpFQ=="
  "resolved" "https://registry.npmmirror.com/pseudomap/-/pseudomap-1.0.2.tgz"
  "version" "1.0.2"

"psl@^1.1.33":
  "integrity" "sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag=="
  "resolved" "https://registry.npmmirror.com/psl/-/psl-1.9.0.tgz"
  "version" "1.9.0"

"pump@^3.0.0":
  "integrity" "sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww=="
  "resolved" "https://registry.npmmirror.com/pump/-/pump-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"punycode@^2.1.0", "punycode@^2.1.1":
  "integrity" "sha512-rRV+zQD8tVFys26lAGR9WUuS4iUAngJScM+ZRSKtvl5tKeZ2t5bvdNFdNHBW9FWR4guGHlgmsZ1G7BSm2wTbuA=="
  "resolved" "https://registry.npmmirror.com/punycode/-/punycode-2.3.0.tgz"
  "version" "2.3.0"

"qs@6.11.0":
  "integrity" "sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q=="
  "resolved" "https://registry.npmmirror.com/qs/-/qs-6.11.0.tgz"
  "version" "6.11.0"
  dependencies:
    "side-channel" "^1.0.4"

"querystringify@^2.1.1":
  "integrity" "sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ=="
  "resolved" "https://registry.npmmirror.com/querystringify/-/querystringify-2.2.0.tgz"
  "version" "2.2.0"

"queue-microtask@^1.2.2":
  "integrity" "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A=="
  "resolved" "https://registry.npmmirror.com/queue-microtask/-/queue-microtask-1.2.3.tgz"
  "version" "1.2.3"

"quill-delta@^3.6.2":
  "integrity" "sha512-wdIGBlcX13tCHOXGMVnnTVFtGRLoP0imqxM696fIPwIf5ODIYUHIvHbZcyvGlZFiFhK5XzDC2lpjbxRhnM05Tg=="
  "resolved" "https://registry.npmjs.org/quill-delta/-/quill-delta-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "deep-equal" "^1.0.1"
    "extend" "^3.0.2"
    "fast-diff" "1.1.2"

"quill-image-drop-module@^1.0.3":
  "integrity" "sha512-HP0Y2kb3nQk1QbRKZzEe1j3mArRQerN5B/U/MlXrOnxmhy3m/xYmVv0YoE13vWnGnBOIcoXGJ/9fi7l6AwsP8Q=="
  "resolved" "https://npm.bangdao-tech.com/quill-image-drop-module/-/quill-image-drop-module-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "quill" "^1.2.2"

"quill-image-resize-module@^3.0.0":
  "integrity" "sha512-1TZBnUxU/WIx5dPyVjQ9yN7C6mLZSp04HyWBEMqT320DIq4MW4JgzlOPDZX5ZpBM3bU6sacU4kTLUc8VgYQZYw=="
  "resolved" "https://npm.bangdao-tech.com/quill-image-resize-module/-/quill-image-resize-module-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "lodash" "^4.17.4"
    "quill" "^1.2.2"
    "raw-loader" "^0.5.1"

"quill@^1.2.2", "quill@^1.3.6":
  "integrity" "sha512-hG/DVzh/TiknWtE6QmWAF/pxoZKYxfe3J/d/+ShUWkDvvkZQVTPeVmUJVu1uE6DDooC4fWTiCLh84ul89oNz5g=="
  "resolved" "https://registry.npmjs.org/quill/-/quill-1.3.7.tgz"
  "version" "1.3.7"
  dependencies:
    "clone" "^2.1.1"
    "deep-equal" "^1.0.1"
    "eventemitter3" "^2.0.3"
    "extend" "^3.0.2"
    "parchment" "^1.1.4"
    "quill-delta" "^3.6.2"

"raf@^3.4.0":
  "integrity" "sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA=="
  "resolved" "https://registry.npmmirror.com/raf/-/raf-3.4.1.tgz"
  "version" "3.4.1"
  dependencies:
    "performance-now" "^2.1.0"

"randombytes@^2.1.0":
  "integrity" "sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ=="
  "resolved" "https://registry.npmmirror.com/randombytes/-/randombytes-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "safe-buffer" "^5.1.0"

"range-parser@^1.2.1", "range-parser@~1.2.1":
  "integrity" "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg=="
  "resolved" "https://registry.npmmirror.com/range-parser/-/range-parser-1.2.1.tgz"
  "version" "1.2.1"

"raw-body@2.5.1":
  "integrity" "sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig=="
  "resolved" "https://registry.npmmirror.com/raw-body/-/raw-body-2.5.1.tgz"
  "version" "2.5.1"
  dependencies:
    "bytes" "3.1.2"
    "http-errors" "2.0.0"
    "iconv-lite" "0.4.24"
    "unpipe" "1.0.0"

"raw-loader@^0.5.1":
  "integrity" "sha512-sf7oGoLuaYAScB4VGr0tzetsYlS8EJH6qnTCfQ/WVEa89hALQ4RQfCKt5xCyPQKPDUbVUAIP1QsxAwfAjlDp7Q=="
  "resolved" "https://npm.bangdao-tech.com/raw-loader/-/raw-loader-0.5.1.tgz"
  "version" "0.5.1"

"read-pkg-up@^7.0.1":
  "integrity" "sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg=="
  "resolved" "https://registry.npmmirror.com/read-pkg-up/-/read-pkg-up-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "find-up" "^4.1.0"
    "read-pkg" "^5.2.0"
    "type-fest" "^0.8.1"

"read-pkg@^5.1.1", "read-pkg@^5.2.0":
  "integrity" "sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg=="
  "resolved" "https://registry.npmmirror.com/read-pkg/-/read-pkg-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    "normalize-package-data" "^2.5.0"
    "parse-json" "^5.0.0"
    "type-fest" "^0.6.0"

"readable-stream@^2.0.1":
  "integrity" "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA=="
  "resolved" "https://registry.npmmirror.com/readable-stream/-/readable-stream-2.3.8.tgz"
  "version" "2.3.8"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^3.0.6", "readable-stream@^3.4.0":
  "integrity" "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA=="
  "resolved" "https://registry.npmmirror.com/readable-stream/-/readable-stream-3.6.2.tgz"
  "version" "3.6.2"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readdirp@~3.6.0":
  "integrity" "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA=="
  "resolved" "https://registry.npmmirror.com/readdirp/-/readdirp-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "picomatch" "^2.2.1"

"regenerate-unicode-properties@^10.1.0":
  "integrity" "sha512-d1VudCLoIGitcU/hEg2QqvyGZQmdC0Lf8BqdOMXGFSvJP4bNV1+XqbPQeHHLD51Jh4QJJ225dlIFvY4Ly6MXmQ=="
  "resolved" "https://registry.npmmirror.com/regenerate-unicode-properties/-/regenerate-unicode-properties-10.1.0.tgz"
  "version" "10.1.0"
  dependencies:
    "regenerate" "^1.4.2"

"regenerate@^1.4.2":
  "integrity" "sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A=="
  "resolved" "https://registry.npmmirror.com/regenerate/-/regenerate-1.4.2.tgz"
  "version" "1.4.2"

"regenerator-runtime@^0.11.0":
  "integrity" "sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg=="
  "resolved" "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz"
  "version" "0.11.1"

"regenerator-runtime@^0.13.11":
  "integrity" "sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg=="
  "resolved" "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz"
  "version" "0.13.11"

"regenerator-transform@^0.15.1":
  "integrity" "sha512-knzmNAcuyxV+gQCufkYcvOqX/qIIfHLv0u5x79kRxuGojfYVky1f15TzZEu2Avte8QGepvUNTnLskf8E6X6Vyg=="
  "resolved" "https://registry.npmmirror.com/regenerator-transform/-/regenerator-transform-0.15.1.tgz"
  "version" "0.15.1"
  dependencies:
    "@babel/runtime" "^7.8.4"

"regexp.prototype.flags@^1.2.0":
  "integrity" "sha512-0SutC3pNudRKgquxGoRGIz946MZVHqbNfPjBdxeOhBrdgDKlRoXmYLQN9xRbrR09ZXWeGAdPuif7egofn6v5LA=="
  "resolved" "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.2.0"
    "functions-have-names" "^1.2.3"

"regexpp@^3.1.0":
  "integrity" "sha512-pq2bWo9mVD43nbts2wGv17XLiNLya+GklZ8kaDLV2Z08gDCsGpnKn9BFMepvWuHCbyVvY7J5o5+BVvoQbmlJLg=="
  "resolved" "https://registry.npmmirror.com/regexpp/-/regexpp-3.2.0.tgz"
  "version" "3.2.0"

"regexpu-core@^5.3.1":
  "integrity" "sha512-RAM5FlZz+Lhmo7db9L298p2vHP5ZywrVXmVXpmAD9GuL5MPH6t9ROw1iA/wfHkQ76Qe7AaPF0nGuim96/IrQMQ=="
  "resolved" "https://registry.npmmirror.com/regexpu-core/-/regexpu-core-5.3.2.tgz"
  "version" "5.3.2"
  dependencies:
    "@babel/regjsgen" "^0.8.0"
    "regenerate" "^1.4.2"
    "regenerate-unicode-properties" "^10.1.0"
    "regjsparser" "^0.9.1"
    "unicode-match-property-ecmascript" "^2.0.0"
    "unicode-match-property-value-ecmascript" "^2.1.0"

"regjsparser@^0.9.1":
  "integrity" "sha512-dQUtn90WanSNl+7mQKcXAgZxvUe7Z0SqXlgzv0za4LwiUhyzBC58yQO3liFoUgu8GiJVInAhJjkj1N0EtQ5nkQ=="
  "resolved" "https://registry.npmmirror.com/regjsparser/-/regjsparser-0.9.1.tgz"
  "version" "0.9.1"
  dependencies:
    "jsesc" "~0.5.0"

"relateurl@^0.2.7":
  "integrity" "sha512-G08Dxvm4iDN3MLM0EsP62EDV9IuhXPR6blNz6Utcp7zyV3tr4HVNINt6MpaRWbxoOHT3Q7YN2P+jaHX8vUbgog=="
  "resolved" "https://registry.npmmirror.com/relateurl/-/relateurl-0.2.7.tgz"
  "version" "0.2.7"

"renderkid@^3.0.0":
  "integrity" "sha512-q/7VIQA8lmM1hF+jn+sFSPWGlMkSAeNYcPLmDQx2zzuiDfaLrOmumR8iaUKlenFgh0XRPIUeSPlH3A+AW3Z5pg=="
  "resolved" "https://registry.npmmirror.com/renderkid/-/renderkid-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "css-select" "^4.1.3"
    "dom-converter" "^0.2.0"
    "htmlparser2" "^6.1.0"
    "lodash" "^4.17.21"
    "strip-ansi" "^6.0.1"

"require-directory@^2.1.1":
  "integrity" "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q=="
  "resolved" "https://registry.npmmirror.com/require-directory/-/require-directory-2.1.1.tgz"
  "version" "2.1.1"

"require-from-string@^2.0.2":
  "integrity" "sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw=="
  "resolved" "https://registry.npmmirror.com/require-from-string/-/require-from-string-2.0.2.tgz"
  "version" "2.0.2"

"requires-port@^1.0.0":
  "integrity" "sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ=="
  "resolved" "https://registry.npmmirror.com/requires-port/-/requires-port-1.0.0.tgz"
  "version" "1.0.0"

"resize-observer-polyfill@^1.5.1":
  "integrity" "sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg=="
  "resolved" "https://registry.npmmirror.com/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz"
  "version" "1.5.1"

"resolve-from@^4.0.0":
  "integrity" "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g=="
  "resolved" "https://registry.npmmirror.com/resolve-from/-/resolve-from-4.0.0.tgz"
  "version" "4.0.0"

"resolve@^1.10.0", "resolve@^1.14.2":
  "integrity" "sha512-nBpuuYuY5jFsli/JIs1oldw6fOQCBioohqWZg/2hiaOybXOft4lonv85uDOKXdf8rhyK159cxU5cDcK/NKk8zw=="
  "resolved" "https://registry.npmmirror.com/resolve/-/resolve-1.22.1.tgz"
  "version" "1.22.1"
  dependencies:
    "is-core-module" "^2.9.0"
    "path-parse" "^1.0.7"
    "supports-preserve-symlinks-flag" "^1.0.0"

"restore-cursor@^2.0.0":
  "integrity" "sha512-6IzJLuGi4+R14vwagDHX+JrXmPVtPpn4mffDJ1UdR7/Edm87fl6yi8mMBIVvFtJaNTUvjughmW4hwLhRG7gC1Q=="
  "resolved" "https://registry.npmmirror.com/restore-cursor/-/restore-cursor-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "onetime" "^2.0.0"
    "signal-exit" "^3.0.2"

"restore-cursor@^3.1.0":
  "integrity" "sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA=="
  "resolved" "https://registry.npmmirror.com/restore-cursor/-/restore-cursor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "onetime" "^5.1.0"
    "signal-exit" "^3.0.2"

"retry@^0.13.1":
  "integrity" "sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg=="
  "resolved" "https://registry.npmmirror.com/retry/-/retry-0.13.1.tgz"
  "version" "0.13.1"

"reusify@^1.0.4":
  "integrity" "sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw=="
  "resolved" "https://registry.npmmirror.com/reusify/-/reusify-1.0.4.tgz"
  "version" "1.0.4"

"rfdc@^1.3.0":
  "integrity" "sha512-V2hovdzFbOi77/WajaSMXk2OLm+xNIeQdMMuB7icj7bk6zi2F8GGAxigcnDFpJHbNyNcgyJDiP+8nOrY5cZGrA=="
  "resolved" "https://registry.npmmirror.com/rfdc/-/rfdc-1.3.0.tgz"
  "version" "1.3.0"

"rimraf@^3.0.2":
  "integrity" "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA=="
  "resolved" "https://registry.npmmirror.com/rimraf/-/rimraf-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "glob" "^7.1.3"

"run-parallel@^1.1.9":
  "integrity" "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA=="
  "resolved" "https://registry.npmmirror.com/run-parallel/-/run-parallel-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "queue-microtask" "^1.2.2"

"rxjs@^7.5.1":
  "integrity" "sha512-F2+gxDshqmIub1KdvZkaEfGDwLNpPvk9Fs6LD/MyQxNgMds/WH9OdDDXOmxUZpME+iSK3rQCctkL0DYyytUqMg=="
  "resolved" "https://registry.npmmirror.com/rxjs/-/rxjs-7.8.0.tgz"
  "version" "7.8.0"
  dependencies:
    "tslib" "^2.1.0"

"safe-buffer@^5.1.0", "safe-buffer@>=5.1.0", "safe-buffer@~5.2.0", "safe-buffer@5.2.1":
  "integrity" "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ=="
  "resolved" "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz"
  "version" "5.2.1"

"safe-buffer@~5.1.0", "safe-buffer@~5.1.1":
  "integrity" "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="
  "resolved" "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-buffer@5.1.2":
  "integrity" "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="
  "resolved" "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safer-buffer@>= 2.1.2 < 3":
  "integrity" "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="
  "resolved" "https://registry.npmmirror.com/safer-buffer/-/safer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"sass-loader@^13.2.0":
  "integrity" "sha512-nrIdVAAte3B9icfBiGWvmMhT/D+eCDwnk+yA7VE/76dp/WkHX+i44Q/pfo71NYbwj0Ap+PGsn0ekOuU1WFJ2AA=="
  "resolved" "https://registry.npmmirror.com/sass-loader/-/sass-loader-13.2.2.tgz"
  "version" "13.2.2"
  dependencies:
    "klona" "^2.0.6"
    "neo-async" "^2.6.2"

"sass@^1.3.0", "sass@^1.59.2":
  "integrity" "sha512-updbwW6fNb5gGm8qMXzVO7V4sWf7LMXnMly/JEyfbfERbVH46Fn6q02BX7/eHTdKpE7d+oTkMMQpFWNUMfFbgQ=="
  "resolved" "https://registry.npmmirror.com/sass/-/sass-1.60.0.tgz"
  "version" "1.60.0"
  dependencies:
    "chokidar" ">=3.0.0 <4.0.0"
    "immutable" "^4.0.0"
    "source-map-js" ">=0.6.2 <2.0.0"

"saxes@^5.0.1":
  "integrity" "sha512-5LBh1Tls8c9xgGjw3QrMwETmTMVk0oFgvrFSvWx62llR2hcEInrKNZ2GZCCuuy2lvWrdl5jhbpeqc5hRYKFOcw=="
  "resolved" "https://registry.npmmirror.com/saxes/-/saxes-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "xmlchars" "^2.2.0"

"schema-utils@^2.6.5":
  "integrity" "sha512-SHiNtMOUGWBQJwzISiVYKu82GiV4QYGePp3odlY1tuKO7gPtphAT5R/py0fA6xtbgLL/RvtJZnU9b8s0F1q0Xg=="
  "resolved" "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "@types/json-schema" "^7.0.5"
    "ajv" "^6.12.4"
    "ajv-keywords" "^3.5.2"

"schema-utils@^2.7.0":
  "integrity" "sha512-SHiNtMOUGWBQJwzISiVYKu82GiV4QYGePp3odlY1tuKO7gPtphAT5R/py0fA6xtbgLL/RvtJZnU9b8s0F1q0Xg=="
  "resolved" "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "@types/json-schema" "^7.0.5"
    "ajv" "^6.12.4"
    "ajv-keywords" "^3.5.2"

"schema-utils@^3.0.0":
  "integrity" "sha512-Y5PQxS4ITlC+EahLuXaY86TXfR7Dc5lw294alXOq86JAHCihAIZfqv8nNCWvaEJvaC51uN9hbLGeV0cFBdH+Fw=="
  "resolved" "https://registry.npmmirror.com/schema-utils/-/schema-utils-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "@types/json-schema" "^7.0.8"
    "ajv" "^6.12.5"
    "ajv-keywords" "^3.5.2"

"schema-utils@^3.1.0":
  "integrity" "sha512-Y5PQxS4ITlC+EahLuXaY86TXfR7Dc5lw294alXOq86JAHCihAIZfqv8nNCWvaEJvaC51uN9hbLGeV0cFBdH+Fw=="
  "resolved" "https://registry.npmmirror.com/schema-utils/-/schema-utils-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "@types/json-schema" "^7.0.8"
    "ajv" "^6.12.5"
    "ajv-keywords" "^3.5.2"

"schema-utils@^3.1.1":
  "integrity" "sha512-Y5PQxS4ITlC+EahLuXaY86TXfR7Dc5lw294alXOq86JAHCihAIZfqv8nNCWvaEJvaC51uN9hbLGeV0cFBdH+Fw=="
  "resolved" "https://registry.npmmirror.com/schema-utils/-/schema-utils-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "@types/json-schema" "^7.0.8"
    "ajv" "^6.12.5"
    "ajv-keywords" "^3.5.2"

"schema-utils@^4.0.0":
  "integrity" "sha512-1edyXKgh6XnJsJSQ8mKWXnN/BVaIbFMLpouRUrXgVq7WYne5kw3MW7UPhO44uRXQSIpTSXoJbmrR2X0w9kUTyg=="
  "resolved" "https://registry.npmmirror.com/schema-utils/-/schema-utils-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "@types/json-schema" "^7.0.9"
    "ajv" "^8.8.0"
    "ajv-formats" "^2.1.1"
    "ajv-keywords" "^5.0.0"

"select-hose@^2.0.0":
  "integrity" "sha512-mEugaLK+YfkijB4fx0e6kImuJdCIt2LxCRcbEYPqRGCs4F2ogyfZU5IAZRdjCP8JPq2AtdNoC/Dux63d9Kiryg=="
  "resolved" "https://registry.npmmirror.com/select-hose/-/select-hose-2.0.0.tgz"
  "version" "2.0.0"

"selfsigned@^2.1.1":
  "integrity" "sha512-GSL3aowiF7wa/WtSFwnUrludWFoNhftq8bUkH9pkzjpN2XSPOAYEgg6e0sS9s0rZwgJzJiQRPU18A6clnoW5wQ=="
  "resolved" "https://registry.npmmirror.com/selfsigned/-/selfsigned-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "node-forge" "^1"

"semver-compare@^1.0.0":
  "integrity" "sha512-YM3/ITh2MJ5MtzaM429anh+x2jiLVjqILF4m4oyQB18W7Ggea7BfqdH/wGMK7dDiMghv/6WG7znWMwUDzJiXow=="
  "resolved" "https://registry.npmmirror.com/semver-compare/-/semver-compare-1.0.0.tgz"
  "version" "1.0.0"

"semver@^5.5.0":
  "integrity" "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="
  "resolved" "https://registry.npmmirror.com/semver/-/semver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^5.6.0":
  "integrity" "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="
  "resolved" "https://registry.npmmirror.com/semver/-/semver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^6.0.0", "semver@^6.1.1", "semver@^6.1.2", "semver@^6.3.0":
  "integrity" "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw=="
  "resolved" "https://registry.npmmirror.com/semver/-/semver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^7.2.1":
  "integrity" "sha512-NB1ctGL5rlHrPJtFDVIVzTyQylMLu9N9VICA6HSFJo8MCGVTMW6gfpicwKmmK/dAjTOrqu5l63JJOpDSrAis3A=="
  "resolved" "https://registry.npmmirror.com/semver/-/semver-7.3.8.tgz"
  "version" "7.3.8"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@^7.3.4":
  "integrity" "sha512-NB1ctGL5rlHrPJtFDVIVzTyQylMLu9N9VICA6HSFJo8MCGVTMW6gfpicwKmmK/dAjTOrqu5l63JJOpDSrAis3A=="
  "resolved" "https://registry.npmmirror.com/semver/-/semver-7.3.8.tgz"
  "version" "7.3.8"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@^7.3.5":
  "integrity" "sha512-NB1ctGL5rlHrPJtFDVIVzTyQylMLu9N9VICA6HSFJo8MCGVTMW6gfpicwKmmK/dAjTOrqu5l63JJOpDSrAis3A=="
  "resolved" "https://registry.npmmirror.com/semver/-/semver-7.3.8.tgz"
  "version" "7.3.8"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@^7.3.8":
  "integrity" "sha512-NB1ctGL5rlHrPJtFDVIVzTyQylMLu9N9VICA6HSFJo8MCGVTMW6gfpicwKmmK/dAjTOrqu5l63JJOpDSrAis3A=="
  "resolved" "https://registry.npmmirror.com/semver/-/semver-7.3.8.tgz"
  "version" "7.3.8"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@2 || 3 || 4 || 5":
  "integrity" "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="
  "resolved" "https://registry.npmmirror.com/semver/-/semver-5.7.1.tgz"
  "version" "5.7.1"

"send@0.18.0":
  "integrity" "sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg=="
  "resolved" "https://registry.npmmirror.com/send/-/send-0.18.0.tgz"
  "version" "0.18.0"
  dependencies:
    "debug" "2.6.9"
    "depd" "2.0.0"
    "destroy" "1.2.0"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "fresh" "0.5.2"
    "http-errors" "2.0.0"
    "mime" "1.6.0"
    "ms" "2.1.3"
    "on-finished" "2.4.1"
    "range-parser" "~1.2.1"
    "statuses" "2.0.1"

"serialize-javascript@^6.0.0", "serialize-javascript@^6.0.1":
  "integrity" "sha512-owoXEFjWRllis8/M1Q+Cw5k8ZH40e3zhp/ovX+Xr/vi1qj6QesbyXXViFbpNvWvPNAD62SutwEXavefrLJWj7w=="
  "resolved" "https://registry.npmmirror.com/serialize-javascript/-/serialize-javascript-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "randombytes" "^2.1.0"

"serve-index@^1.9.1":
  "integrity" "sha512-pXHfKNP4qujrtteMrSBb0rc8HJ9Ms/GrXwcUtUtD5s4ewDJI8bT3Cz2zTVRMKtri49pLx2e0Ya8ziP5Ya2pZZw=="
  "resolved" "https://registry.npmmirror.com/serve-index/-/serve-index-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "accepts" "~1.3.4"
    "batch" "0.6.1"
    "debug" "2.6.9"
    "escape-html" "~1.0.3"
    "http-errors" "~1.6.2"
    "mime-types" "~2.1.17"
    "parseurl" "~1.3.2"

"serve-static@1.15.0":
  "integrity" "sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g=="
  "resolved" "https://registry.npmmirror.com/serve-static/-/serve-static-1.15.0.tgz"
  "version" "1.15.0"
  dependencies:
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "parseurl" "~1.3.3"
    "send" "0.18.0"

"setprototypeof@1.1.0":
  "integrity" "sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ=="
  "resolved" "https://registry.npmmirror.com/setprototypeof/-/setprototypeof-1.1.0.tgz"
  "version" "1.1.0"

"setprototypeof@1.2.0":
  "integrity" "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw=="
  "resolved" "https://registry.npmmirror.com/setprototypeof/-/setprototypeof-1.2.0.tgz"
  "version" "1.2.0"

"shallow-clone@^3.0.0":
  "integrity" "sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA=="
  "resolved" "https://registry.npmmirror.com/shallow-clone/-/shallow-clone-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "^6.0.2"

"shallow-equal@^1.0.0":
  "integrity" "sha512-S4vJDjHHMBaiZuT9NPb616CSmLf618jawtv3sufLl6ivK8WocjAo58cXwbRV1cgqxH0Qbv+iUt6m05eqEa2IRA=="
  "resolved" "https://registry.npmmirror.com/shallow-equal/-/shallow-equal-1.2.1.tgz"
  "version" "1.2.1"

"shallowequal@^1.0.2":
  "integrity" "sha512-y0m1JoUZSlPAjXVtPPW70aZWfIL/dSP7AFkRnniLCrK/8MDKog3TySTBmckD+RObVxH0v4Tox67+F14PdED2oQ=="
  "resolved" "https://registry.npmmirror.com/shallowequal/-/shallowequal-1.1.0.tgz"
  "version" "1.1.0"

"shebang-command@^1.2.0":
  "integrity" "sha512-EV3L1+UQWGor21OmnvojK36mhg+TyIKDh3iFBKBohr5xeXIhNBcx8oWdgkTEEQ+BEFFYdLRuqMfd5L84N1V5Vg=="
  "resolved" "https://registry.npmmirror.com/shebang-command/-/shebang-command-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "shebang-regex" "^1.0.0"

"shebang-command@^2.0.0":
  "integrity" "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA=="
  "resolved" "https://registry.npmmirror.com/shebang-command/-/shebang-command-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "shebang-regex" "^3.0.0"

"shebang-regex@^1.0.0":
  "integrity" "sha512-wpoSFAxys6b2a2wHZ1XpDSgD7N9iVjg29Ph9uV/uaP9Ex/KXlkTZTeddxDPSYQpgvzKLGJke2UU0AzoGCjNIvQ=="
  "resolved" "https://registry.npmmirror.com/shebang-regex/-/shebang-regex-1.0.0.tgz"
  "version" "1.0.0"

"shebang-regex@^3.0.0":
  "integrity" "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A=="
  "resolved" "https://registry.npmmirror.com/shebang-regex/-/shebang-regex-3.0.0.tgz"
  "version" "3.0.0"

"shell-quote@^1.7.3":
  "integrity" "sha512-QHsz8GgQIGKlRi24yFc6a6lN69Idnx634w49ay6+jA5yFh7a1UY+4Rp6HPx/L/1zcEDPEij8cIsiqR6bQsE5VQ=="
  "resolved" "https://registry.npmmirror.com/shell-quote/-/shell-quote-1.8.0.tgz"
  "version" "1.8.0"

"side-channel@^1.0.4":
  "integrity" "sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw=="
  "resolved" "https://registry.npmmirror.com/side-channel/-/side-channel-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.0"
    "get-intrinsic" "^1.0.2"
    "object-inspect" "^1.9.0"

"signal-exit@^3.0.0", "signal-exit@^3.0.2", "signal-exit@^3.0.3":
  "integrity" "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ=="
  "resolved" "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.7.tgz"
  "version" "3.0.7"

"sirv@^1.0.7":
  "integrity" "sha512-JuLThK3TnZG1TAKDwNIqNq6QA2afLOCcm+iE8D1Kj3GA40pSPsxQjjJl0J8X3tsR7T+CP1GavpzLwYkgVLWrZQ=="
  "resolved" "https://registry.npmmirror.com/sirv/-/sirv-1.0.19.tgz"
  "version" "1.0.19"
  dependencies:
    "@polka/url" "^1.0.0-next.20"
    "mrmime" "^1.0.0"
    "totalist" "^1.0.0"

"slash@^3.0.0":
  "integrity" "sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q=="
  "resolved" "https://registry.npmmirror.com/slash/-/slash-3.0.0.tgz"
  "version" "3.0.0"

"slice-ansi@^3.0.0":
  "integrity" "sha512-pSyv7bSTC7ig9Dcgbw9AuRNUb5k5V6oDudjZoMBSr13qpLBG7tB+zgCkARjq7xIUgdz5P1Qe8u+rSGdouOOIyQ=="
  "resolved" "https://registry.npmmirror.com/slice-ansi/-/slice-ansi-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "astral-regex" "^2.0.0"
    "is-fullwidth-code-point" "^3.0.0"

"slice-ansi@^4.0.0":
  "integrity" "sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ=="
  "resolved" "https://registry.npmmirror.com/slice-ansi/-/slice-ansi-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "astral-regex" "^2.0.0"
    "is-fullwidth-code-point" "^3.0.0"

"sockjs@^0.3.24":
  "integrity" "sha512-GJgLTZ7vYb/JtPSSZ10hsOYIvEYsjbNU+zPdIHcUaWVNUEPivzxku31865sSSud0Da0W4lEeOPlmw93zLQchuQ=="
  "resolved" "https://registry.npmmirror.com/sockjs/-/sockjs-0.3.24.tgz"
  "version" "0.3.24"
  dependencies:
    "faye-websocket" "^0.11.3"
    "uuid" "^8.3.2"
    "websocket-driver" "^0.7.4"

"source-map-js@^1.0.2", "source-map-js@>=0.6.2 <2.0.0":
  "integrity" "sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw=="
  "resolved" "https://registry.npmmirror.com/source-map-js/-/source-map-js-1.0.2.tgz"
  "version" "1.0.2"

"source-map-support@~0.5.20":
  "integrity" "sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w=="
  "resolved" "https://registry.npmmirror.com/source-map-support/-/source-map-support-0.5.21.tgz"
  "version" "0.5.21"
  dependencies:
    "buffer-from" "^1.0.0"
    "source-map" "^0.6.0"

"source-map@^0.6.0", "source-map@^0.6.1", "source-map@~0.6.0", "source-map@~0.6.1":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"spdx-correct@^3.0.0":
  "integrity" "sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA=="
  "resolved" "https://registry.npmmirror.com/spdx-correct/-/spdx-correct-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "spdx-expression-parse" "^3.0.0"
    "spdx-license-ids" "^3.0.0"

"spdx-exceptions@^2.1.0":
  "integrity" "sha512-/tTrYOC7PPI1nUAgx34hUpqXuyJG+DTHJTnIULG4rDygi4xu/tfgmq1e1cIRwRzwZgo4NLySi+ricLkZkw4i5A=="
  "resolved" "https://registry.npmmirror.com/spdx-exceptions/-/spdx-exceptions-2.3.0.tgz"
  "version" "2.3.0"

"spdx-expression-parse@^3.0.0":
  "integrity" "sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q=="
  "resolved" "https://registry.npmmirror.com/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "spdx-exceptions" "^2.1.0"
    "spdx-license-ids" "^3.0.0"

"spdx-license-ids@^3.0.0":
  "integrity" "sha512-XkD+zwiqXHikFZm4AX/7JSCXA98U5Db4AFd5XUg/+9UNtnH75+Z9KxtpYiJZx36mUDVOwH83pl7yvCer6ewM3w=="
  "resolved" "https://registry.npmmirror.com/spdx-license-ids/-/spdx-license-ids-3.0.13.tgz"
  "version" "3.0.13"

"spdy-transport@^3.0.0":
  "integrity" "sha512-hsLVFE5SjA6TCisWeJXFKniGGOpBgMLmerfO2aCyCU5s7nJ/rpAepqmFifv/GCbSbueEeAJJnmSQ2rKC/g8Fcw=="
  "resolved" "https://registry.npmmirror.com/spdy-transport/-/spdy-transport-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "debug" "^4.1.0"
    "detect-node" "^2.0.4"
    "hpack.js" "^2.1.6"
    "obuf" "^1.1.2"
    "readable-stream" "^3.0.6"
    "wbuf" "^1.7.3"

"spdy@^4.0.2":
  "integrity" "sha512-r46gZQZQV+Kl9oItvl1JZZqJKGr+oEkB08A6BzkiR7593/7IbtuncXHd2YoYeTsG4157ZssMu9KYvUHLcjcDoA=="
  "resolved" "https://registry.npmmirror.com/spdy/-/spdy-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "debug" "^4.1.0"
    "handle-thing" "^2.0.0"
    "http-deceiver" "^1.2.7"
    "select-hose" "^2.0.0"
    "spdy-transport" "^3.0.0"

"sprintf-js@~1.0.2":
  "integrity" "sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g=="
  "resolved" "https://registry.npmmirror.com/sprintf-js/-/sprintf-js-1.0.3.tgz"
  "version" "1.0.3"

"ssf@~0.11.2":
  "integrity" "sha512-+idbmIXoYET47hH+d7dfm2epdOMUDjqcB4648sTZ+t2JwoyBFL/insLfB/racrDmsKB3diwsDA696pZMieAC5g=="
  "resolved" "https://registry.npmmirror.com/ssf/-/ssf-0.11.2.tgz"
  "version" "0.11.2"
  dependencies:
    "frac" "~1.1.2"

"ssri@^8.0.1":
  "integrity" "sha512-97qShzy1AiyxvPNIkLWoGua7xoQzzPjQ0HAH4B0rWKo7SZ6USuPcrUiAFrws0UH8RrbWmgq3LMTObhPIHbbBeQ=="
  "resolved" "https://registry.npmmirror.com/ssri/-/ssri-8.0.1.tgz"
  "version" "8.0.1"
  dependencies:
    "minipass" "^3.1.1"

"stable@^0.1.8":
  "integrity" "sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w=="
  "resolved" "https://registry.npmmirror.com/stable/-/stable-0.1.8.tgz"
  "version" "0.1.8"

"stackframe@^1.3.4":
  "integrity" "sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw=="
  "resolved" "https://registry.npmmirror.com/stackframe/-/stackframe-1.3.4.tgz"
  "version" "1.3.4"

"statuses@>= 1.4.0 < 2":
  "integrity" "sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA=="
  "resolved" "https://registry.npmmirror.com/statuses/-/statuses-1.5.0.tgz"
  "version" "1.5.0"

"statuses@2.0.1":
  "integrity" "sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ=="
  "resolved" "https://registry.npmmirror.com/statuses/-/statuses-2.0.1.tgz"
  "version" "2.0.1"

"string_decoder@^1.1.1":
  "integrity" "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA=="
  "resolved" "https://registry.npmmirror.com/string_decoder/-/string_decoder-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "safe-buffer" "~5.2.0"

"string_decoder@~1.1.1":
  "integrity" "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg=="
  "resolved" "https://registry.npmmirror.com/string_decoder/-/string_decoder-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "safe-buffer" "~5.1.0"

"string-argv@0.3.1":
  "integrity" "sha512-a1uQGz7IyVy9YwhqjZIZu1c8JO8dNIe20xBmSS6qu9kv++k3JGzCVmprbNN5Kn+BgzD5E7YYwg1CcjuJMRNsvg=="
  "resolved" "https://registry.npmmirror.com/string-argv/-/string-argv-0.3.1.tgz"
  "version" "0.3.1"

"string-convert@^0.2.0":
  "integrity" "sha512-u/1tdPl4yQnPBjnVrmdLo9gtuLvELKsAoRapekWggdiQNvvvum+jYF329d84NAa660KQw7pB2n36KrIKVoXa3A=="
  "resolved" "https://registry.npmmirror.com/string-convert/-/string-convert-0.2.1.tgz"
  "version" "0.2.1"

"string-width@^2.1.1":
  "integrity" "sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw=="
  "resolved" "https://registry.npmmirror.com/string-width/-/string-width-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-fullwidth-code-point" "^2.0.0"
    "strip-ansi" "^4.0.0"

"string-width@^4.1.0", "string-width@^4.2.0", "string-width@^4.2.3":
  "integrity" "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g=="
  "resolved" "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.1"

"stringify-object@3.3.0":
  "integrity" "sha512-rHqiFh1elqCQ9WPLIC8I0Q/g/wj5J1eMkyoiD6eoQApWHP0FtlK7rqnhmabL5VUY9JQCcqwwvlOaSuutekgyrw=="
  "resolved" "https://registry.npmmirror.com/stringify-object/-/stringify-object-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "get-own-enumerable-property-symbols" "^3.0.0"
    "is-obj" "^1.0.1"
    "is-regexp" "^1.0.0"

"strip-ansi@^4.0.0":
  "integrity" "sha512-4XaJ2zQdCzROZDivEVIDPkcQn8LMFSa8kj8Gxb/Lnwzv9A8VctNZ+lfivC/sV3ivW8ElJTERXZoPBRrZKkNKow=="
  "resolved" "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-regex" "^3.0.0"

"strip-ansi@^6.0.0", "strip-ansi@^6.0.1":
  "integrity" "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A=="
  "resolved" "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "ansi-regex" "^5.0.1"

"strip-eof@^1.0.0":
  "integrity" "sha512-7FCwGGmx8mD5xQd3RPUvnSpUXHM3BWuzjtpD4TXsfcZ9EL4azvVVUscFYwD9nx8Kh+uCBC00XBtAykoMHwTh8Q=="
  "resolved" "https://registry.npmmirror.com/strip-eof/-/strip-eof-1.0.0.tgz"
  "version" "1.0.0"

"strip-final-newline@^2.0.0":
  "integrity" "sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA=="
  "resolved" "https://registry.npmmirror.com/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  "version" "2.0.0"

"strip-indent@^2.0.0":
  "integrity" "sha512-RsSNPLpq6YUL7QYy44RnPVTn/lcVZtb48Uof3X5JLbF4zD/Gs7ZFDv2HWol+leoQN2mT86LAzSshGfkTlSOpsA=="
  "resolved" "https://registry.npmmirror.com/strip-indent/-/strip-indent-2.0.0.tgz"
  "version" "2.0.0"

"strip-json-comments@^3.1.0", "strip-json-comments@^3.1.1":
  "integrity" "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig=="
  "resolved" "https://registry.npmmirror.com/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  "version" "3.1.1"

"style-resources-loader@1.5.0":
  "integrity" "sha512-fIfyvQ+uvXaCBGGAgfh+9v46ARQB1AWdaop2RpQw0PBVuROsTBqGvx8dj0kxwjGOAyq3vepe4AOK3M6+Q/q2jw=="
  "resolved" "https://registry.npmjs.org/style-resources-loader/-/style-resources-loader-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "glob" "^7.2.0"
    "loader-utils" "^2.0.0"
    "schema-utils" "^2.7.0"
    "tslib" "^2.3.1"

"stylehacks@^5.1.1":
  "integrity" "sha512-sBpcd5Hx7G6seo7b1LkpttvTz7ikD0LlH5RmdcBNb6fFR0Fl7LQwHDFr300q4cwUqi+IYrFGmsIHieMBfnN/Bw=="
  "resolved" "https://registry.npmmirror.com/stylehacks/-/stylehacks-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "browserslist" "^4.21.4"
    "postcss-selector-parser" "^6.0.4"

"supports-color@^5.3.0":
  "integrity" "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow=="
  "resolved" "https://registry.npmmirror.com/supports-color/-/supports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^7.1.0":
  "integrity" "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw=="
  "resolved" "https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "has-flag" "^4.0.0"

"supports-color@^8.0.0", "supports-color@8.1.1":
  "integrity" "sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q=="
  "resolved" "https://registry.npmmirror.com/supports-color/-/supports-color-8.1.1.tgz"
  "version" "8.1.1"
  dependencies:
    "has-flag" "^4.0.0"

"supports-preserve-symlinks-flag@^1.0.0":
  "integrity" "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w=="
  "resolved" "https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  "version" "1.0.0"

"svg-tags@^1.0.0":
  "integrity" "sha512-ovssysQTa+luh7A5Weu3Rta6FJlFBBbInjOh722LIt6klpU2/HtdUbszju/G4devcvk8PGt7FCLv5wftu3THUA=="
  "resolved" "https://registry.npmmirror.com/svg-tags/-/svg-tags-1.0.0.tgz"
  "version" "1.0.0"

"svgo@^2.7.0":
  "integrity" "sha512-+N/Q9kV1+F+UeWYoSiULYo4xYSDQlTgb+ayMobAXPwMnLvop7oxKMo9OzIrX5x3eS4L4f2UHhc9axXwY8DpChg=="
  "resolved" "https://registry.npmmirror.com/svgo/-/svgo-2.8.0.tgz"
  "version" "2.8.0"
  dependencies:
    "@trysound/sax" "0.2.0"
    "commander" "^7.2.0"
    "css-select" "^4.1.3"
    "css-tree" "^1.1.3"
    "csso" "^4.2.0"
    "picocolors" "^1.0.0"
    "stable" "^0.1.8"

"symbol-tree@^3.2.4":
  "integrity" "sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw=="
  "resolved" "https://registry.npmmirror.com/symbol-tree/-/symbol-tree-3.2.4.tgz"
  "version" "3.2.4"

"table@^6.0.9":
  "integrity" "sha512-Y4X9zqrCftUhMeH2EptSSERdVKt/nEdijTOacGD/97EKjhQ/Qs8RTlEGABSJNNN8lac9kheH+af7yAkEWlgneA=="
  "resolved" "https://registry.npmmirror.com/table/-/table-6.8.1.tgz"
  "version" "6.8.1"
  dependencies:
    "ajv" "^8.0.1"
    "lodash.truncate" "^4.4.2"
    "slice-ansi" "^4.0.0"
    "string-width" "^4.2.3"
    "strip-ansi" "^6.0.1"

"tapable@^2.0.0", "tapable@^2.1.1", "tapable@^2.2.0":
  "integrity" "sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ=="
  "resolved" "https://registry.npmmirror.com/tapable/-/tapable-2.2.1.tgz"
  "version" "2.2.1"

"terser-webpack-plugin@^5.1.1", "terser-webpack-plugin@^5.1.3":
  "integrity" "sha512-AfKwIktyP7Cu50xNjXF/6Qb5lBNzYaWpU6YfoX3uZicTx0zTy0stDDCsvjDapKsSDvOeWo5MEq4TmdBy2cNoHw=="
  "resolved" "https://registry.npmmirror.com/terser-webpack-plugin/-/terser-webpack-plugin-5.3.7.tgz"
  "version" "5.3.7"
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.17"
    "jest-worker" "^27.4.5"
    "schema-utils" "^3.1.1"
    "serialize-javascript" "^6.0.1"
    "terser" "^5.16.5"

"terser@^5.10.0", "terser@^5.16.5":
  "integrity" "sha512-QI5g1E/ef7d+PsDifb+a6nnVgC4F22Bg6T0xrBrz6iloVB4PUkkunp6V8nzoOOZJIzjWVdAGqCdlKlhLq/TbIA=="
  "resolved" "https://registry.npmmirror.com/terser/-/terser-5.16.8.tgz"
  "version" "5.16.8"
  dependencies:
    "@jridgewell/source-map" "^0.3.2"
    "acorn" "^8.5.0"
    "commander" "^2.20.0"
    "source-map-support" "~0.5.20"

"text-table@^0.2.0":
  "integrity" "sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw=="
  "resolved" "https://registry.npmmirror.com/text-table/-/text-table-0.2.0.tgz"
  "version" "0.2.0"

"thenify-all@^1.0.0":
  "integrity" "sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA=="
  "resolved" "https://registry.npmmirror.com/thenify-all/-/thenify-all-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "thenify" ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  "integrity" "sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw=="
  "resolved" "https://registry.npmmirror.com/thenify/-/thenify-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "any-promise" "^1.0.0"

"thread-loader@^3.0.0":
  "integrity" "sha512-ByaL2TPb+m6yArpqQUZvP+5S1mZtXsEP7nWKKlAUTm7fCml8kB5s1uI3+eHRP2bk5mVYfRSBI7FFf+tWEyLZwA=="
  "resolved" "https://registry.npmmirror.com/thread-loader/-/thread-loader-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "json-parse-better-errors" "^1.0.2"
    "loader-runner" "^4.1.0"
    "loader-utils" "^2.0.0"
    "neo-async" "^2.6.2"
    "schema-utils" "^3.0.0"

"through@^2.3.8":
  "integrity" "sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg=="
  "resolved" "https://registry.npmmirror.com/through/-/through-2.3.8.tgz"
  "version" "2.3.8"

"thunky@^1.0.2":
  "integrity" "sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA=="
  "resolved" "https://registry.npmmirror.com/thunky/-/thunky-1.1.0.tgz"
  "version" "1.1.0"

"tinycolor2@^1.4.1":
  "integrity" "sha512-XPaBkWQJdsf3pLKJV9p4qN/S+fm2Oj8AIPo1BTUhg5oxkvm9+SVEGFdhyOz7tTdUTfvxMiAs4sp6/eZO2Ew+pw=="
  "resolved" "https://registry.npmmirror.com/tinycolor2/-/tinycolor2-1.6.0.tgz"
  "version" "1.6.0"

"to-fast-properties@^2.0.0":
  "integrity" "sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog=="
  "resolved" "https://registry.npmmirror.com/to-fast-properties/-/to-fast-properties-2.0.0.tgz"
  "version" "2.0.0"

"to-regex-range@^5.0.1":
  "integrity" "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ=="
  "resolved" "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "is-number" "^7.0.0"

"toidentifier@1.0.1":
  "integrity" "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA=="
  "resolved" "https://registry.npmmirror.com/toidentifier/-/toidentifier-1.0.1.tgz"
  "version" "1.0.1"

"totalist@^1.0.0":
  "integrity" "sha512-gduQwd1rOdDMGxFG1gEvhV88Oirdo2p+KjoYFU7k2g+i7n6AFFbDQ5kMPUsW0pNbfQsB/cwXvT1i4Bue0s9g5g=="
  "resolved" "https://registry.npmmirror.com/totalist/-/totalist-1.1.0.tgz"
  "version" "1.1.0"

"tough-cookie@^4.0.0":
  "integrity" "sha512-G9fqXWoYFZgTc2z8Q5zaHy/vJMjm+WV0AkAeHxVCQiEB1b+dGvWzFW6QV07cY5jQ5gRkeid2qIkzkxUnmoQZUQ=="
  "resolved" "https://registry.npmmirror.com/tough-cookie/-/tough-cookie-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "psl" "^1.1.33"
    "punycode" "^2.1.1"
    "universalify" "^0.2.0"
    "url-parse" "^1.5.3"

"tr46@^2.1.0":
  "integrity" "sha512-15Ih7phfcdP5YxqiB+iDtLoaTz4Nd35+IiAv0kQ5FNKHzXgdWqPoTIqEDDJmXceQt4JZk6lVPT8lnDlPpGDppw=="
  "resolved" "https://registry.npmmirror.com/tr46/-/tr46-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "punycode" "^2.1.1"

"tr46@~0.0.3":
  "integrity" "sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw=="
  "resolved" "https://registry.npmmirror.com/tr46/-/tr46-0.0.3.tgz"
  "version" "0.0.3"

"tslib@^1.10.0":
  "integrity" "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="
  "resolved" "https://registry.npmmirror.com/tslib/-/tslib-1.14.1.tgz"
  "version" "1.14.1"

"tslib@^2.0.3", "tslib@^2.1.0":
  "integrity" "sha512-336iVw3rtn2BUK7ORdIAHTyxHGRIHVReokCR3XjbckJMK7ms8FysBfhLR8IXnAgy7T0PTPNBWKiH514FOW/WSg=="
  "resolved" "https://registry.npmmirror.com/tslib/-/tslib-2.5.0.tgz"
  "version" "2.5.0"

"tslib@^2.3.1":
  "integrity" "sha512-t0hLfiEKfMUoqhG+U1oid7Pva4bbDPHYfJNiB7BiIjRkj1pyC++4N3huJfqY6aRH6VTB0rvtzQwjM4K6qpfOig=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-2.6.1.tgz"
  "version" "2.6.1"

"tslib@2.3.0":
  "integrity" "sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-2.3.0.tgz"
  "version" "2.3.0"

"type-check@^0.4.0":
  "integrity" "sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew=="
  "resolved" "https://registry.npmmirror.com/type-check/-/type-check-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "prelude-ls" "^1.2.1"

"type-check@~0.3.2":
  "integrity" "sha512-ZCmOJdvOWDBYJlzAoFkC+Q0+bUyEOS1ltgp1MGU03fqHG+dbi9tBFU2Rd9QKiDZFAYrhPh2JUf7rZRIuHRKtOg=="
  "resolved" "https://registry.npmmirror.com/type-check/-/type-check-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "prelude-ls" "~1.1.2"

"type-check@~0.4.0":
  "integrity" "sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew=="
  "resolved" "https://registry.npmmirror.com/type-check/-/type-check-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "prelude-ls" "^1.2.1"

"type-fest@^0.20.2":
  "integrity" "sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ=="
  "resolved" "https://registry.npmmirror.com/type-fest/-/type-fest-0.20.2.tgz"
  "version" "0.20.2"

"type-fest@^0.21.3":
  "integrity" "sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w=="
  "resolved" "https://registry.npmmirror.com/type-fest/-/type-fest-0.21.3.tgz"
  "version" "0.21.3"

"type-fest@^0.6.0":
  "integrity" "sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg=="
  "resolved" "https://registry.npmmirror.com/type-fest/-/type-fest-0.6.0.tgz"
  "version" "0.6.0"

"type-fest@^0.8.1":
  "integrity" "sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA=="
  "resolved" "https://registry.npmmirror.com/type-fest/-/type-fest-0.8.1.tgz"
  "version" "0.8.1"

"type-is@~1.6.18":
  "integrity" "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g=="
  "resolved" "https://registry.npmmirror.com/type-is/-/type-is-1.6.18.tgz"
  "version" "1.6.18"
  dependencies:
    "media-typer" "0.3.0"
    "mime-types" "~2.1.24"

"unicode-canonical-property-names-ecmascript@^2.0.0":
  "integrity" "sha512-yY5PpDlfVIU5+y/BSCxAJRBIS1Zc2dDG3Ujq+sR0U+JjUevW2JhocOF+soROYDSaAezOzOKuyyixhD6mBknSmQ=="
  "resolved" "https://registry.npmmirror.com/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz"
  "version" "2.0.0"

"unicode-match-property-ecmascript@^2.0.0":
  "integrity" "sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q=="
  "resolved" "https://registry.npmmirror.com/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "unicode-canonical-property-names-ecmascript" "^2.0.0"
    "unicode-property-aliases-ecmascript" "^2.0.0"

"unicode-match-property-value-ecmascript@^2.1.0":
  "integrity" "sha512-qxkjQt6qjg/mYscYMC0XKRn3Rh0wFPlfxB0xkt9CfyTvpX1Ra0+rAmdX2QyAobptSEvuy4RtpPRui6XkV+8wjA=="
  "resolved" "https://registry.npmmirror.com/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.1.0.tgz"
  "version" "2.1.0"

"unicode-property-aliases-ecmascript@^2.0.0":
  "integrity" "sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w=="
  "resolved" "https://registry.npmmirror.com/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz"
  "version" "2.1.0"

"universalify@^0.2.0":
  "integrity" "sha512-CJ1QgKmNg3CwvAv/kOFmtnEN05f0D/cn9QntgNOQlQF9dgvVTHj3t+8JPdjqawCHk7V/KA+fbUqzZ9XWhcqPUg=="
  "resolved" "https://registry.npmmirror.com/universalify/-/universalify-0.2.0.tgz"
  "version" "0.2.0"

"universalify@^2.0.0":
  "integrity" "sha512-hAZsKq7Yy11Zu1DE0OzWjw7nnLZmJZYTDZZyEFHZdUhV8FkH5MCfoU1XMaxXovpyW5nq5scPqq0ZDP9Zyl04oQ=="
  "resolved" "https://registry.npmmirror.com/universalify/-/universalify-2.0.0.tgz"
  "version" "2.0.0"

"unpipe@~1.0.0", "unpipe@1.0.0":
  "integrity" "sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ=="
  "resolved" "https://registry.npmmirror.com/unpipe/-/unpipe-1.0.0.tgz"
  "version" "1.0.0"

"update-browserslist-db@^1.0.10":
  "integrity" "sha512-OztqDenkfFkbSG+tRxBeAnCVPckDBcvibKd35yDONx6OU8N7sqgwc7rCbkJ/WcYtVRZ4ba68d6byhC21GFh7sQ=="
  "resolved" "https://registry.npmmirror.com/update-browserslist-db/-/update-browserslist-db-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "escalade" "^3.1.1"
    "picocolors" "^1.0.0"

"uri-js@^4.2.2":
  "integrity" "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg=="
  "resolved" "https://registry.npmmirror.com/uri-js/-/uri-js-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "punycode" "^2.1.0"

"url-parse@^1.5.3":
  "integrity" "sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ=="
  "resolved" "https://registry.npmmirror.com/url-parse/-/url-parse-1.5.10.tgz"
  "version" "1.5.10"
  dependencies:
    "querystringify" "^2.1.1"
    "requires-port" "^1.0.0"

"util-deprecate@^1.0.1", "util-deprecate@^1.0.2", "util-deprecate@~1.0.1":
  "integrity" "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw=="
  "resolved" "https://registry.npmmirror.com/util-deprecate/-/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"utila@~0.4":
  "integrity" "sha512-Z0DbgELS9/L/75wZbro8xAnT50pBVFQZ+hUEueGDU5FN51YSCYM+jdxsfCiHjwNP/4LCDD0i/graKpeBnOXKRA=="
  "resolved" "https://registry.npmmirror.com/utila/-/utila-0.4.0.tgz"
  "version" "0.4.0"

"utils-merge@1.0.1":
  "integrity" "sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA=="
  "resolved" "https://registry.npmmirror.com/utils-merge/-/utils-merge-1.0.1.tgz"
  "version" "1.0.1"

"uuid@^8.3.2":
  "integrity" "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg=="
  "resolved" "https://registry.npmmirror.com/uuid/-/uuid-8.3.2.tgz"
  "version" "8.3.2"

"v8-compile-cache@^2.0.3":
  "integrity" "sha512-l8lCEmLcLYZh4nbunNZvQCJc5pv7+RCwa8q/LdUx8u7lsWvPDKmpodJAJNwkAhJC//dFY48KuIEmjtd4RViDrA=="
  "resolved" "https://registry.npmmirror.com/v8-compile-cache/-/v8-compile-cache-2.3.0.tgz"
  "version" "2.3.0"

"validate-npm-package-license@^3.0.1":
  "integrity" "sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew=="
  "resolved" "https://registry.npmmirror.com/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "spdx-correct" "^3.0.0"
    "spdx-expression-parse" "^3.0.0"

"vary@~1.1.2":
  "integrity" "sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg=="
  "resolved" "https://registry.npmmirror.com/vary/-/vary-1.1.2.tgz"
  "version" "1.1.2"

"vue-cli-plugin-style-resources-loader@0.1.5":
  "integrity" "sha512-LluhjWTZmpGl3tiXg51EciF+T70IN/9t6UvfmgluJBqxbrb6OV9i7L5lTd+OKtcTeghDkhcBmYhtTxxU4w/8sQ=="
  "resolved" "https://registry.npmmirror.com/vue-cli-plugin-style-resources-loader/-/vue-cli-plugin-style-resources-loader-0.1.5.tgz"
  "version" "0.1.5"

"vue-eslint-parser@^8.0.1":
  "integrity" "sha512-dzHGG3+sYwSf6zFBa0Gi9ZDshD7+ad14DGOdTLjruRVgZXe2J+DcZ9iUhyR48z5g1PqRa20yt3Njna/veLJL/g=="
  "resolved" "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-8.3.0.tgz"
  "version" "8.3.0"
  dependencies:
    "debug" "^4.3.2"
    "eslint-scope" "^7.0.0"
    "eslint-visitor-keys" "^3.1.0"
    "espree" "^9.0.0"
    "esquery" "^1.4.0"
    "lodash" "^4.17.21"
    "semver" "^7.3.5"

"vue-hot-reload-api@^2.3.0":
  "integrity" "sha512-BXq3jwIagosjgNVae6tkHzzIk6a8MHFtzAdwhnV5VlvPTFxDCvIttgSiHWjdGoTJvXtmRu5HacExfdarRcFhog=="
  "resolved" "https://registry.npmmirror.com/vue-hot-reload-api/-/vue-hot-reload-api-2.3.4.tgz"
  "version" "2.3.4"

"vue-loader@^17.0.0":
  "integrity" "sha512-/OOyugJnImKCkAKrAvdsWMuwoCqGxWT5USLsjohzWbMgOwpA5wQmzQiLMzZd7DjhIfunzAGIApTOgIylz/kwcg=="
  "resolved" "https://registry.npmmirror.com/vue-loader/-/vue-loader-17.0.1.tgz"
  "version" "17.0.1"
  dependencies:
    "chalk" "^4.1.0"
    "hash-sum" "^2.0.0"
    "loader-utils" "^2.0.0"

"vue-ref@^2.0.0":
  "integrity" "sha512-uKNKpFOVeWNqS2mrBZqnpLyXJo5Q+vnkex6JvpENvhXHFNBW/SJTP8vJywLuVT3DpxwXcF9N0dyIiZ4/NpTexQ=="
  "resolved" "https://registry.npmmirror.com/vue-ref/-/vue-ref-2.0.0.tgz"
  "version" "2.0.0"

"vue-router@3":
  "integrity" "sha512-VYXZQLtjuvKxxcshuRAwjHnciqZVoXAjTjcqBTz4rKc8qih9g9pI3hbDjmqXaHdgL3v8pV6P8Z335XvHzESxLQ=="
  "resolved" "https://registry.npmmirror.com/vue-router/-/vue-router-3.6.5.tgz"
  "version" "3.6.5"

"vue-style-loader@^4.1.0", "vue-style-loader@^4.1.3":
  "integrity" "sha512-sFuh0xfbtpRlKfm39ss/ikqs9AbKCoXZBpHeVZ8Tx650o0k0q/YCM7FRvigtxpACezfq6af+a7JeqVTWvncqDg=="
  "resolved" "https://registry.npmmirror.com/vue-style-loader/-/vue-style-loader-4.1.3.tgz"
  "version" "4.1.3"
  dependencies:
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.0.2"

"vue-template-compiler@^2.0.0", "vue-template-compiler@^2.6.0", "vue-template-compiler@>=2.5.0", "vue-template-compiler@>=2.6.0 <=2":
  "integrity" "sha512-AYbUWAJHLGGQM7+cNTELw+KsOG9nl2CnSv467WobS5Cv9uk3wFcnr1Etsz2sEIHEZvw1U+o9mRlEO6QbZvUPGQ=="
  "resolved" "https://npm.bangdao-tech.com/vue-template-compiler/-/vue-template-compiler-2.7.16.tgz"
  "version" "2.7.16"
  dependencies:
    "de-indent" "^1.0.2"
    "he" "^1.2.0"

"vue-template-es2015-compiler@^1.9.0":
  "integrity" "sha512-4gDntzrifFnCEvyoO8PqyJDmguXgVPxKiIxrBKjIowvL9l+N66196+72XVYR8BBf1Uv1Fgt3bGevJ+sEmxfZzw=="
  "resolved" "https://registry.npmmirror.com/vue-template-es2015-compiler/-/vue-template-es2015-compiler-1.9.1.tgz"
  "version" "1.9.1"

"vue@*", "vue@^2 || ^3.2.13", "vue@^2.0.0", "vue@^2.6.0", "vue@^2.6.14", "vue@>=2.5.0", "vue@>=2.6.0 <=2", "vue@2.7.14":
  "integrity" "sha512-b2qkFyOM0kwqWFuQmgd4o+uHGU7T+2z3T+WQp8UBjADfEv2n4FEMffzBmCKNP0IGzOEEfYjvtcC62xaSKeQDrQ=="
  "resolved" "https://registry.npmmirror.com/vue/-/vue-2.7.14.tgz"
  "version" "2.7.14"
  dependencies:
    "@vue/compiler-sfc" "2.7.14"
    "csstype" "^3.1.0"

"vue2-editor@^2.10.3":
  "integrity" "sha512-99rWL93xfGeFRrq8NY5L7U+Cog/Uenx+UOOJragtxtbhBE9Rv5/C3P/YhJhjMECSbQyHFjUriqv1S3mghvU9Kg=="
  "resolved" "https://registry.npmjs.org/vue2-editor/-/vue2-editor-2.10.3.tgz"
  "version" "2.10.3"
  dependencies:
    "quill" "^1.3.6"

"vuex@3.6.2":
  "integrity" "sha512-ETW44IqCgBpVomy520DT5jf8n0zoCac+sxWnn+hMe/CzaSejb/eVw2YToiXYX+Ex/AuHHia28vWTq4goAexFbw=="
  "resolved" "https://registry.npmmirror.com/vuex/-/vuex-3.6.2.tgz"
  "version" "3.6.2"

"vxe-table-plugin-antd@1.11.3":
  "integrity" "sha512-y05BQTCOYnVpNZwwRLbjcMyvUSJHoZphVFy5kih2CmfiewOou2b8A1iIUBUUe+SbKzsJ9lMsdivpy7c6brHPIA=="
  "resolved" "https://registry.npmjs.org/vxe-table-plugin-antd/-/vxe-table-plugin-antd-1.11.3.tgz"
  "version" "1.11.3"

"vxe-table@>= 2.10.0 || >= 3.2.0", "vxe-table@>=3.0.19 <=3", "vxe-table@3.6.1", "vxe-table@3.6.6":
  "integrity" "sha512-DKifb0ekFYCu0UkTE0r1BmkRhabnCnS1+ilnTwciuTyC/DzGfwi8s+8LjX7YMV+Jp+hr4CJubIzHPcP41M+JTg=="
  "resolved" "https://registry.npmmirror.com/vxe-table/-/vxe-table-3.6.6.tgz"
  "version" "3.6.6"

"w3c-hr-time@^1.0.2":
  "integrity" "sha512-z8P5DvDNjKDoFIHK7q8r8lackT6l+jo/Ye3HOle7l9nICP9lf1Ci25fy9vHd0JOWewkIFzXIEig3TdKT7JQ5fQ=="
  "resolved" "https://registry.npmmirror.com/w3c-hr-time/-/w3c-hr-time-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "browser-process-hrtime" "^1.0.0"

"w3c-xmlserializer@^2.0.0":
  "integrity" "sha512-4tzD0mF8iSiMiNs30BiLO3EpfGLZUT2MSX/G+o7ZywDzliWQ3OPtTZ0PTC3B3ca1UAf4cJMHB+2Bf56EriJuRA=="
  "resolved" "https://registry.npmmirror.com/w3c-xmlserializer/-/w3c-xmlserializer-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "xml-name-validator" "^3.0.0"

"warning@^4.0.0":
  "integrity" "sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w=="
  "resolved" "https://registry.npmmirror.com/warning/-/warning-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "loose-envify" "^1.0.0"

"watchpack@^2.4.0":
  "integrity" "sha512-Lcvm7MGST/4fup+ifyKi2hjyIAwcdI4HRgtvTpIUxBRhB+RFtUh8XtDOxUfctVCnhVi+QQj49i91OyvzkJl6cg=="
  "resolved" "https://registry.npmmirror.com/watchpack/-/watchpack-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "glob-to-regexp" "^0.4.1"
    "graceful-fs" "^4.1.2"

"wbuf@^1.1.0", "wbuf@^1.7.3":
  "integrity" "sha512-O84QOnr0icsbFGLS0O3bI5FswxzRr8/gHwWkDlQFskhSPryQXvrTMxjxGP4+iWYoauLoBvfDpkrOauZ+0iZpDA=="
  "resolved" "https://registry.npmmirror.com/wbuf/-/wbuf-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "minimalistic-assert" "^1.0.0"

"wcwidth@^1.0.1":
  "integrity" "sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg=="
  "resolved" "https://registry.npmmirror.com/wcwidth/-/wcwidth-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "defaults" "^1.0.3"

"webidl-conversions@^3.0.0":
  "integrity" "sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ=="
  "resolved" "https://registry.npmmirror.com/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
  "version" "3.0.1"

"webidl-conversions@^5.0.0":
  "integrity" "sha512-VlZwKPCkYKxQgeSbH5EyngOmRp7Ww7I9rQLERETtf5ofd9pGeswWiOtogpEO850jziPRarreGxn5QIiTqpb2wA=="
  "resolved" "https://registry.npmmirror.com/webidl-conversions/-/webidl-conversions-5.0.0.tgz"
  "version" "5.0.0"

"webidl-conversions@^6.1.0":
  "integrity" "sha512-qBIvFLGiBpLjfwmYAaHPXsn+ho5xZnGvyGvsarywGNc8VyQJUMHJ8OBKGGrPER0okBeMDaan4mNBlgBROxuI8w=="
  "resolved" "https://registry.npmmirror.com/webidl-conversions/-/webidl-conversions-6.1.0.tgz"
  "version" "6.1.0"

"webpack-bundle-analyzer@^4.4.0":
  "integrity" "sha512-ZzoSBePshOKhr+hd8u6oCkZVwpVaXgpw23ScGLFpR6SjYI7+7iIWYarjN6OEYOfRt8o7ZyZZQk0DuMizJ+LEIg=="
  "resolved" "https://registry.npmmirror.com/webpack-bundle-analyzer/-/webpack-bundle-analyzer-4.8.0.tgz"
  "version" "4.8.0"
  dependencies:
    "@discoveryjs/json-ext" "0.5.7"
    "acorn" "^8.0.4"
    "acorn-walk" "^8.0.0"
    "chalk" "^4.1.0"
    "commander" "^7.2.0"
    "gzip-size" "^6.0.0"
    "lodash" "^4.17.20"
    "opener" "^1.5.2"
    "sirv" "^1.0.7"
    "ws" "^7.3.1"

"webpack-chain@^6.5.1":
  "integrity" "sha512-7doO/SRtLu8q5WM0s7vPKPWX580qhi0/yBHkOxNkv50f6qB76Zy9o2wRTrrPULqYTvQlVHuvbA8v+G5ayuUDsA=="
  "resolved" "https://registry.npmmirror.com/webpack-chain/-/webpack-chain-6.5.1.tgz"
  "version" "6.5.1"
  dependencies:
    "deepmerge" "^1.5.2"
    "javascript-stringify" "^2.0.1"

"webpack-dev-middleware@^5.3.1":
  "integrity" "sha512-hj5CYrY0bZLB+eTO+x/j67Pkrquiy7kWepMHmUMoPsmcUaeEnQJqFzHJOyxgWlq746/wUuA64p9ta34Kyb01pA=="
  "resolved" "https://registry.npmmirror.com/webpack-dev-middleware/-/webpack-dev-middleware-5.3.3.tgz"
  "version" "5.3.3"
  dependencies:
    "colorette" "^2.0.10"
    "memfs" "^3.4.3"
    "mime-types" "^2.1.31"
    "range-parser" "^1.2.1"
    "schema-utils" "^4.0.0"

"webpack-dev-server@^4.7.3":
  "integrity" "sha512-5tWg00bnWbYgkN+pd5yISQKDejRBYGEw15RaEEslH+zdbNDxxaZvEAO2WulaSaFKb5n3YG8JXsGaDsut1D0xdA=="
  "resolved" "https://registry.npmmirror.com/webpack-dev-server/-/webpack-dev-server-4.13.1.tgz"
  "version" "4.13.1"
  dependencies:
    "@types/bonjour" "^3.5.9"
    "@types/connect-history-api-fallback" "^1.3.5"
    "@types/express" "^4.17.13"
    "@types/serve-index" "^1.9.1"
    "@types/serve-static" "^1.13.10"
    "@types/sockjs" "^0.3.33"
    "@types/ws" "^8.5.1"
    "ansi-html-community" "^0.0.8"
    "bonjour-service" "^1.0.11"
    "chokidar" "^3.5.3"
    "colorette" "^2.0.10"
    "compression" "^1.7.4"
    "connect-history-api-fallback" "^2.0.0"
    "default-gateway" "^6.0.3"
    "express" "^4.17.3"
    "graceful-fs" "^4.2.6"
    "html-entities" "^2.3.2"
    "http-proxy-middleware" "^2.0.3"
    "ipaddr.js" "^2.0.1"
    "launch-editor" "^2.6.0"
    "open" "^8.0.9"
    "p-retry" "^4.5.0"
    "rimraf" "^3.0.2"
    "schema-utils" "^4.0.0"
    "selfsigned" "^2.1.1"
    "serve-index" "^1.9.1"
    "sockjs" "^0.3.24"
    "spdy" "^4.0.2"
    "webpack-dev-middleware" "^5.3.1"
    "ws" "^8.13.0"

"webpack-merge@^5.7.3":
  "integrity" "sha512-/SaI7xY0831XwP6kzuwhKWVKDP9t1QY1h65lAFLbZqMPIuYcD9QAW4u9STIbU9kaJbPBB/geU/gLr1wDjOhQ+Q=="
  "resolved" "https://registry.npmmirror.com/webpack-merge/-/webpack-merge-5.8.0.tgz"
  "version" "5.8.0"
  dependencies:
    "clone-deep" "^4.0.1"
    "wildcard" "^2.0.0"

"webpack-sources@*", "webpack-sources@^3.2.3":
  "integrity" "sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w=="
  "resolved" "https://registry.npmmirror.com/webpack-sources/-/webpack-sources-3.2.3.tgz"
  "version" "3.2.3"

"webpack-virtual-modules@^0.4.2":
  "integrity" "sha512-5tyDlKLqPfMqjT3Q9TAqf2YqjwmnUleZwzJi1A5qXnlBCdj2AtOJ6wAWdglTIDOPgOiOrXeBeFcsQ8+aGQ6QbA=="
  "resolved" "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.4.6.tgz"
  "version" "0.4.6"

"webpack@^2.0.0 || ^3.0.0 || ^4.0.0 || ^5.0.0", "webpack@^3.0.0 || ^4.0.0 || ^5.0.0", "webpack@^3.0.0 || ^4.1.0 || ^5.0.0-0", "webpack@^4.0.0 || ^5.0.0", "webpack@^4.1.0 || ^5.0.0-0", "webpack@^4.27.0 || ^5.0.0", "webpack@^4.37.0 || ^5.0.0", "webpack@^5.0.0", "webpack@^5.1.0", "webpack@^5.20.0", "webpack@^5.54.0", "webpack@>=2", "webpack@5.76.3":
  "integrity" "sha512-18Qv7uGPU8b2vqGeEEObnfICyw2g39CHlDEK4I7NK13LOur1d0HGmGNKGT58Eluwddpn3oEejwvBPoP4M7/KSA=="
  "resolved" "https://registry.npmmirror.com/webpack/-/webpack-5.76.3.tgz"
  "version" "5.76.3"
  dependencies:
    "@types/eslint-scope" "^3.7.3"
    "@types/estree" "^0.0.51"
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/wasm-edit" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"
    "acorn" "^8.7.1"
    "acorn-import-assertions" "^1.7.6"
    "browserslist" "^4.14.5"
    "chrome-trace-event" "^1.0.2"
    "enhanced-resolve" "^5.10.0"
    "es-module-lexer" "^0.9.0"
    "eslint-scope" "5.1.1"
    "events" "^3.2.0"
    "glob-to-regexp" "^0.4.1"
    "graceful-fs" "^4.2.9"
    "json-parse-even-better-errors" "^2.3.1"
    "loader-runner" "^4.2.0"
    "mime-types" "^2.1.27"
    "neo-async" "^2.6.2"
    "schema-utils" "^3.1.0"
    "tapable" "^2.1.1"
    "terser-webpack-plugin" "^5.1.3"
    "watchpack" "^2.4.0"
    "webpack-sources" "^3.2.3"

"websocket-driver@^0.7.4", "websocket-driver@>=0.5.1":
  "integrity" "sha512-b17KeDIQVjvb0ssuSDF2cYXSg2iztliJ4B9WdsuB6J952qCPKmnVq4DyW5motImXHDC1cBT/1UezrJVsKw5zjg=="
  "resolved" "https://registry.npmmirror.com/websocket-driver/-/websocket-driver-0.7.4.tgz"
  "version" "0.7.4"
  dependencies:
    "http-parser-js" ">=0.5.1"
    "safe-buffer" ">=5.1.0"
    "websocket-extensions" ">=0.1.1"

"websocket-extensions@>=0.1.1":
  "integrity" "sha512-OqedPIGOfsDlo31UNwYbCFMSaO9m9G/0faIHj5/dZFDMFqPTcx6UwqyOy3COEaEOg/9VsGIpdqn62W5KhoKSpg=="
  "resolved" "https://registry.npmmirror.com/websocket-extensions/-/websocket-extensions-0.1.4.tgz"
  "version" "0.1.4"

"whatwg-encoding@^1.0.5":
  "integrity" "sha512-b5lim54JOPN9HtzvK9HFXvBma/rnfFeqsic0hSpjtDbVxR3dJKLc+KB4V6GgiGOvl7CY/KNh8rxSo9DKQrnUEw=="
  "resolved" "https://registry.npmmirror.com/whatwg-encoding/-/whatwg-encoding-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "iconv-lite" "0.4.24"

"whatwg-fetch@^3.6.2":
  "integrity" "sha512-bJlen0FcuU/0EMLrdbJ7zOnW6ITZLrZMIarMUVmdKtsGvZna8vxKYaexICWPfZ8qwf9fzNq+UEIZrnSaApt6RA=="
  "resolved" "https://registry.npmmirror.com/whatwg-fetch/-/whatwg-fetch-3.6.2.tgz"
  "version" "3.6.2"

"whatwg-mimetype@^2.3.0":
  "integrity" "sha512-M4yMwr6mAnQz76TbJm914+gPpB/nCwvZbJU28cUD6dR004SAxDLOOSUaB1JDRqLtaOV/vi0IC5lEAGFgrjGv/g=="
  "resolved" "https://registry.npmmirror.com/whatwg-mimetype/-/whatwg-mimetype-2.3.0.tgz"
  "version" "2.3.0"

"whatwg-url@^5.0.0":
  "integrity" "sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw=="
  "resolved" "https://registry.npmmirror.com/whatwg-url/-/whatwg-url-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "tr46" "~0.0.3"
    "webidl-conversions" "^3.0.0"

"whatwg-url@^8.0.0", "whatwg-url@^8.5.0":
  "integrity" "sha512-gAojqb/m9Q8a5IV96E3fHJM70AzCkgt4uXYX2O7EmuyOnLrViCQlsEBmF9UQIu3/aeAIp2U17rtbpZWNntQqdg=="
  "resolved" "https://registry.npmmirror.com/whatwg-url/-/whatwg-url-8.7.0.tgz"
  "version" "8.7.0"
  dependencies:
    "lodash" "^4.7.0"
    "tr46" "^2.1.0"
    "webidl-conversions" "^6.1.0"

"which@^1.2.9":
  "integrity" "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ=="
  "resolved" "https://registry.npmmirror.com/which/-/which-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "isexe" "^2.0.0"

"which@^2.0.1":
  "integrity" "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA=="
  "resolved" "https://registry.npmmirror.com/which/-/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"wildcard@^2.0.0":
  "integrity" "sha512-JcKqAHLPxcdb9KM49dufGXn2x3ssnfjbcaQdLlfZsL9rH9wgDQjUtDxbo8NE0F6SFvydeu1VhZe7hZuHsB2/pw=="
  "resolved" "https://registry.npmmirror.com/wildcard/-/wildcard-2.0.0.tgz"
  "version" "2.0.0"

"wmf@~1.0.1":
  "integrity" "sha512-/p9K7bEh0Dj6WbXg4JG0xvLQmIadrner1bi45VMJTfnbVHsc7yIajZyoSoK60/dtVBs12Fm6WkUI5/3WAVsNMw=="
  "resolved" "https://registry.npmmirror.com/wmf/-/wmf-1.0.2.tgz"
  "version" "1.0.2"

"word-wrap@^1.2.3", "word-wrap@~1.2.3":
  "integrity" "sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ=="
  "resolved" "https://registry.npmmirror.com/word-wrap/-/word-wrap-1.2.3.tgz"
  "version" "1.2.3"

"word@~0.4.0":
  "integrity" "sha512-Aq84KjS7Z9HSU14qf4l/NEouaqfJAZtE9zEz7TIvw9V/3oJeUbjQwhz7ggqbL7I7REt4Bz+9HuCWsBO5N7xChw=="
  "resolved" "https://registry.npmmirror.com/word/-/word-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "cfb" "^1.2.0"
    "jsdom" "^16.2.2"

"wrap-ansi@^3.0.1":
  "integrity" "sha512-iXR3tDXpbnTpzjKSylUJRkLuOrEC7hwEB221cgn6wtF8wpmz28puFXAEfPT5zrjM3wahygB//VuWEr1vTkDcNQ=="
  "resolved" "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "string-width" "^2.1.1"
    "strip-ansi" "^4.0.0"

"wrap-ansi@^6.2.0":
  "integrity" "sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA=="
  "resolved" "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-6.2.0.tgz"
  "version" "6.2.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrap-ansi@^7.0.0":
  "integrity" "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q=="
  "resolved" "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrappy@1":
  "integrity" "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ=="
  "resolved" "https://registry.npmmirror.com/wrappy/-/wrappy-1.0.2.tgz"
  "version" "1.0.2"

"ws@^7.3.1", "ws@^7.4.6":
  "integrity" "sha512-F+P9Jil7UiSKSkppIiD94dN07AwvFixvLIj1Og1Rl9GGMuNipJnV9JzjD6XuqmAeiswGvUmNLjr5cFuXwNS77Q=="
  "resolved" "https://registry.npmmirror.com/ws/-/ws-7.5.9.tgz"
  "version" "7.5.9"

"ws@^8.13.0":
  "integrity" "sha512-x9vcZYTrFPC7aSIbj7sRCYo7L/Xb8Iy+pW0ng0wt2vCJv7M9HOMy0UoN3rr+IFC7hb7vXoqS+P9ktyLLLhO+LA=="
  "resolved" "https://registry.npmmirror.com/ws/-/ws-8.13.0.tgz"
  "version" "8.13.0"

"xe-utils@^3.5.0", "xe-utils@>=3.3.1", "xe-utils@3.5.6":
  "integrity" "sha512-oFZWxtTqxR6a2FtTQ1vSD10ab+XUXSz6jWaTaK+CsfbpIWH79jU6DipJJPgw9zxfYigcnJvb98aaT188kqhqcA=="
  "resolved" "https://registry.npmmirror.com/xe-utils/-/xe-utils-3.5.6.tgz"
  "version" "3.5.6"

"xlsx@0.17.2":
  "integrity" "sha512-RIhN6/oc/ZqYZqY4jz4AX92yNfULhtNrcZP1lknIcsyR+Ra8Zu/9F1lAZWncYbDex95iYQX/XNNNzNFXZjlNOQ=="
  "resolved" "https://registry.npmmirror.com/xlsx/-/xlsx-0.17.2.tgz"
  "version" "0.17.2"
  dependencies:
    "adler-32" "~1.2.0"
    "cfb" "^1.1.4"
    "codepage" "~1.15.0"
    "commander" "~2.17.1"
    "crc-32" "~1.2.0"
    "exit-on-epipe" "~1.0.1"
    "fflate" "^0.3.8"
    "ssf" "~0.11.2"
    "wmf" "~1.0.1"
    "word" "~0.4.0"

"xml-name-validator@^3.0.0":
  "integrity" "sha512-A5CUptxDsvxKJEU3yO6DuWBSJz/qizqzJKOMIfUJHETbBw/sFaDxgd6fxm1ewUaM0jZ444Fc5vC5ROYurg/4Pw=="
  "resolved" "https://registry.npmmirror.com/xml-name-validator/-/xml-name-validator-3.0.0.tgz"
  "version" "3.0.0"

"xmlchars@^2.2.0":
  "integrity" "sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw=="
  "resolved" "https://registry.npmmirror.com/xmlchars/-/xmlchars-2.2.0.tgz"
  "version" "2.2.0"

"y18n@^5.0.5":
  "integrity" "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA=="
  "resolved" "https://registry.npmmirror.com/y18n/-/y18n-5.0.8.tgz"
  "version" "5.0.8"

"yallist@^2.1.2":
  "integrity" "sha512-ncTzHV7NvsQZkYe1DW7cbDLm0YpzHmZF5r/iyP3ZnQtMiJ+pjzisCiMNI+Sj+xQF5pXhSHxSB3uDbsBTzY/c2A=="
  "resolved" "https://registry.npmmirror.com/yallist/-/yallist-2.1.2.tgz"
  "version" "2.1.2"

"yallist@^3.0.2":
  "integrity" "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g=="
  "resolved" "https://registry.npmmirror.com/yallist/-/yallist-3.1.1.tgz"
  "version" "3.1.1"

"yallist@^4.0.0":
  "integrity" "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="
  "resolved" "https://registry.npmmirror.com/yallist/-/yallist-4.0.0.tgz"
  "version" "4.0.0"

"yaml@^1.10.0", "yaml@^1.10.2":
  "integrity" "sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg=="
  "resolved" "https://registry.npmmirror.com/yaml/-/yaml-1.10.2.tgz"
  "version" "1.10.2"

"yargs-parser@^20.2.2":
  "integrity" "sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w=="
  "resolved" "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-20.2.9.tgz"
  "version" "20.2.9"

"yargs@^16.0.0":
  "integrity" "sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw=="
  "resolved" "https://registry.npmmirror.com/yargs/-/yargs-16.2.0.tgz"
  "version" "16.2.0"
  dependencies:
    "cliui" "^7.0.2"
    "escalade" "^3.1.1"
    "get-caller-file" "^2.0.5"
    "require-directory" "^2.1.1"
    "string-width" "^4.2.0"
    "y18n" "^5.0.5"
    "yargs-parser" "^20.2.2"

"yarn@^1.22.19":
  "integrity" "sha512-/0V5q0WbslqnwP91tirOvldvYISzaqhClxzyUKXYxs07yUILIs5jx/k6CFe8bvKSkds5w+eiOqta39Wk3WxdcQ=="
  "resolved" "https://registry.npmjs.org/yarn/-/yarn-1.22.19.tgz"
  "version" "1.22.19"

"yorkie@^2.0.0":
  "integrity" "sha512-jcKpkthap6x63MB4TxwCyuIGkV0oYP/YRyuQU5UO0Yz/E/ZAu+653/uov+phdmO54n6BcvFRyyt0RRrWdN2mpw=="
  "resolved" "https://registry.npmmirror.com/yorkie/-/yorkie-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "execa" "^0.8.0"
    "is-ci" "^1.0.10"
    "normalize-path" "^1.0.0"
    "strip-indent" "^2.0.0"

"zrender@5.4.4":
  "integrity" "sha512-0VxCNJ7AGOMCWeHVyTrGzUgrK4asT4ml9PEkeGirAkKNYXYzoPJCLvmyfdoOXcjTHPs10OZVMfD1Rwg16AZyYw=="
  "resolved" "https://registry.npmjs.org/zrender/-/zrender-5.4.4.tgz"
  "version" "5.4.4"
  dependencies:
    "tslib" "2.3.0"
