/** @format */
import { constantRoutes } from '@/router/config';
import { formatMenuData, getFirstSiteMenuPath } from '@/router/utils';

export default {
  namespaced: true,
  state: {
    menuData: [],
    menuMessageData: [],
    sysFirstMenuPath: '',
    collapsed: false,
  },
  mutations: {
    setMenuData (state, routerData = []) {
      const newRouter = [...constantRoutes].concat(routerData).filter((item) => {
        return !item.hidden;
      });
      const menuData = formatMenuData(newRouter);
      console.log('menuData', menuData);
      state.menuData = menuData;
      // 系统管理相关菜单
      state.sysFirstMenuPath = getFirstSiteMenuPath(
        formatMenuData(
          routerData.filter((item) => {
            return !item.hidden;
          })
        )
      );
    },
    setSideMenuCollapsed (state, collapsed) {
      state.collapsed = collapsed;
    },
  },
};
