import { login, logout, getInfo, getRouters } from '@/api/system/base';
import { filterAsyncRouter } from '@/utils/system/common/routerUtil';
import { constantRoutes } from '@/router/config';
import { getToken, setToken, removeToken, removeTokenWithDomain } from '@/utils/system/common/auth';
import { setUser, removeUser, getUser, getUserProperty } from '@/utils/system/common/user-persistent';
import { getTypeList } from '@/api/system/workorder';
import { getAllUser } from '@/api/system/settings';

export default {
  namespaced: true,
  state: {
    token: getToken(),
    name: getUserProperty('userName'),
    username: getUserProperty('userName'),
    avatar: getUserProperty('avatar'),
    email: getUserProperty('email'),
    merchant: getUserProperty('merchant'),
    user: getUser(),
    sysApps: [],
    permissions: [],
    routes: [],
    addRoutes: [],
    orderSort: [],
    userList: [],
    orderId: [], //跳转页面id,orderId
    arrStatus: [], //跳转页面orderId的状态，
    arrTime: [], //跳转页面orderTime
    cumId: '', //通话记录id，长id
    mainUniqueId: '', //38接口要的id
    isCall: false, //打电话过来了，我要如何处理
    customerNumber: '', //电话获取访客详情
    tabIndex: '', // 工单工作台tab索引
    callNumber: '', //呼出电话号码
  },
  getters: {
    permissions: (state) => {
      return state.permissions;
    },
  },
  mutations: {
    // 删除应用信息，重新获取用户
    RESET_BASE_INFO: (state) => {
      state.sysApps = [];
    },
    // 设置用户基础信息
    SET_BASE_INFO: (state, data) => {
      const { merchant = {}, user = {}, sysApps = [], permissions = [] } = data;

      state.name = user.userName || '';
      state.username = user.userName || '';
      state.avatar = require('@/assets/images/profile.jpg');
      // state.avatar = user.avatar
      //   ? process.env.VUE_APP_BASE_API + user.avatar
      //   : require('@/assets/images/profile.jpg');

      if (permissions.length > 0) {
        state.permissions = permissions;
      }
      state.user = user;
      setUser({ ...user, merchant: merchant });
      state.sysApps = sysApps;
      state.merchant = merchant;
    },
    // 设置路由
    SET_ROUTES: (state, routes) => {
      state.addRoutes = routes;
      state.routes = constantRoutes.concat(routes);
    },
    SET_TOKEN: (state, token) => {
      state.token = token;
      setToken(token);
    },
    SET_ORDER_SORT: (state, data) => {
      state.orderSort = data;
    },
    SET_USER_LIST: (state, data) => {
      state.userList = data;
    },
    RESET_STATE: (state) => {
      removeToken();
      removeTokenWithDomain();
      state.token = '';
      state.name = '';
      state.avatar = '';
      state.user = {};
      removeUser();
      state.sysApps = [];
      state.permissions = [];
      state.routes = [];
      state.addRoutes = [];
    },
    //下面三个和telmanager相关
    GET_TELID(state, value) {
      state.orderId = value;
      console.log('store内orderId', value);
    },
    PHONE_CALL(state, value) {
      state.isCall = value;
    },
    CALL_NUMBER(state, value) {
      state.callNumber = value;
    },
    MAIN_ID(state, value) {
      state.mainUniqueId = value;
    },
    PHONE_GET(state, value) {
      state.customerNumber = value;
    },
    CUMID_GET(state, value) {
      state.cumId = value;
    },
    STATUS_GET(state, value) {
      state.arrStatus = value;
    },
    TIME_GET(state, value) {
      state.arrTime = value;
    },
    SET_TAB(state, value) {
      state.tabIndex = value;
    },
  },

  actions: {
    // 获取用户信息
    async GetInfo({ commit }) {
      const token = getToken();
      const [res, err] = await getInfo(token);
      if (err) throw new Error(err);
      const { data } = res;
      commit('SET_BASE_INFO', data);
      return data;
    },

    // 生成路由
    async GenerateRoutes({ commit }, { appId = '' }) {
      // 向后端请求路由数据
      if (!appId) return;
      const [systemRes, err] = await getRouters({ appId });
      if (err) throw new Error(err);
      // 系统管理菜单
      let systemRoutes = filterAsyncRouter(systemRes.data);
      // 菜单综合
      const dynamicRoutes = systemRoutes;
      commit('SET_ROUTES', dynamicRoutes);
      return dynamicRoutes;
    },

    //  获取工单分类
    async GetOrderSort({ commit }) {
      const [res, err] = await getTypeList();
      if (err) throw new Error(err);
      const { data } = res;
      commit('SET_ORDER_SORT', data);
      return data;
    },

    //  获取用户列表
    async GetUserList({ commit }, { nickName }) {
      const [res, err] = await getAllUser(nickName);
      if (err) throw new Error(err);
      const { data } = res;
      commit('SET_USER_LIST', data);
      return data;
    },

    // 登录
    async Login({ commit }, { username, password, code, requestNo }) {
      commit('RESET_STATE');
      const [res, err] = await login(username, password, code, requestNo);
      if (err) return [undefined, err];
      const token = res?.data?.token;
      commit('SET_TOKEN', token);
      return ['success', undefined];
    },

    // 退出系统
    async LogOut({ commit }) {
      const token = getToken();
      await logout(token);
      commit('RESET_STATE');
    },

    // 前端 登出
    FedLogOut({ commit }) {
      commit('RESET_STATE');
    },

    //orderId获取
    orderIdFn({ commit }, value) {
      commit('GET_TELID', value);
    },

    //打电话
    isCallFn({ commit }, value) {
      commit('PHONE_CALL', value);
    },
    //设置外呼客户号码
    callNumberFn({ commit }, value) {
      commit('CALL_NUMBER', value);
    },

    //cummunicateId获取
    cumIdFn({ commit }, value) {
      commit('CUMID_GET', value);
    },

    mainIdFn({ commit }, value) {
      commit('MAIN_ID', value);
    },

    numberFn({ commit }, value) {
      commit('PHONE_GET', value);
    },
    statusFn({ commit }, value) {
      commit('STATUS_GET', value);
    },
    timeFn({ commit }, value) {
      commit('TIME_GET', value);
    },
    tabIndexGet({ commit }, value) {
      commit('SET_TAB', value);
    },
  },
};
