export default {
  namespaced: true,
  state: {
    dict: new Array(),
  },
  mutations: {
    SET_DICT: (state, { key, value }) => {
      if (key !== null && key !== '') {
        state.dict.push({
          key: key,
          value: value,
        });
      }
    },
    REMOVE_DICT: (state, key) => {
      try {
        for (let i = 0; i < state.dict.length; i++) {
          if (state.dict[i].key == key) {
            state.dict.splice(i, i);
            return true;
          }
        }
        // eslint-disable-next-line no-empty
      } catch (e) {}
    },
    CLEAN_DICT: (state) => {
      state.dict = new Array();
    },
  },
  actions: {
    // 设置字典
    setDict({ commit }, data) {
      commit('SET_DICT', data);
    },
    // 删除字典
    removeDict({ commit }, key) {
      commit('REMOVE_DICT', key);
    },
    // 清空字典
    cleanDict({ commit }) {
      commit('CLEAN_DICT');
    },
    // 获取字典
    getDict({ state }, dictKeys) {
      if (dictKeys == null && dictKeys == '') {
        return null;
      }
      try {
        for (let i = 0; i < state.dict.length; i++) {
          console.log(state.dict[i]);
          if (state.dict[i].key == dictKeys) {
            return state.dict[i].value;
          }
        }
      } catch (e) {
        return null;
      }
    },
    /**
     * 批量获取字典值
     * @param {*} store
     * @param {string|string[]} dictKeys
     * @returns
     */
    getDicts({ dispatch }, dictKeys) {
      if (!dictKeys) return;
      let dictKeysArr = [];
      if (Array.isArray(dictKeys)) {
        dictKeysArr = dictKeys;
      } else {
        dictKeysArr = [dictKeys];
      }
      return Promise.all(
        dictKeysArr.map((dictKey) => {
          return dispatch('getDict', dictKey);
        })
      );
    },
  },
};
