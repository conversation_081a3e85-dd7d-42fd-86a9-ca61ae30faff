<template>
  <a-config-provider :locale="locale" :get-popup-container="popContainer">
    <router-view ref="box"> </router-view>
  </a-config-provider>
</template>

<script>
import zh_CN from 'ant-design-vue/lib/locale-provider/zh_CN';
import { requestJson } from '@/utils/system/request/requestJson';

export default {
  name: 'App',
  data() {
    return {
      locale: zh_CN,
    };
  },
  watch: {
    '$store.state.permission.routes': function (val) {
      if (val && val.length > 0) {
        // 有路由信息，则展示版本更新弹窗
        this.showUpdateInfoModal();
      }
    },
  },
  created() {
    this.setHtmlTitle();
  },
  mounted() {
    window.onbeforeunload = function (e) {
      e.preventDefault();
      return (e.realValue = '刷新后将丢失工作条登陆信息!');
    };
  },
  methods: {
    setHtmlTitle() {
      document.title = process.env.VUE_APP_NAME;
    },
    popContainer() {
      return document.getElementById('popContainer');
    },
    // 展示版本更新弹窗
    async showUpdateInfoModal() {
      const version = process.env.VUE_APP_VERSION;
      const updateInfoShowedLocalStorageKey = `${process.env.VUE_APP_NAME}_UPDATE_INFO_SHOWED_VERSION`;

      const showedVersion = window.localStorage.getItem(updateInfoShowedLocalStorageKey);
      // 无版本信息 或者 缓存已展示的版本和当前版本一致，则不再展示弹窗
      if (!version || showedVersion === version) return;
      // 获取当前版本更新信息
      const [res, error] = await requestJson(
        process.env.VUE_APP_VERSION_INFO_URL.replace('#version#', version) + '?v=' + new Date().getTime()
      );
      if (error) return;
      // 展示弹窗
      const { title, updateDate, version: versionText, functionList = [] } = res || {};
      this.$info({
        title: title,
        width: 800,
        content: (
          <div class="update-info" style="overflow: auto; width: 100%;min-height: 400px;max-height: 600px;">
            <p style="font-size: 16px;">版本：{versionText} </p>
            <p style="font-size: 16px;">更新时间：{updateDate} </p>
            {functionList.map(({ title, desc }) => {
              return (
                <div class="update-info">
                  <h3 style="margin-top: 20px;">{title}</h3>
                  <p domPropsInnerHTML={desc} />
                </div>
              );
            })}
          </div>
        ),
        onOk() {
          // 点击知道了按钮，标记改版本弹窗已展示过，不在提示
          window.localStorage.setItem(updateInfoShowedLocalStorageKey, version);
        },
      });
    },
  },
};
</script>

<style lang="less"></style>
