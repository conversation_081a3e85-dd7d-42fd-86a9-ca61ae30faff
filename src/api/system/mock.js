// 总览需要
// 获取卡片数据&颜色
export const cardData = [
  {
    text: '新建工单量',
    nums: 20,
  },
  {
    text: '完成工单量',
    nums: 10,
  },
  {
    text: '升级工单量',
    nums: 8,
  },
  {
    text: '升级工单完结量',
    nums: 12,
  },
];
// 工作台tabs
export const workbenchTabs = [
  {
    label: '全部工单',
    value: 'workall',
    queryType: 6,
  },
  {
    label: '待处理的工单',
    value: 'waiting',
    queryType: 1,
  },
  {
    label: '我提交的工单',
    value: 'submitted',
    queryType: 2,
  },
  {
    label: '我处理的工单',
    value: 'handled',
    queryType: 3,
  },
  {
    label: '我关注的工单',
    value: 'followed',
    queryType: 4,
  },
  {
    label: '工单池',
    value: 'workpool',
    queryType: 5,
  },
];
// 任务提醒
export const tipStatu = [
  {
    statu: '紧急状态',
    nums: 10,
    label: 'exigency',
  },
  {
    statu: '超时状态',
    nums: 4,
    label: 'overtime',
  },
  {
    statu: '催单状态',
    nums: 6,
    label: 'urging',
  },
  {
    statu: '待处理状态',
    nums: 6,
    label: 'waiting',
  },
];
export const chartDataMock = [
  {
    date: '08-21',
    newOrderCount: 80,
    finishOrderCount: 99,
  },
  {
    date: '08-22',
    newOrderCount: 90,
    finishOrderCount: 69,
  },
  {
    date: '08-23',
    newOrderCount: 70,
    finishOrderCount: 89,
  },
  {
    date: '08-24',
    newOrderCount: 50,
    finishOrderCount: 49,
  },
  {
    date: '08-25',
    newOrderCount: 30,
    finishOrderCount: 29,
  },
  {
    date: '08-26',
    newOrderCount: 81,
    finishOrderCount: 79,
  },
  {
    date: '08-27',
    newOrderCount: 83,
    finishOrderCount: 91,
  },
];
// 工作台筛选器需要
export const tableMockData = {
  count: 3,
  data: [
    {
      orderId: '123456',
      createByName: '章三',
      urgency: '非常紧急',
      categoryName: '投诉类-投诉类1',
      orderStatus: '处理中',
      orderSource: '新电途',
      flowType: '人工流转',
      phoneNumber: '123456',
      createTime: '08-10 16:17',
      handler: '里斯',
      followFlag: 1,
      replyTime: '8-16 17:23',
      expireFlag: 0,
      remindFlag: 1,
      orderFollowId: '123',
    },
    {
      orderId: '123456',
      createByName: '章三',
      urgency: '一般',
      categoryName: '投诉类-投诉类1',
      orderStatus: '已完成',
      orderSource: '新电途',
      flowType: '自动分配哦',
      phoneNumber: '123456',
      createTime: '08-11 13:14',
      handler: '里斯',
      followFlag: 0,
      replyTime: '8-16 17:23',
      expireFlag: 1,
      remindFlag: 0,
      orderFollowId: '456',
    },
    {
      orderId: '123456',
      createByName: '章三',
      urgency: '紧急',
      categoryName: '投诉类-投诉类2',
      orderStatus: '未受理',
      orderSource: '新电途',
      flowType: '工单池',
      phoneNumber: '123456',
      createTime: '08-11 15:18',
      handler: '里斯',
      followFlag: 1,
      replyTime: '8-16 17:23',
      expireFlag: 1,
      remindFlag: 1,
      orderFollowId: '282',
    },
  ],
};

export const watingMockData = {
  count: 2,
  data: [
    {
      orderId: '555666',
      createByName: '张五',
      urgency: '紧急',
      categoryName: '投诉类-投诉3',
      orderStatus: '待处理',
      orderSource: '新电途',
      flowType: '自动分配',
      phoneNumber: '666',
      createTime: '08-11 16:17',
      handler: '石头',
      followFlag: 1,
      replyTime: '8-16 17:23',
      expireFlag: 1,
      remindFlag: 0,
      orderFollowId: 'qqq3',
    },
    {
      orderId: '123456',
      createByName: '三',
      urgency: '低',
      categoryName: '投诉类-投诉1',
      orderStatus: '待处理',
      orderSource: '新电途',
      flowType: '工单池',
      phoneNumber: '4444',
      createTime: '08-12 15:18',
      handler: '略略',
      followFlag: 0,
      replyTime: '8-16 17:23',
      expireFlag: 0,
      remindFlag: 0,
      orderFollowId: '11c3',
    },
  ],
};

export const orderStatuDict = [
  {
    label: '已处理',
    value: '1',
    key: 1,
  },
  {
    label: '已完结',
    value: '2',
    key: 2,
  },
  {
    label: '处理中',
    value: '3',
    key: 3,
  },
  {
    label: '待处理',
    value: '4',
    key: 4,
  },
];

export const orderSortData = {
  code: '00000',
  message: null,
  count: 1,
  data: [
    {
      categoryName: '投诉类',
      handleGroup: '8888',
      handleGroupName: '宝宝8士',
      handleUser: '6666',
      handlevalueName: '啊楷',
      categoryId: 'a-b',
      children: [
        {
          categoryName: '投诉1',
          handleGroup: '8888',
          handleGroupName: '宝宝8士',
          handleUser: '6666',
          handlevalueName: '啊楷',
          categoryId: 'a-b-1',
          parentId: 'a-b',
          children: [
            {
              categoryName: '投诉1-2',
              handleGroup: '8888',
              handleGroupName: '宝宝8士',
              handleUser: '6666',
              handlevalueName: '啊楷',
              categoryId: 'a-b-0-1',
              parentId: 'a-b-1',
            },
          ],
        },
        {
          categoryName: '投诉2',
          handleGroup: '8888',
          handleGroupName: '宝宝8士',
          handleUser: '6666',
          handlevalueName: '啊楷',
          categoryId: 'a-b-2',
        },
        {
          categoryName: '投诉3',
          handleGroup: '8888',
          handleGroupName: '宝宝8士',
          handleUser: '6666',
          handlevalueName: '啊楷',
          categoryId: 'a-b-3',
        },
      ],
    },
    {
      categoryName: '故障类',
      categoryId: 'a-c',
      children: [
        {
          categoryName: '故障1',
          handleGroup: '8888',
          handleGroupName: '宝宝8士',
          handleUser: '6666',
          handlevalueName: '啊楷',
          categoryId: 'a-c-1',
          parentId: 'a-c',
        },
        {
          categoryName: '故障2',
          handleGroup: '8888',
          handleGroupName: '宝宝8士',
          handleUser: '6666',
          handlevalueName: '啊楷',
          categoryId: 'a-c-2',
          parentId: 'a-c',
        },
        {
          categoryName: '故障3',
          handleGroup: '8888',
          handleGroupName: '宝宝8士',
          handleUser: '6666',
          handlevalueName: '啊楷',
          categoryId: 'a-c-3',
          parentId: 'a-c',
        },
      ],
    },
  ],
};

export const submiterMock = [
  {
    label: '陈亿达',
    value: 'yd',
    key: 'yd',
  },
  {
    label: '洪柳洲',
    value: 'hz',
    key: 'hz',
  },
  {
    label: '吴悦洋',
    value: 'yy',
    key: 'yy',
  },
  {
    label: '方小能',
    value: 'xn',
    key: 'xn',
  },
];
// 导航栏需要
export const contactData = [
  {
    title: '新电途',
    callNum: 5,
    answerNum: 8,
  },
  {
    title: '大都会',
    callNum: 7,
    answerNum: 10,
  },
  {
    title: '灵锡',
    callNum: 1,
    answerNum: 9,
  },
];
export const queueDataMock = [
  {
    title: '新电途',
    num: 5,
  },
  {
    title: '大都会',
    num: 6,
  },
  {
    title: '灵锡',
    num: 0,
  },
];
export const workLine = [
  {
    label: '新电途',
    value: 'xdt',
  },
  {
    label: '大都会',
    value: 'ddh',
  },
  {
    label: '灵锡',
    value: 'lx',
  },
];
export const workStatu = [
  {
    label: '在线',
    value: 'online',
  },
  {
    label: '接单中',
    value: 'working',
  },
  {
    label: '忙碌',
    value: 'busy',
  },
];
// 工单详情
export const workDetail = {
  orderId: '12467811',
  createByName: '啊洋',
  createTime: '2023-07-23',
  orderSource: '心电图',
  categoryName: '投诉类-投诉4',
  urgency: '一般',
  content: '这是正文',
  orderStatus: '待处理',
  handleUserName: '啊达',
  isHandle: false,
  isDrop: true,
  replyContent: '这是草稿',
  reply: [
    {
      operator: '啊洋',
      avatar: 'https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png',
      replyContent: '???你怎么搞的',
      groupName: '啊sirs',
      replyDate: '2023-08-07',
      replyTime: '12:30:45',
    },
    {
      operator: '客服',
      avatar: 'https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png',
      replyContent: '不服',
      groupName: '啊sirs',
      replyDate: '2023-08-07',
      replyTime: '12:31:15',
    },
    {
      operator: '啊洋',
      avatar: 'https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png',
      replyContent: '来干',
      groupName: '啊sirs',
      replyDate: '2023-08-07',
      replyTime: '12:31:25',
    },
    {
      operator: '客服',
      avatar: 'https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png',
      replyContent: '七七七',
      groupName: '啊sirs',
      replyDate: '2023-08-07',
      replyTime: '12:31:55',
    },
  ],
  flow: [
    {
      operator: '啊洋',
      groupName: '技术体验部',
      operatorType: '新建工单',
      orderStatus: '待处理->处理中',
      handlevalueName: '啊达',
      handleGoupName: '技术体验部',
      createTime: '2023-08-15 16:03:45',
    },
    {
      operator: '啊达',
      groupName: '技术体验部',
      operatorType: '处理工单',
      orderStatus: '处理中->已处理',
      handlevalueName: '啊洲',
      handleGoupName: '技术体验部',
      createTime: '2023-08-15 17:33:12',
    },
    {
      operator: '啊洲',
      groupName: '技术体验部',
      operatorType: '完结工单',
      orderStatus: '已处理->已完结',
      handlevalueName: '啊能',
      handleGoupName: '技术体验部',
      createTime: '2023-08-15 17:53:33',
    },
  ],
};

export const departmentData = [
  {
    label: '产品技术中心',
    principal: '啊杰',
    value: '1-1',
    key: '1-1',
    children: [
      {
        label: '技术体验部',
        key: '1-1-1',
        value: '1-1-1',
        principal: '啊杰',
        children: [
          {
            career: '前端开发工程师',
            label: '啊洋',
            key: '1-1-1-1',
            value: '1-1-1-1',
          },
          {
            career: 'Android开发工程师',
            label: '啊能',
            key: '1-1-1-2',
            value: '1-1-1-2',
          },
        ],
      },
    ],
  },
  {
    label: '应用开发中心',
    principal: '啊冰',
    value: '1-2',
    key: '1-2',
    children: [
      {
        label: '应用开发部',
        key: '1-2-1',
        value: '1-2-1',
        principal: '啊冰',
        children: [
          {
            career: '应用开发工程师',
            label: '啊强',
            key: '1-2-1-1',
            value: '1-2-1-1',
          },
          {
            career: '软件开发工程师',
            label: '啊翔',
            key: '1-2-1-2',
            value: '1-2-1-2',
          },
        ],
      },
    ],
  },
];

export const replyTemp = {
  count: 2,
  data: [
    {
      defaultReplyId: '123',
      replyTitle: '问候',
      replyContent: '你好呀，生活愉快！',
      categoryNameList: ['投诉类', '充电类'],
      lastUpdateTime: '2023-08-17 14:31:22',
      updateByName: '啊能',
      enableFlag: 1,
    },
    {
      defaultReplyId: '456',
      replyTitle: '感谢',
      replyContent: '谢谢侬！',
      categoryNameList: ['all'],
      lastUpdateTime: '2023-08-17 14:33:22',
      updateByName: '啊能',
      enableFlag: 0,
    },
  ],
};

export const orderTempData = {
  count: 2,
  data: [
    {
      templateName: '基础模板',
      orderTemplateId: '123',
      fieldNameList: [
        {
          fieldName: '出生年月',
          fieldKey: 'birth_year_mon',
          fieldType: 'date',
          fieldDesc: '请输入出生年月',
          defaultValue: '1999-01-01',
          emptyFlag: true,
        },
        {
          fieldName: '车辆类型',
          fieldKey: 'car_type',
          fieldType: 'text',
          fieldDesc: '请输入车辆类型',
          defaultValue: '喷气式回旋电动小摩托',
          emptyFlag: true,
        },
      ],
      lastUpdateTime: '2023-08-18 15:23:41',
      lastUpdateUser: '啊能',
      categoryList: ['a-b-2', 'a-b-3'],
      enableFlag: 1,
    },
    {
      orderTemplateId: '456',
      templateName: '高阶模板',
      fieldNameList: [
        {
          fieldName: '身份证号',
          fieldKey: 'identify_id',
          fieldType: 'long',
          fieldDesc: '请输入身份证号',
          defaultValue: '12345612345678123456',
          emptyFlag: false,
        },
        {
          fieldName: '家庭住址',
          fieldKey: 'address',
          fieldType: 'text',
          fieldDesc: '请输入家庭住址',
          defaultValue: '1999-01-01',
          emptyFlag: true,
        },
      ],
      lastUpdateTime: '2023-08-18 15:28:11',
      lastUpdateUser: '啊洋',
      categoryList: ['a-c-1', 'a-b-2'],
      enableFlag: 0,
    },
  ],
};

export const allotList = {
  count: 2,
  data: [
    {
      orderTakerId: '123',
      name: '啊洲',
      department: '产品技术中心',
      systemAllotNum: 4,
      lastAllotTime: '2023-08-21 09:30:31',
      enable: true,
    },
    {
      orderTakerId: '456',
      name: '啊洋',
      department: '产品技术中心',
      systemAllotNum: 9,
      lastAllotTime: '2023-08-17 14:25:33',
      enable: false,
    },
  ],
};

export const inspectList = {
  count: 3,
  data: [
    {
      checkTemplateId: '123',
      templateName: '普通模板',
      templateKey: 'normal',
      projectName: '服务态度',
      projectScore: '20',
      createTime: '2023-08-30 12:12:12',
      createByName: '啊sir',
    },
    {
      checkTemplateId: '123',
      templateName: '普通模板',
      templateKey: 'normal',
      projectName: '解决问题能力',
      projectScore: '50',
      createTime: '2023-08-30 12:12:12',
      createByName: '啊sir',
    },
    {
      checkTemplateId: '123',
      templateName: '普通模板',
      templateKey: 'normal',
      projectName: '及时答复',
      projectScore: '30',
      createTime: '2023-08-30 12:12:12',
      createByName: '啊sir',
    },
  ],
};

export const fieldType = [
  {
    label: '文本',
    value: 1,
  },
  {
    label: '下拉列表',
    value: 2,
  },
  {
    label: '日期',
    value: 3,
  },
];

export const allUserList = {
  code: '10000',
  data: [
    {
      valueId: 'ORG1001',
      valueName: '邦道科技',
      userList: [
        {
          valueId: '1zdc',
          valueName: 'SmartTube',
          userStatus: 1,
        },
        {
          valueId: 'qc2',
          valueName: 'SmartTube2',
          userStatus: 2,
        },
      ],
    },
    {
      valueId: 'ORG1002',
      valueName: '邦道科技zero',
      userList: [
        {
          valueId: 'dqdc21',
          valueName: 'SmartTube21',
          userStatus: 2,
        },
        {
          valueId: 'dq4',
          valueName: 'SmartTube4',
          userStatus: 3,
        },
      ],
    },
  ],
  mas: '成功',
  success: true,
};
