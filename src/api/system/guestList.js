import request from '@/utils/system/request';

// 关注/取关
export function followAPI(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/workOrder/follow',
    method: 'post',
    data: params,
  });
}

// 访客列表
export function listAPI(params) {
  console.log(params);
  console.log('客户列表接口');
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/visitor/list',
    method: 'post',
    data: params,
  });
}

// 访客新增
export function exportAPI(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/visitor/export',
    method: 'post',
    data: params,
    responseType: 'blob',
  });
}

// 访客详情
export function detailAPI(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/visitor/detail',
    method: 'post',
    data: params,
  });
}

// 访客新增
export function addAPI(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/visitor/add',
    method: 'post',
    data: params,
  });
}
