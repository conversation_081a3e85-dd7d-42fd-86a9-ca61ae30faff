import request from '@/utils/system/request';

// 获取工单列表
export function getOrderList(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/workOrder/list',
    method: 'post',
    data: params,
  });
}

// 导出全部工单
export function exportOrderList(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/workOrder/export',
    method: 'post',
    data: params,
    responseType: 'blob',
  });
}

// 获取工单分类列表
export function getTypeList(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/category/list',
    method: 'post',
    data: params ? params : {},
  });
}

// 获取工单详情
export function getOrderDetail(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/workOrder/detail',
    method: 'post',
    data: params,
  });
}

// 我提交的工单-编辑工单
export function editOrder(orderId, mainUniqueId) {
  return request({
    url:
      process.env.VUE_APP_SYSTEM_API +
      `/customer-ticket/workOrder/getEditOrder?orderId=${orderId}&mainUniqueId=${mainUniqueId}`,
    method: 'get',
  });
}

// 关注工单
export function orderFollow(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/workOrder/follow',
    method: 'post',
    data: params,
  });
}

// 接单
export function orderTake(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/workOrder/takeOrder',
    method: 'post',
    data: params,
  });
}

// 催单
export function orderRemind(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/workOrder/remind',
    method: 'post',
    data: params,
  });
}

// 废弃工单
export function orderDrop(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/workOrder/drop',
    method: 'post',
    data: params,
  });
}

// 工单处理（流转、回复）
export function orderHandle(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/workOrder/handle',
    method: 'post',
    data: params,
  });
}

// 工单处理（保存草稿）
export function orderSaveDraft(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/workOrder/reply',
    method: 'post',
    data: params,
  });
}

// 获取工单看板信息
export function orderBoard(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/workOrder/board',
    method: 'post',
    data: params,
  });
}

// 获取坐席数据
export function orderQueue(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/tr/queue/count',
    method: 'post',
    data: params,
  });
}

// 工台工作台获取通话历史
export function getHistoryCall(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/communicate/checkList',
    method: 'post',
    data: params,
  });
}
