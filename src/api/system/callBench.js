import request from '@/utils/system/request';
//工单分类列表
export function workTypeAPI(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/category/list',
    method: 'post',
    data: params ? params : {},
  });
}

//根据工单分类获取默认的流转方式
export function flowTypeAPI(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/category/flowType?categoryId=' + params,
    method: 'get',
  });
}
// 在选择自动分配之后触发，自动获取受理人/受理组参数
export function getAutoAllocationAPI(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/category/getAutoAllocation?categoryId=' + params,
    method: 'get',
  });
}

//根据工单分类获取工单模版
export function listAPI(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/order/template/list?categoryId=' + params,
    method: 'get',
  });
}

//人工选择的时候获取当前用户所属业务线下面的所有用户
export async function userListAPI() {
  let [result] = await request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/user/list',
    method: 'get',
  });
  if (result.code == '10000') {
    return result.data;
  } else {
    console.error('获取受理人受理组时发生了错误');
    return { data: [] };
  }
}

//历史工单关注取关的接口
export function followAPI(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/workOrder/follow',
    method: 'post',
    data: params,
  });
}

//创建新工单
export function addAPI(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/workOrder/add',
    method: 'post',
    data: params,
  });
}

//呼叫工作台-获取通话记录
export function callListAPI(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/communicate/list',
    method: 'post',
    data: params,
  });
}

//访客详情
export function detailAPI(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/visitor/detail',
    method: 'post',
    data: params,
  });
}

// 访客编辑
export function editVisitorAPI(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/visitor/add',
    method: 'post',
    data: params,
  });
}
// 历史工单
export function orderListAPI(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/workOrder/list',
    method: 'post',
    data: params,
  });
}
// 草稿接口
export function orderDraftAPI(id, uid) {
  console.log('id和uid', id, uid);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + `/customer-ticket/workOrder/getEditOrder?orderId=${id}&mainUniqueId=${uid}`,
    method: 'get',
  });
}
// 电话查重接口
export function checkPhone(value) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + `/customer-ticket/visitor/checkPhone?phoneNumber=${value}`,
    method: 'get',
  });
}
// 获取站点信息列表
export function stationList(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/maintenance/station/list',
    method: 'post',
    data: params,
  });
}
//获取运维人员列表
export function maintenanceList(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/maintenance/user/list',
    method: 'post',
    data: params,
  });
}
//工单创建失败-重试
export function retry(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/workOrder/retry',
    method: 'post',
    data: params,
  });
}
//工单台账创建失败-重试
export function retryLedger(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/workOrder/retryLedgerOrder',
    method: 'post',
    data: params,
  });
}

//批量创建工单
export function batchAddAPI(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/workOrder/batchAdd',
    method: 'post',
    data: params,
  });
}
