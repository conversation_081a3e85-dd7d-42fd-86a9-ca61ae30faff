import request from '@/utils/system/request';

// 查询字典数据列表
export function listData(query) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/api/authority/admin/dict/data/list',
    method: 'get',
    params: query,
  });
}

// 查询字典数据详细
export function getData(dictCode) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/api/authority/admin/dict/data/' + dictCode,
    method: 'get',
  });
}

// 根据字典类型查询字典数据信息
export function getDict(dictCode, query) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/digital-portal/api/authority/admin/dict/data/dictCode/' + dictCode,
    method: 'get',
    params: query,
  });
}
// 根据字典类型查询字典数据信息
export function getDicts(data) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/api/authority/admin/dict/data/batch',
    method: 'POST',
    data,
  });
}
//  获取所有字典key
export function allDict() {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/api/common/dict/typeList',
    method: 'get',
  });
}
//  根据字典key获取字典数据
export function getDictByKey(dictCode) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/api/common/dict/data' + dictCode,
    method: 'get',
  });
}
//  获取所有字典key
export function newAllDict() {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/common/dict/typeList',
    method: 'get',
  });
}
//  根据字典key获取字典数据
export function newGetDictByKey(dictCode) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/common/dict/data?dictCode=' + dictCode,
    method: 'get',
  });
}

//字典使用,直接获取数组
export async function DictCodeAPI(params) {
  let [result] = await request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/common/dict/data?dictCode=' + params,
    method: 'get',
  });
  if (result.code === '10000') {
    let newResult = Object.entries(result.data).map(([label, value]) => ({
      label,
      value,
    }));
    return newResult;
  } else {
    console.error('Bad Dict Code');
  }
}
