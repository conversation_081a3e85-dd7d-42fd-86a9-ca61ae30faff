import request from '@/utils/system/request';

// 登录
export function login(username, password, code, requestNo) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/api/authority/admin/login',
    method: 'post',
    data: {
      username,
      password,
      code,
      requestNo,
    },
  });
}

// 登出
export function logout() {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/api/authority/admin/logout',
    method: 'get',
  });
}
// 修改密码
export function changeFirstPassword(data) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/api/authority/admin/user/resetPwdFirst',
    method: 'post',
    data,
  });
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/digital-portal/api/authority/admin/getInfo',
    method: 'get',
  });
}

// 获取动态路由配置
export const getRouters = (params) => {
  const { appId } = params;
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/digital-portal/api/authority/admin/getRouters',
    method: 'get',
    params: appId !== 'undefined' ? params : {},
  });
};
// 新增
export function getList(query) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/api/authority/admin/app/list',
    method: 'GET',
    params: query,
  });
}
// 修改用户
export function updateUser(data) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/api/authority/admin/user/update',
    method: 'post',
    data: data,
  });
}

// 用户密码重置
export function resetUserPwd(data) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/api/authority/admin/user/resetPwd',
    method: 'POST',
    data: data,
  });
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/api/authority/admin/user/' + userId,
    method: 'get',
  });
}
// 前端监听响铃电话事件，更新坐席人
export function getSaveMainUniqueId(id, tel, time, cno) {
  return request(
    {
      url:
        process.env.VUE_APP_SYSTEM_API +
        `/customer-ticket/communicate/save?mainUniqueId=${id}&customerNumber=${tel}&startTime=${time}&cno=${cno}`,
      method: 'get',
    },
    { errorCustom: true }
  );
}
export function getSaveMainUniqueIded(id) {
  return request(
    {
      url:
        process.env.VUE_APP_SYSTEM_API +
        '/customer-ticket/tr/call/outBoard?cdr_enterprise_id=8010450&cdr_main_unique_id=' +
        id,
      method: 'get',
    },
    { errorCustom: true }
  );
}
// 用户当前在线状态
export function userNowStatu() {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/user/extend/queryUserStatus',
    method: 'post',
  });
}
