import request from '@/utils/system/request';

// SLA服务目标详情
export function getSlaDetail() {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sla/detail',
    method: 'post',
  });
}

// SLA服务目标修改
export function updateSla(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sla/add',
    method: 'post',
    data: params,
  });
}

// 获取工单模板列表
export function getOrderTemplate(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/order/template/page',
    method: 'post',
    data: params,
  });
}

// 工单模板新增
export function addOrderTemplate(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/order/template/save',
    method: 'post',
    data: params,
  });
}

// 获取当前业务线下用户
export function getAllUser(nickName) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/user/list?nickName=' + nickName,
    method: 'get',
  });
}

// 获取工单分配配置
export function getOrderAssign() {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/order/assign/query',
    method: 'post',
  });
}

// 更新工单分配配置
export function updateOrderAssign(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/order/assign/save',
    method: 'post',
    data: params,
  });
}

// 接单人员分配列表
export function getOrderTakerList(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/taker/assign/query',
    method: 'post',
    data: params,
  });
}

// 接单人员启用
export function switchTakerEnable(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/taker/assign/changeEnabled',
    method: 'post',
    data: params,
  });
}

// 自动分配添加人员
export function addAssignUser(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/taker/assign/addAssign',
    method: 'post',
    data: params,
  });
}

// 获取全部质检模板
export function getAllInspect(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/check/template/list',
    method: 'post',
    data: params,
  });
}

// 添加质检模板
export function addInspectTemp(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/check/template/add',
    method: 'post',
    data: params,
  });
}

// 预设回复编辑/新增
export function replyAdd(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/default/reply/save',
    method: 'post',
    data: params,
  });
}

// 获取预设回复列表
export function getReplyList(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/default/reply/list',
    method: 'post',
    data: params,
  });
}

// 详情中获取回复列表
export function getReplyAll(categoryId) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/default/reply/listAll?categoryId=' + categoryId,
    method: 'get',
  });
}

// 预设回复启用/删除
export function updateReplyStatus(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/default/reply/editStatus',
    method: 'post',
    data: params,
  });
}

// 保存工单分类
export function saveOrderSort(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/category/save',
    method: 'post',
    data: params,
  });
}
// 删除工单分类
export function deleteOrderSort(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/category/delete',
    method: 'post',
    data: params,
  });
}

// 切换用户状态
export function switchUserStatu(userStatu) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/user/extend/updateUserExtend?userStatus=' + userStatu,
    method: 'get',
  });
}
