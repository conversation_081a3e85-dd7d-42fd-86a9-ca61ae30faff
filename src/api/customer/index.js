import request from '@/utils/system/request';

// 客户列表查询
export function customerListAPI(params) {
  console.log('客户列表接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customerInfo/queryList',
    method: 'post',
    data: params,
  });
}

// 客户新增
export function customerAddAPI(params) {
  console.log('客户新增接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customerInfo/save',
    method: 'post',
    data: params,
  });
}

// 客户详情
export function customerDetailAPI(params) {
  console.log('客户详情接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customerInfo/customerDetailInfo',
    method: 'post',
    data: params,
  });
}

// 客户更新
export function customerUpdateAPI(params) {
  console.log('客户更新接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customerInfo/save',
    method: 'post',
    data: params,
  });
}

// 客户导出
export function customerExportAPI(params) {
  console.log('客户导出接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customerInfo/export',
    method: 'post',
    data: params,
    responseType: 'blob',
  });
}

// 统一的字段选项数据获取接口
export function getFieldOptionsAPI(params) {
  console.log('获取字段选项数据接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customerInfo/queryDistinctValue',
    method: 'post',
    data: params,
  });
}

// 批量导入客户信息
export function customerBatchImportAPI(formData, params) {
  console.log('批量导入客户信息接口');
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customerInfo/importCustomerInfo',
    method: 'post',
    data: formData,
    params: params,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 分配客户跟进人
export function allocateFollowerAPI(params) {
  console.log('分配客户跟进人接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customerInfo/allocateFollower',
    method: 'post',
    data: params,
  });
}

// 获取租户下的用户列表
export function getUserListAPI(params) {
  console.log('获取租户下的用户列表接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/communicate/getUserList',
    method: 'get',
    params: params,
  });
}

// 校验手机号是否已存在（新接口）
export function verifyMobileAPI(params) {
  console.log('校验手机号是否已存在接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customerInfo/verifyMobile',
    method: 'get',
    params: params,
  });
}

// 获取跟进人列表（新接口）
export function getFollowerListAPI(params) {
  console.log('获取跟进人列表接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/communicate/getUserList',
    method: 'get',
    params: params,
  });
}

// 获取省市树列表（新接口）
export function getProvinceTreeAPI(params) {
  console.log('获取省市树列表接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/area/getAllCityTreeList',
    method: 'post',
    data: params,
  });
}
