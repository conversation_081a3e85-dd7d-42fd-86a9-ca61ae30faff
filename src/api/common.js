import request from '@/utils/system/request';

// 文件上传
export function upload(file) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/file/upload',
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 文件获取
export function getFile(fileKey) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/file/getFile?fileKey=' + fileKey,
    method: 'get',
    responseType: 'arraybuffer',
  });
}

// 文件获取url
export function getUrl(fileKey) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/file/getUrl?fileKey=' + fileKey,
    method: 'get',
  });
}
