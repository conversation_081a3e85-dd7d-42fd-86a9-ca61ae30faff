import request from '@/utils/system/request';
//短信模版
export default {
  // 列表查询
  getTableData(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/sendRecord/list',
      method: 'post',
      data: data,
    });
  },
  // 导出
  export(params) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/sendRecord/export',
      method: 'post',
      data: params,
      responseType: 'blob',
    });
  },
  //短信签名
  querySignName(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/sendRecord/smsSignList',
      method: 'get',
      params: data,
    });
  },
  //业务类型
  queryBusinessName(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/sendRecord/businessTypeList',
      method: 'get',
      params: data,
    });
  },
  //失败原因
  queryFailReasonName(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/sendRecord/failReasonList',
      method: 'get',
      params: data,
    });
  },
  //发送批次号
  queryBatchCodeName(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/batch/queryCodeList',
      method: 'post',
      data: data,
    });
  },
};
