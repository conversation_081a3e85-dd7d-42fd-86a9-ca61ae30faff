import request from '@/utils/system/request';
//短信模版
export default {
  // 列表查询
  getTableData(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/template/list',
      method: 'post',
      data: data,
    });
  },
  //新增/编辑
  add(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/template/save',
      method: 'post',
      data: data,
    });
  },
  update(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/template/save',
      method: 'post',
      data: data,
    });
  },
  //删除
  delete(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/template/remove',
      method: 'get',
      params: data,
    });
  },
  // 状态切换
  switchTakerEnable(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/template/changeStatus',
      method: 'post',
      data: data,
    });
  },
  //日志
  getLogList(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/template/log',
      method: 'get',
      params: data,
    });
  },
  //复制
  copy(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/template/copy',
      method: 'get',
      params: data,
    });
  },
  //短信签名
  querySignName(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/template/smsSignList',
      method: 'get',
      params: data,
    });
  },
  //业务类型
  queryBusinessName(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/template/businessTypeList',
      method: 'get',
      params: data,
    });
  },
  //上传表格文件获取返回信息
  excelAdd(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/parseExcel',
      method: 'post',
      data: data,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  //下发
  send(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/red/send',
      method: 'post',
      data: data,
    });
  },
  //相同内容短信失败重新下发
  resendSame(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/red/Resend',
      method: 'post',
      data: data,
    });
  },
  //重发
  resend(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/retry/send',
      method: 'post',
      data: data,
    });
  },
  //提交群发短信
  submitBatch(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/redSendMulti',
      method: 'post',
      data: data,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  //轮询短信发送结果
  querySendResult(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/getCallBackData',
      method: 'post',
      data: data,
    });
  },
};
