import request from '@/utils/system/request';
//短信统计
export default {
  // 数据概括
  queryData(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/statistics/data',
      method: 'post',
      data: data,
    });
  },
  // 发送趋势
  queryTendency(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/statistics/tendency',
      method: 'post',
      data: data,
    });
  },
  // 业务类型
  queryBusiness(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/statistics/businessType',
      method: 'post',
      data: data,
    });
  },
  // 短信签名
  querySign(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/statistics/smsSign',
      method: 'post',
      data: data,
    });
  },
  // 发送账号
  queryAccount(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/statistics/account',
      method: 'post',
      data: data,
    });
  },
};
