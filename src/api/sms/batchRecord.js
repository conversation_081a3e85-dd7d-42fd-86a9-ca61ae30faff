import request from '@/utils/system/request';
//短信模版
export default {
  // 列表查询
  getTableData(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/batch/list',
      method: 'post',
      data: data,
    });
  },
  //日志
  getLogList(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/batch/log',
      method: 'get',
      params: data,
    });
  },
  //备注
  remark(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/batch/remark',
      method: 'post',
      data: data,
    });
  },
  //失败明细列表
  getFailDetail(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/batch/failDetailList',
      method: 'post',
      data: data,
    });
  },
  //业务类型
  queryBusinessName(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/batch/businessTypeList',
      method: 'get',
      params: data,
    });
  },
  // 导出
  export(params) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/batch/export',
      method: 'post',
      data: params,
      responseType: 'blob',
    });
  },
  // 重发
  resend(params) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/retry/send',
      method: 'post',
      data: params,
    });
  },
  //轮询短信发送结果
  querySendResult(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/getCallBackData',
      method: 'post',
      data: data,
    });
  },
};
