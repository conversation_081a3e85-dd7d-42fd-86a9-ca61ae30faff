import request from '@/utils/system/request';
//短信模版
export default {
  // 短信内容校验
  validateContent(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/checkSign',
      method: 'get',
      params: data,
    });
  },
  //上传表格文件获取返回信息
  excelAdd(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/parseExcel',
      method: 'post',
      data: data,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  //下发
  send(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/red/customized/send',
      method: 'post',
      data: data,
    });
  },
  //相同内容短信失败重新下发
  resendSame(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/red/customized/resend',
      method: 'post',
      data: data,
    });
  },
  //重发
  resend(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/retry/send',
      method: 'post',
      data: data,
    });
  },
  //提交群发短信
  submitBatch(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/onlinePersonalMsg',
      method: 'post',
      data: data,
    });
  },
  //轮询短信发送结果
  querySendResult(data) {
    return request({
      url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/sms/getCallBackData',
      method: 'post',
      data: data,
    });
  },
};
