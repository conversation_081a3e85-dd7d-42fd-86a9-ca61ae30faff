import { Modal } from 'ant-design-vue';
import { throttle } from 'lodash';
import store from '@/store';
import router from '@/router';
import { redirectLogin } from '@/router/guards';

// 重新登陆
export const throttledConfirmModal = throttle(
  () => {
    Modal.confirm({
      title: '系统提示',
      content: '登录状态已过期或者不合法，您可以继续留在该页面，或者重新登录',
      okText: '重新登录',
      cancelText: '取消',
      onOk() {
        store.dispatch('base/FedLogOut').finally(() => {
          router.push(redirectLogin());
        });
      },
    });
  },
  3000,
  { trailing: false }
);
