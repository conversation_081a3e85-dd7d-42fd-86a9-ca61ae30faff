import axios from 'axios';

const SYSTEM_ERROR = '系统异常，请重试';

const BASE_URL = process.env.VUE_APP_MOCK_API;
// 创建axios实例
export const myAxios = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: BASE_URL,
  // 超时
  timeout: 30000,
});
myAxios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8';

/**
 * 响应拦截器
 */
myAxios.interceptors.response.use(
  (response) => {
    if (response.status !== 200) {
      return Promise.reject(SYSTEM_ERROR);
    }
    return response;
  },
  () => {
    return Promise.reject(SYSTEM_ERROR);
  }
);

/**
 * @param {*} config 请求参数
 * @param {*} extend 扩展配置
 * @returns
 */
export const requestMock = async (config) => {
  try {
    let res = await myAxios(config);
    return [res.data, undefined];
  } catch (error) {
    return [
      undefined,
      {
        status: 400,
        msg: error.message,
      },
    ];
  }
};
