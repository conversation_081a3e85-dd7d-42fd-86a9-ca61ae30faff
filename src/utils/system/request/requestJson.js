import axios from 'axios';

/**
 * 查询json
 * @param {*} url json路径
 * @returns
 */
export function requestJson(url) {
  return new Promise((resolve) => {
    axios({
      url: url,
    })
      .then((res) => {
        if (res.status === 200 && res.data) {
          resolve([res.data]);
        } else {
          resolve([undefined, true]);
        }
      })
      .catch(() => {
        resolve([undefined, true]);
      });
  });
}
