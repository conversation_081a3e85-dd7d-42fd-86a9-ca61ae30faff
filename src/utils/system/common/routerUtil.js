/** @format */
/**
 * 加载导航守卫
 * @param guards
 * @param options
 */
export function loadGuards(guards, options) {
  const { beforeEach, afterEach } = guards;
  const { router } = options;
  beforeEach.forEach((guard) => {
    if (guard && typeof guard === 'function') {
      router.beforeEach((to, from, next) => guard(to, from, next, options));
    }
  });
  afterEach.forEach((guard) => {
    if (guard && typeof guard === 'function') {
      router.afterEach((to, from) => guard(to, from, options));
    }
  });
}

export function getBaseUrl() {
  const firstName = window.location.pathname.replace(/^\/([^/]*).*$/, '$1');
  const baseURL = `${window.location.origin}${firstName ? '/' + firstName : ''}`;
  return baseURL;
}
// 递归遍历找到component对应的组件
export function filterAsyncRouter(asyncRouterMap) {
  if (asyncRouterMap && Array.isArray(asyncRouterMap)) {
    return asyncRouterMap.map((route) => {
      if (route.component) {
        route.component = lazyLoad(route.component);
      } else {
        // 不传component的情况创建<router-view/>
        route.component = {
          render(c) {
            return c('router-view');
          },
        };
      }
      if (route.children && route.children.length) {
        // 判断是否是按钮菜单
        const find = route.children.find((item) => item.type === 'F');
        // 按钮菜单不添加
        if (find) {
          route.children = undefined;
        } else {
          route.children = filterAsyncRouter(route.children);
        }
      }
      if (route.redirect === 'noRedirect') {
        route.redirect = undefined;
      }
      return route;
    });
  } else {
    return [];
  }
}
// 路由懒加载
function lazyLoad(viewPath) {
  if (viewPath === 'Layout') {
    return () => import(`@/layouts/AdminLayout`);
  } else if (viewPath.indexOf('/layout/') !== -1) {
    // 如果是layout组件 截取掉 /layout/
    const realPath = viewPath.slice(8);
    return () => import(`@/layouts/${realPath}`);
  } else {
    return () => import(`@/views/${viewPath}`);
  }
}
