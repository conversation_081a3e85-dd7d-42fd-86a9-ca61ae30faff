import { newGetDictByKey } from '@/api/system/dict';
import { message } from 'ant-design-vue';
import store from '@/store';

// 数据合并
export function mergeRecursive(source, target) {
  for (var p in target) {
    try {
      if (target[p].constructor == Object) {
        source[p] = mergeRecursive(source[p], target[p]);
      } else {
        source[p] = target[p];
      }
    } catch (e) {
      source[p] = target[p];
    }
  }
  return source;
}
/**
 * @param {Array} arg
 * @returns {Boolean}
 */
export function isArray(arg) {
  if (typeof Array.isArray === 'undefined') {
    return Object.prototype.toString.call(arg) === '[object Array]';
  }
  return Array.isArray(arg);
}

// 用户树形结构处理
export function setCustomTree(list) {
  const data = list.map((item) => ({
    value: item.valueId,
    valueName: item.valueName,
    children: item.userList && item.userList.length ? setCustomTree(item.userList) : null,
    userStatus: item.userStatus ? item.userStatus : null,
    selectable: item.userList && item.userList.length ? false : true,
    scopedSlots: { title: 'myTitle' },
  }));
  return data;
}

// 用户树形结构搜索树处理
export function setCustomSearchTree(list) {
  const data = list.map((item) => ({
    value: item.valueId + '_' + item.valueName,
    valueName: item.valueName,
    children: item.userList && item.userList.length ? setCustomSearchTree(item.userList) : null,
    userStatus: item.userStatus ? item.userStatus : null,
    selectable: item.userList && item.userList.length ? false : true,
    scopedSlots: { title: 'myTitle' },
  }));
  return data;
}

// 获取接口回调通用message
export function defaultMsg(result, msg, err) {
  if (!err) {
    if (result.code === '10000') {
      message.success(`${msg}成功！`);
    } else {
      message.error(`${msg}失败～`);
    }
  }
}

// 二进制流文件下载转换
export function downLoadXlsx(result, name) {
  const blob = new Blob([result]);
  const link = document.createElement('a'); //创建一个a标签
  const url = URL.createObjectURL(blob); //将blob文件对象通过URL.createObjectURL()方法转为为url
  link.href = url; //为a标签设置href属性，并赋值为url
  link.download = name; //定义下载的文件名，文件名要包含后缀哟！如'导出EXCEL.xlsx'
  document.body.appendChild(link); //把a标签放在body上
  link.click(); //出发a标签点击下载
  document.body.removeChild(link); //在body中移除这个a标签
  URL.revokeObjectURL(url); //释放blob对象
}

// 基础获取字典并转换
export async function defaultDict(data) {
  const [result] = await newGetDictByKey(data);
  let res = [];
  for (let item in result.data) {
    res.push({
      label: item,
      value: result.data[item],
    });
  }
  return res;
}

// 工单状态过滤
export function orderStatuFilter(data) {
  return data.filter((item) => {
    return item.value !== '9'; // 不许展示已废弃
  });
}

// 把antd的label，value数据进行处理，第一个是label数组，第二个是value数组
//并且再把数组join成字符串

//label字符串逗号
export function labelFn(data) {
  try {
    let result = data.map((item) => {
      return item ? item.label : null;
    });
    return result.join(',');
  } catch (err) {
    console.log('错误，label或者value格式不对');
  }
}

//value字符串逗号
export function valueFn(data) {
  try {
    let result = data.map((item) => {
      return item ? item.value : null;
    });
    return result.join(',');
  } catch (err) {
    console.log('错误，label或者value格式不对');
  }
}

//根据label找value，第一个参数传完整的label，value对象；第二个以及以后传你逗号隔开的字符串，也就是多个值
export function labelFindFn(data, labelString) {
  if (typeof labelString !== 'string' || labelString === '') {
    return ''; // 返回空字符串或其他默认值，具体根据需求而定
  }

  const labels = labelString.split(',');
  const values = [];

  labels.forEach((label) => {
    const item = data.find((obj) => obj.label === label.trim());
    if (item) {
      values.push(item['value']); // 修改此行
    }
  });

  if (values.length === 1) {
    return values[0];
  } else {
    return values.join(',');
  }
}

//这是根据value或者value逗号隔开找label，输出label用,隔开
export function valueFindFn(data, valueString) {
  if (typeof valueString !== 'string' || valueString === '') {
    return ''; // 返回空字符串或者其他默认值，具体根据需求而定
  }
  const values = valueString.split(',');

  const labels = values.map((value) => {
    const item = data.find((obj) => obj.value === value);
    return item ? item.label : null;
  });

  const validLabels = labels.filter((label) => label !== null);

  if (validLabels.length === 1) {
    return validLabels[0];
  } else {
    return validLabels.join(',');
  }
}

// 根据子节点id获取父节点
export function getFatherBySon(target, list, result = []) {
  for (let i = 0; i < list.length; i++) {
    const item = list[i];
    if (item.value === target) return result;
    if (!item.children || !item.children.length) continue;
    result.push(item);
    if (getFatherBySon(target, item.children, result).length) return result;
    result.pop();
  }
  return [];
}

// 权限判断标识，以防组件中不能使用v-hasPermi
export function isHasPermi(value) {
  const all_permission = '*:*:*';
  const permissions = (store.getters && store.getters['base/permissions']) || [];
  if (value && value instanceof Array && value.length > 0) {
    const permissionFlag = value;
    const hasPermissions = permissions?.some((permission) => {
      return all_permission === permission || permissionFlag.includes(permission);
    });
    if (!hasPermissions) {
      return false;
    }
  } else {
    throw new Error('请设置操作权限标签值');
  }
  return true;
}

// 截取cut之前的部分
export function cutString(value, cut) {
  const final = value.indexOf(cut);
  return value.substring(0, final);
}

// 优先级
export function urgencyColor(value) {
  if (value === '非常紧急') {
    return '#f5222d';
  } else if (value === '紧急') {
    return '#fa8c16';
  } else {
    return '#52c41a';
  }
}
