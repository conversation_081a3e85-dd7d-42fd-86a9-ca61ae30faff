// 重置初始样式

main {
  padding: 0px;
}
.vxe-table {
  .vxe-header--column:first-child {
    .vxe-cell {
      padding-left: 16px;
    }
  }
  .vxe-body--column:first-child {
    .vxe-cell {
      padding-left: 16px;
    }
  }
  .vxe-table--fixed-right-wrapper.scrolling--middle {
    box-shadow: -8px 0px 4px -4px rgba(0, 0, 0, 0.12);
  }
}
.full-width {
  width: 100% !important;
  min-width: 150px;
}
.ant-input-search {
  &.without-search-icon {
    .ant-input-suffix {
      display: none;
    }
  }
}
.info-card {
  margin: 0 0 16px !important;
}
.detail-header {
  background-color: @g-background-white !important;
  padding: 16px !important;
  /deep/ .ant-page-header-heading-title {
    font-size: 18px !important;
  }
}

// antd按钮渐变背景
.ant-btn-primary {
  background: linear-gradient(to right, #1b58f4, #457aff);
  background-color: #1b58f4;
}
.ant-btn-primary:focus,
.ant-btn-primary:hover {
  background-color: #1b58f4;
}
