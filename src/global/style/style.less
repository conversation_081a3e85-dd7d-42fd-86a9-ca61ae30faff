// 自定义全局样式

.beauty-scroll {
  scrollbar-color: @g-primary @g-primary-1;
  scrollbar-width: thin;
  -ms-overflow-style: none;
  position: relative;
  &::-webkit-scrollbar {
    width: 3px;
    height: 1px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: @g-primary;
  }
  &::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 1px rgba(0, 0, 0, 0);
    border-radius: 3px;
    background: @g-primary-2;
  }
  // min-height: 100vh;
}

.beauty-container-scroll {
  scrollbar-color: @g-primary @g-primary-1;
  scrollbar-width: thin;
  -ms-overflow-style: none;
  position: relative;
  &::-webkit-scrollbar {
    width: 3px;
    height: 1px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: @g-primary;
  }
  &::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 1px rgba(0, 0, 0, 0);
    border-radius: 3px;
    background: @g-primary-2;
  }
}

.split-right {
  &:not(:last-child) {
    border-right: 1px solid rgba(98, 98, 98, 0.2);
  }
}

.disabled {
  cursor: not-allowed;
  color: @g-gray-disabled;
  pointer-events: none;
}

.beauty-title {
  color: #333;
  font-weight: bold;
  font-size: 14px;
  line-height: 14px;
  &::before {
    background: #1677ff;
    height: 11px;
    width: 3px;
    content: '';
    display: inline-block;
    margin-right: 4px;
  }
}

// table操作按钮
.operate-button {
  cursor: pointer;
  color: #1677ff;
  padding: 8px 0;
  display: inline-block;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  margin-right: 16px;
}
