// 背景颜色
@g-background-base: #f4f4f4; // 底层背景
@g-background-white: #ffffff; // 白色背景
@g-background-white-light: #fafafa; // 白灰色背景

// 文字颜色
@g-title: rgba(0, 0, 0, 0.85); // 标题
@g-text: rgba(0, 0, 0, 0.65); // 正常文本
@g-desc: rgba(0, 0, 0, 0.45); // 描述

// 按钮颜色
@g-primary: #1677ff; // 主按钮颜色
@g-primary-1: #91caff; // 按钮高亮颜色
@g-primary-2: #bae0ff; // 更亮

// 状态色
@g-success-color: #52c41a; // 成功
@g-warning-color: #faad14; // 警告
@g-error-color: #f5222d; // 错误

// 灰色
@g-gray-disabled: rgba(0, 0, 0, 0.25); // 置灰颜色
@g-gray-light: rgba(0, 0, 0, 0.15); // 浅灰色
@g-gray-bright: #f0f2f5; // 亮灰色
@g-gray-dark: #8c8c8c; // 深灰色
@import '~ant-design-vue/lib/style/themes/default';

// 背景颜色
@g-background-base: #f4f4f4; // 底层背景
@g-background-white: #ffffff; // 白色背景
@g-background-white-light: #fafafa; // 白灰色背景

// 文字颜色
@g-title: rgba(0, 0, 0, 0.85); // 标题
@g-text: rgba(0, 0, 0, 0.65); // 正常文本
@g-desc: rgba(0, 0, 0, 0.45); // 描述

// 按钮颜色
@g-primary: #1677ff; // 主按钮颜色
@g-primary-1: #91caff; // 按钮高亮颜色
@g-primary-2: #bae0ff; // 更亮

// 状态色
@g-success-color: #52c41a; // 成功
@g-warning-color: #faad14; // 警告
@g-error-color: #f5222d; // 错误

// 灰色
@g-gray-disabled: rgba(0, 0, 0, 0.25); // 置灰颜色
@g-gray-light: rgba(0, 0, 0, 0.15); // 浅灰色
@g-gray-bright: #f0f2f5; // 亮灰色
@g-gray-dark: #8c8c8c; // 深灰色

// TODO: 兼容之前代码 冗余部分需要删除
@primary-1: #e6f4ff;
@gray-1: #ffffff;
@gray-2: #fafafa;
@gray-3: #f5f5f5;
@gray-4: #f0f0f0;
@gray-5: #d9d9d9;
@gray-6: #bfbfbf;
@gray-7: #8c8c8c;
@gray-8: #595959;
@gray-9: #434343;
@gray-10: #262626;
@gray-11: #1f1f1f;
@gray-12: #141414;
@gray-13: #000000;
@gray-99: #999999;

@header-text-color: #748194;
@header-background-color: #001529;
@pk-default-text-color: #33ade4;

// @title-color: @heading-color;
// @text-color: @text-color;

// @layout-bg-color: @layout-body-background;
@base-bg-color: #fff;
// @hover-bg-color: rgba(0, 0, 0, 0.025);
// @border-color: @border-color-split;
// @shadow-color: @shadow-color;

// @text-color-inverse: @text-color-inverse;
// @hover-bg-color-light: @hover-bg-color;
// @hover-bg-color-dark: @primary-7;
// @hover-bg-color-night: rgba(255, 255, 255, 0.025);
// @header-bg-color-dark: @layout-header-background;

@shadow-down: @shadow-1-down;
@title-color: rgba(0, 0, 0, 0.85);
@text-color-second: rgba(0, 0, 0, 0.45);

// @shadow-up: @shadow-1-up;
// @shadow-left: @shadow-1-left;
// @shadow-right: @shadow-1-right;

// @theme-list: light, dark, night;
