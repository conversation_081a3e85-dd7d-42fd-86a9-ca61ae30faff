<template>
  <div :data="treeNode.valueName">
    <span>{{ treeNode.valueName }}</span>
    <span v-if="treeNode.userStatus === '1'">
      <a-icon class="userStatu" type="smile" theme="twoTone" twoToneColor="#52c41a" />在线
    </span>
    <span v-else-if="treeNode.userStatus === '2'">
      <a-icon class="userStatu" type="fire" theme="twoTone" twoToneColor="#eb2f96" />忙碌
    </span>
    <span v-else-if="treeNode.userStatus === '3'"><a-icon class="userStatu" type="frown" theme="twoTone" />离线</span>
  </div>
</template>
<script setup>
import { ref, watchEffect } from 'vue';

const props = defineProps({
  newNodes: Object,
});
let treeNode = ref(props.newNodes);
watchEffect(() => {
  treeNode.value = props.newNodes;
});
</script>
<style>
.userStatu {
  margin: 0 8px 0 16px;
}
</style>
