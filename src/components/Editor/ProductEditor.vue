<template>
  <div @click="clickImg" style="margin-bottom: 8px">
    <a-spin :spinning="spin">
      <vue-editor
        id="editor"
        useCustomImageHandler
        @image-added="handleImageAdded"
        v-model="val"
        :editorToolbar="customToolbar"
        :disabled="disabled"
        :customModules="customModulesForEditor"
        :editorOptions="editorSettings"
        @selection-change="selectChange"
        @text-change="textChange"
      ></vue-editor>
      <Upload
        :visible="uploadVisible"
        v-model="url"
        accept=".rar,.zip,.7z,.pdf"
        btnText="点击插入附件"
        :max-num="5"
        :default-list="addFileList"
        @change="handleFileChange"
      ></Upload>
      <!-- 添加图片预览 -->
      <ImgViewer :visible="visible" :imgView="imgView" @cancelModal="cancelModal" />
    </a-spin>
  </div>
</template>
<script>
import ImgViewer from '@/components/ImgModal/ImgViewer';
import { VueEditor } from 'vue2-editor';
import Upload from '../UploadImg/index.vue';
import { getFile, upload } from '@/api/common.js';
import { defaultMsg } from '@/utils/system/common';
import { message } from 'ant-design-vue';
// 导入图片操作相关插件
import { ImageDrop } from 'quill-image-drop-module';
import ImageResize from 'quill-image-resize-module';
const color = [
  'rgb(0,0,0)',
  'rgb(38,38,38)',
  'rgb(89,89,89)',
  'rgb(140,140,140)',
  'rgb(191,191,191)',
  'rgb(217,217,217)',
  'rgb(233,233,233)',
  'rgb(245,245,245)',
  'rgb(250,250,250)',
  'rgb(255,255,255)',
  'rgb(255,60,57)',
  'rgb(231, 95, 51)',
  'rgb(235, 144, 58)',
  'rgb(245, 219, 77)',
  'rgb(114, 192, 64)',
  'rgb(89, 191, 192)',
  'rgb(66, 144, 247)',
  'rgb(54, 88, 226)',
  'rgb(106, 57, 201)',
  'rgb(216, 68, 147)',
  'rgb(251, 233, 230)',
  'rgb(252, 237, 225)',
  'rgb(252, 239, 212)',
  'rgb(252, 251, 207)',
  'rgb(231, 246, 213)',
  'rgb(218, 244, 240)',
  'rgb(217, 237, 250)',
  'rgb(224, 232, 250)',
  'rgb(237, 225, 248)',
  'rgb(246, 226, 234)',
  'rgb(255, 163, 158)',
  'rgb(255, 187, 150)',
  'rgb(255, 213, 145)',
  'rgb(255, 251, 143)',
  'rgb(183, 235, 143)',
  'rgb(135, 232, 222)',
  'rgb(145, 213, 255)',
  'rgb(173, 198, 255)',
  'rgb(211, 173, 247)',
  'rgb(255, 173, 210)',
  'rgb(255, 77, 79)',
  'rgb(255, 122, 69)',
  'rgb(255, 169, 64)',
  'rgb(255, 236, 61)',
  'rgb(115, 209, 61)',
  'rgb(54, 207, 201)',
  'rgb(64, 169, 255)',
  'rgb(89, 126, 247)',
  'rgb(146, 84, 222)',
  'rgb(247, 89, 171)',
  'rgb(207, 19, 34)',
  'rgb(212, 56, 13)',
  'rgb(212, 107, 8)',
  'rgb(212, 177, 6)',
  'rgb(56, 158, 13)',
  'rgb(8, 151, 156)',
  'rgb(9, 109, 217)',
  'rgb(83, 29, 171)',
  'rgb(196, 29, 127)',
  'rgb(130, 0, 20)',
  'rgb(135, 20, 0)',
  'rgb(135, 56, 0)',
  'rgb(97, 71, 0)',
  'rgb(19, 82, 0)',
  'rgb(0, 71, 79)',
  'rgb(0, 58, 140)',
  'rgb(6, 17, 120)',
  'rgb(34, 7, 94)',
  'rgb(120, 6, 80)',
];
export default {
  name: 'ProductEditor',
  components: {
    VueEditor,
    Upload,
    ImgViewer,
  },
  props: {
    value: {
      type: String,
    },
    type: {
      type: String,
      default: 'PJC',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    addFileList: {
      type: Array,
      default: () => [],
    },
    uploadVisible: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      val: '',
      url: '',
      spin: false,
      customToolbar: [
        [{ header: [false, 1, 2, 3] }],
        ['bold', 'italic', 'underline', 'strike'], // toggled buttons
        [{ align: '' }, { align: 'center' }, { align: 'right' }],
        [{ background: color }, { color: color }],
        [{ list: 'ordered' }, { list: 'bullet' }, { list: 'check' }],
        ['image'],
        ['clean'], // remove formatting button
      ],
      // 调整图片大小和位置
      customModulesForEditor: [
        { alias: 'imageDrop', module: ImageDrop },
        { alias: 'imageResize', module: ImageResize },
      ],
      // 设置编辑器图片可拖拽
      editorSettings: {
        modules: { imageDrop: true, imageResize: {} },
      },
      visible: false,
      imgView: '',
    };
  },
  created() {
    this.val = this.value;
    console.log('father', this.addFileList);
  },
  watch: {
    value(v) {
      this.val = v;
    },
  },
  methods: {
    clickImg(e) {
      if (e.target.src) {
        this.imgView = e.target.src;
        this.visible = true;
      }
    },
    cancelModal() {
      this.visible = false;
      this.imgView = '';
    },
    handleFileChange(fileList) {
      this.$emit('addFile', fileList);
    },

    async handleImageAdded(file, Editor, cursorLocation, resetUploader) {
      if (this.disabled) {
        message.warn('只读内容，禁止上传！');
        return;
      }
      this.spin = true;
      const formData = new FormData();
      formData.append('file', file);
      if (file.size > 20000000) {
        message.warn('图片大小不得超过20M！');
        return;
      } else {
        const [res] = await upload(formData);
        defaultMsg(res, '上传');
        const fileKey = res.data.fileKey; // 获取文件key
        const imgType = fileKey.slice(fileKey.lastIndexOf('.') + 1); // 获取文件类型
        const [result] = await getFile(fileKey); // 获取文件
        const src = `data:image/${imgType};base64,` + this.arrayBufferToBase64(result); // 拼接至src
        Editor.insertEmbed(cursorLocation, 'image', src); // 插入至富文本框
        this.spin = false;
        resetUploader();
      }
    },
    arrayBufferToBase64(buffer) {
      let binary = '';
      let bytes = new Uint8Array(buffer);
      let len = bytes.byteLength;
      for (let i = 0; i < len; i++) {
        binary += String.fromCharCode(bytes[i]);
      }
      return window.btoa(binary);
    },
    textChange() {
      this.$emit('input', this.val);
    },
    selectChange() {
      this.$emit('input', this.val);
    },
  },
};
</script>
<style scoped lang="less">
// /deep/ .ql-snow .ql-editor img {
//   max-width: 40%;
// }
/deep/.ql-snow .ql-picker.ql-header .ql-picker-item::before,
/deep/.ql-snow .ql-picker.ql-header .ql-picker-label::before {
  position: relative;
  top: -8px;
}
/deep/.ql-snow .ql-color-picker .ql-picker-label svg,
/deep/.ql-snow .ql-icon-picker .ql-picker-label svg {
  position: relative;
  top: -8px;
}
::v-deep .ql-tooltip {
  z-index: 9;
}
/deep/.ql-snow .ql-tooltip::before {
  content: '访问地址:';
  line-height: 26px;
  margin-right: 8px;
}
/deep/.ql-snow .ql-tooltip a.ql-action::after {
  border-right: 1px solid #ccc;
  content: '编辑';
  margin-left: 16px;
  padding-right: 8px;
}
/deep/.ql-snow .ql-tooltip a.ql-remove::before {
  content: '删除';
  margin-left: 8px;
}
</style>
