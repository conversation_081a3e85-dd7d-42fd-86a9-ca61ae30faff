<template>
  <div class="replyLine">
    预设回复：
    <a-select
      style="width: 240px"
      :placeholder="placeTip"
      show-search
      v-model="replyChoose"
      @change="handleSelect"
      @focus="getReplyList"
      :getPopupContainer="(triggerNode) => triggerNode.parentNode"
    >
      <a-select-option v-for="item in replyList" :key="item.value">
        {{ item.label }}
      </a-select-option>
    </a-select>
  </div>
</template>
<script setup>
import { getReplyAll } from '@/api/system/settings';
import { computed, ref, watchEffect } from 'vue';

const props = defineProps({
  orderSort: String,
});
let replyList = ref();
let replyChoose = ref();
let orderSort = ref(props.orderSort);
const emits = defineEmits(['selectedReply']);
let placeTip = computed(() => {
  return orderSort.value ? '预设回复选择' : '请先选择工单分类';
});
// 选择预设回复模板
function handleSelect(value) {
  const [res] = replyList.value.filter((item) => {
    return item.value === value;
  });
  emits('selectedReply', res);
  // replyChoose.value = undefined; // 允许重复选择
}
// 获取预设回复
async function getReplyList() {
  const [res] = await getReplyAll(orderSort.value);
  replyList.value = res.data.map((item) => {
    return {
      label: item.replyTitle,
      value: item.defaultReplyId,
      content: item.replyContent,
    };
  });
}
watchEffect(() => {
  orderSort.value = props.orderSort;
});
</script>
<style lang="less" scoped>
.replyLine {
  height: 32px;
  line-height: 32px;
}
</style>
