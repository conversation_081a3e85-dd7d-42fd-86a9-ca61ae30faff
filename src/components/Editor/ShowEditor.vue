<template>
  <div @click="clickImg">
    <a-spin :spinning="spin">
      <vue-editor
        id="editor"
        useCustomImageHandler
        v-model="val"
        :editorToolbar="customToolbar"
        :disabled="disabled"
      ></vue-editor>
      <!-- 添加图片预览 -->
      <ImgViewer :visible="visible" :imgView="imgView" @cancelModal="cancelModal" />
    </a-spin>
  </div>
</template>
<script>
// 回显富文本专用组件
import ImgViewer from '@/components/ImgModal/ImgViewer';
import { VueEditor } from 'vue2-editor';
export default {
  name: 'ProductEditor',
  components: {
    VueEditor,
    ImgViewer,
  },
  props: {
    value: {
      type: String,
    },
    type: {
      type: String,
      default: 'PJC',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  // components: { Editor, Toolbar },
  data() {
    return {
      val: '',
      url: '',
      spin: false,
      customToolbar: [[]],
      visible: false,
      imgView: '',
    };
  },
  created() {
    this.val = this.value;
  },
  watch: {
    value(v) {
      this.val = v;
    },
  },
  methods: {
    clickImg(e) {
      if (e.target.src) {
        this.imgView = e.target.src;
        this.visible = true;
      }
    },
    cancelModal() {
      this.visible = false;
      this.imgView = '';
    },
  },
};
</script>
<style scoped lang="less">
/deep/ .ql-toolbar.ql-snow {
  display: none;
}
/deep/ .ql-toolbar.ql-snow + .ql-container.ql-snow {
  border-top: 1px solid #ccc;
}
/deep/ .ql-snow .ql-editor img {
  max-width: 40%;
}
/deep/ .ql-editor {
  min-height: 100px;
  font-size: 12px;
}
/deep/.ql-snow .ql-picker.ql-header .ql-picker-item::before,
/deep/.ql-snow .ql-picker.ql-header .ql-picker-label::before {
  position: relative;
  top: -8px;
}

/deep/.ql-snow .ql-color-picker .ql-picker-label svg,
/deep/.ql-snow .ql-icon-picker .ql-picker-label svg {
  position: relative;
  top: -8px;
}

::v-deep .ql-tooltip {
  z-index: 9;
}

/deep/.ql-snow .ql-tooltip::before {
  content: '访问地址:';
  line-height: 26px;
  margin-right: 8px;
}

/deep/.ql-snow .ql-tooltip a.ql-action::after {
  border-right: 1px solid #ccc;
  content: '编辑';
  margin-left: 16px;
  padding-right: 8px;
}

/deep/.ql-snow .ql-tooltip a.ql-remove::before {
  content: '删除';
  margin-left: 8px;
}
</style>
