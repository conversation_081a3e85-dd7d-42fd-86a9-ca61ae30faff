<template>
  <a-form-model :model="form" ref="baseForm">
    <div class="operation-wrap" v-if="showAddBtn">
      <a-button type="primary" @click="handleAdd"> 添加 </a-button>
      <!-- <a-button type="link" size="small" @click="handleMinus" style="color: #ff4d4f">
        <template #icon><minus-circle-outlined /></template>
        删除
      </a-button> -->
    </div>

    <vxe-table :data="form.tableData" resizable align="center" border ref="xTable" max-height="500px">
      <vxe-column v-bind="item" v-for="(item, index) in columns" :key="index">
        <template #header="{ column }" v-if="!item.type">
          <span class="required-star" v-if="isRequired(item)">*</span>
          {{ column.title }}
        </template>
        <!-- 通用插槽处理 -->
        <template v-for="(slotVal, slotName) in item.slots" #[slotName]="scope">
          <slot
            :name="slotVal"
            v-bind="{
              ...scope,
              column: item,
              $rowIndex: scope?.$rowIndex,
            }"
          ></slot>
        </template>
        <!-- 编辑模式 -->
        <template #default="{ row, $rowIndex }" v-if="item.isEdit">
          <a-form-model-item
            :prop="'tableData[' + $rowIndex + '].' + item.field"
            :rules="handleRules(item.rules, item, row, $rowIndex)"
            v-bind="item.itemProps"
          >
            <component
              :is="item.element || 'a-input'"
              v-model="row[item.field]"
              v-bind="{ ...item.props, ...item.attrs }"
              v-on="getProxyEvents(item, row, $rowIndex)"
            >
              <template v-if="item.element === 'a-select' && item.props?.options">
                <a-select-option
                  v-for="(i, j) in item.props.options"
                  :key="j"
                  :value="i[item.props.optionValue || 'value']"
                >
                  {{ i[item.props.optionLabel || 'label'] }}
                </a-select-option>
              </template>
            </component>
            <span style="color: red; float: right" v-if="row[item.verifyMsgName]">
              {{ row[item.verifyMsgName] }}
            </span>
          </a-form-model-item>
        </template>
      </vxe-column>
    </vxe-table>
  </a-form-model>
</template>

<script>
import { initParams } from '@/utils/buse.js';

export default {
  components: {},
  props: {
    columns: {
      type: Array,
      default: () => [],
    },
    value: {
      type: Array,
      default: () => [],
    },
    showAddBtn: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      form: {
        tableData: this.value,
      },
    };
  },
  watch: {
    value(newValue) {
      this.form.tableData = newValue;
    },
    'form.tableData'(newVal) {
      this.$emit('input', newVal);
    },
  },
  methods: {
    //处理表单校验规则：增加item、row、$rowIndex参数
    handleRules(rules, item, row, $rowIndex) {
      return rules?.map((x) => {
        if (x.validator) {
          return {
            ...x,
            validator: (rule, value, callback) => {
              return x.validator(rule, value, callback, item, row, $rowIndex);
            },
          };
        } else {
          return x;
        }
      });
    },
    handleProxyEvent(handlerName, item, row, rowIndex) {
      return (...args) => {
        if (typeof item.on?.[handlerName] === 'function') {
          item.on[handlerName](...args, item, row, rowIndex);
        }
      };
    },
    getProxyEvents(item, row, rowIndex) {
      return Object.keys(item.on || {}).reduce((res, eventName) => {
        res[eventName] = this.handleProxyEvent(eventName, item, row, rowIndex);
        return res;
      }, {});
    },
    handleMinus() {
      const selected = this.$refs.xTable.getCheckboxRecords(true);
      if (!selected?.length) {
        this.$message.warning('请勾选要删除的行');
        return;
      }
      this.form.tableData = this.form.tableData.filter((x) => !selected.includes(x));
    },
    handleAdd() {
      this.form.tableData.push({ ...initParams(this.columns) });
    },
    isRequired(item) {
      return item.rules?.some((x) => x.required);
    },
    validate(callback) {
      return this.$refs.baseForm.validate(callback);
    },
    validateField(props, callback) {
      return this.$refs.baseForm.validateField(props, callback);
    },
  },
};
</script>

<style lang="less" scoped>
.operation-wrap {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 12px;

  //   button {
  //     padding: 0 7px;
  //   }
}

// 调整表单校验样式
:deep(.ant-form-item) {
  margin-bottom: 0;

  .ant-form-item-explain {
    position: absolute;
    font-size: 12px;
    line-height: 1.2;
    padding-top: 2px;
  }
}

:deep(.vxe-body--row) {
  height: 60px;
}

.required-star {
  color: #ff4d4f;
  margin-right: 3px;
  font-weight: bold;
}
</style>
