<template>
  <div>
    <a-timeline :hide-timestamp="true" v-if="list && list.length > 0">
      <a-timeline-item
        placement="top"
        v-for="(item, index) in list"
        :key="index"
        class="timeline"
        :color="index === 0 ? '#3761ef' : 'gray'"
        size="large"
      >
        <a-card>
          <div style="margin-bottom: 16px; display: flex; justify-content: space-between">
            <div>
              <span class="timeline-title">{{ item[operateTypeTitle] || '--' }}</span>
            </div>
            <div>
              <span v-if="item[operatorNameTitle]">操作人：{{ item[operatorNameTitle] }}</span>
            </div>
            <div>
              <span>{{ item[createTimeTitle] }}</span>
            </div>
          </div>
          <a-row> {{ item[operateDetailTitle] }}</a-row>
        </a-card>
      </a-timeline-item>
    </a-timeline>
    <a-empty v-else></a-empty>
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    //操作类型
    operateTypeTitle: {
      type: String,
      default: 'operatorTypeName',
    },
    //操作人
    operatorNameTitle: {
      type: String,
      default: 'operatorUserName',
    },
    //操作时间
    createTimeTitle: {
      type: String,
      default: 'operatorTime',
    },
    //说明备注
    operateDetailTitle: {
      type: String,
      default: 'remark',
    },
  },
};
</script>

<style scoped lang="less">
.timeline {
  &-title {
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
