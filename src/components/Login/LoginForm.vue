<template>
  <div class="login-box-wrapper">
    <div class="login-box-header">
      <div class="title">
        <!-- {{ systemName }} -->
      </div>
      <!-- <div class="desc">
        {{ systemDesc }}
      </div> -->
    </div>

    <a-form-model class="bd-form" ref="loginForm" :model="form" :rules="rules">
      <a-form-model-item prop="username">
        <a-input v-model="form.username" placeholder="请输入账户" size="large">
          <a-icon slot="prefix" type="user" />
        </a-input>
      </a-form-model-item>
      <a-form-model-item prop="password">
        <a-input-password v-model="form.password" placeholder="请输入密码" type="password" size="large">
          <a-icon slot="prefix" type="lock" />
        </a-input-password>
      </a-form-model-item>
      <a-form-model-item prop="code">
        <div class="form-item-code">
          <a-input
            v-model="form.code"
            placeholder="请输入验证码"
            :maxLength="4"
            size="large"
            @pressEnter="onClickLogin"
          />
          <div class="bd-code-box" @click="getCode">
            <a-spin :spinning="codeLoading">
              <img class="bd-code-img" :src="codeImg" />
            </a-spin>
          </div>
        </div>
      </a-form-model-item>
      <a-button
        :loading="loading"
        class="login-btn"
        size="large"
        type="primary"
        htmlType="submit"
        @click="onClickLogin"
      >
        登录
      </a-button>
    </a-form-model>
  </div>
</template>
<script>
import { rsaCode } from '@/utils/system/common/auth';
import { getBaseUrl } from '@/utils/system/common/routerUtil.js';
import moment from 'moment';
const getDefaultLoginFormData = () => {
  return {
    username: '',
    password: '',
    code: '',
  };
};

export default {
  data() {
    return {
      loading: false,
      requestNo: '',
      codeImg: '',
      codeLoading: false,
      form: getDefaultLoginFormData(),
      rules: {
        username: [
          { required: true, message: '请输入账户名', trigger: 'change' },
          { max: 30, message: '账户名不得超过30个字符' },
        ],
        code: [{ required: true, message: '请输入验证码', trigger: 'change' }],
        password: [
          {
            required: true,
            message: '请输入密码',
            trigger: 'change',
            whitespace: true,
          },
        ],
      },
    };
  },
  computed: {
    systemName() {
      return process.env.VUE_APP_NAME || '登录';
    },
    systemDesc() {
      return process.env.VUE_APP_DESC;
    },
  },
  created() {
    this.getCode();
  },
  methods: {
    // 登录
    async onClickLogin() {
      this.$refs.loginForm.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          const { username, password, code } = this.form;
          const passwordRsa = rsaCode(password); //加密密码
          const [, err] = await this.$store.dispatch('base/Login', {
            username,
            password: passwordRsa,
            code,
            requestNo: this.requestNo,
          });
          this.loading = false;
          if (err) {
            const { subCode } = err;
            if (subCode && subCode === 'password-need-reset') {
              this.goResetPassword();
              return;
              // } else if (subCode && subCode === 'biz-process-failed') {
              //   this.getCode();
            } else {
              this.form.code = '';
              this.getCode();
            }
            return;
          }
          // 登录成功
          this.$emit('success');
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 表单重置
    resetFields() {
      this.form = getDefaultLoginFormData();
      this.$refs.loginForm.resetFields();
    },

    // 获取验证码
    async getCode() {
      if (this.codeLoading) return;
      this.codeLoading = true;
      this.requestNo = moment().valueOf();
      this.codeImg = `${process.env.VUE_APP_USE_BUILD_TYPE ? getBaseUrl() : ''}${
        process.env.VUE_APP_SYSTEM_API
      }/api/authority/admin/captcha?requestNo=${this.requestNo}`;
      // 监听图片加载完成，移除loading
      const img = new Image();
      img.onload = () => {
        this.codeLoading = false;
      };
      img.onerror = () => {
        this.$message.error('获取验证码失败！');
        this.codeLoading = false;
      };
      img.src = this.codeImg;
    },
    // 跳转修改密码页面
    goResetPassword() {
      this.$router.push({ path: '/resetPassword' });
    },
  },
};
</script>
<style lang="less" scoped>
.title {
  margin-left: 52px;
  height: 44px;
  line-height: 44px;
  font-size: 34px;
  color: @g-title;
  font-family: 'Myriad Pro', 'Helvetica Neue', Arial, Helvetica, sans-serif;
  font-weight: 600;
  position: relative;
  margin-bottom: 10px;

  &::before {
    position: absolute;
    content: '';
    background: url('../../assets/images/login-t.png') no-repeat;
    // background-size: 44px 44px;
    // width: 44px;
    // height: 44px;
    background-size: 400px 94px;
    width: 400px;
    height: 94px;

    left: -220px;
    top: -40px;
  }
}

.login-box-wrapper {
  // width: 378px;
  // height: 346px;
  margin: 0 auto;
  padding: 32px 30px;
  .login-box-header {
    margin-bottom: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .platform-logo {
      margin-bottom: 8px;
      height: 42px;
      width: auto;
    }
    .login-title {
      margin: 0;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.45);
      text-align: center;
      line-height: 22px;
    }
  }
  .bd-form {
    width: 318px;
    /deep/ .ant-form-item {
      margin-bottom: 24px;
    }
    // /deep/ .ant-input-affix-wrapper .ant-input-prefix {
    //   color: rgba(0, 0, 0, 0.25);
    // }
    .form-item-code {
      display: flex;
      .bd-code-box {
        width: 100px;
        height: 40px;
        margin-left: 16px;
        .bd-code-img {
          width: 100px;
          height: 40px;
        }
      }
    }
    .login-btn {
      border-radius: 20px;
      width: 100%;
      height: 40px;
      // background: #84bf41;
      // border-color: #84bf41;
    }
  }
}
</style>
