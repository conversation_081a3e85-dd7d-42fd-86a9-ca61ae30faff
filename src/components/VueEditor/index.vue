<template>
  <div>
    <vue-editor v-model="content" :editor-toolbar="customToolbar" />
    <Upload />
  </div>
</template>

<script>
// Basic Use - Covers most scenarios
import { VueEditor } from 'vue2-editor';

export default {
  components: { VueEditor },
  data() {
    return {
      content: '你好',
      customToolbar: [
        ['bold', 'italic', 'underline'],
        [{ list: 'ordered' }, { list: 'bullet' }],
        ['url', 'image', 'code-block'],
      ],
    };
  },
  methods: {},
};
</script>
