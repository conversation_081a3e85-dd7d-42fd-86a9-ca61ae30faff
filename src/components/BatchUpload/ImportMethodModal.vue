<template>
  <a-modal
    title="数据导入处理方式"
    :visible="visible"
    :closable="true"
    :maskClosable="false"
    width="600px"
    @cancel="handleCancel"
    :footer="null"
  >
    <a-form :model="form" ref="form" :label-col="{ span: 0 }" :wrapper-col="{ span: 24 }">
      <a-form-item name="saveMethod">
        <a-radio-group v-model="form.saveMethod" @change="handleMethodChange">
          <a-radio :value="1" style="display: block; margin-bottom: 16px">
            客户手机号已存在时，对历史客户信息不更新，该手机号将自动过滤不导入
          </a-radio>
          <a-radio :value="2" style="display: block"> 客户手机号已存在时，对历史客户信息进行覆盖处理 </a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>

    <div class="modal-footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleConfirm" :disabled="!form.saveMethod">确定</a-button>
    </div>
  </a-modal>
</template>

<script>
export default {
  name: 'ImportMethodModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      form: {
        saveMethod: null, // 1: 不更新已存在数据, 2: 覆盖已存在数据
      },
    };
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        // 弹窗打开时重置表单
        this.form.saveMethod = 1; // 默认选择第一个选项
      }
    },
  },
  methods: {
    handleMethodChange(e) {
      this.form.saveMethod = e.target.value;
    },

    handleConfirm() {
      if (!this.form.saveMethod) {
        this.$message.warning('请选择数据导入处理方式');
        return;
      }

      this.$emit('confirm', this.form.saveMethod);
    },

    handleCancel() {
      this.$emit('cancel');
    },
  },
};
</script>

<style scoped>
.modal-footer {
  text-align: right;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.modal-footer .ant-btn {
  margin-left: 8px;
}

.ant-radio-wrapper {
  line-height: 1.8;
  padding: 8px 0;
}
</style>
