<template>
  <a-row>
    <a-col style="overflow: hidden">
      <div id="smoothline" ref="main" style="width: 1256px; height: 400px; left: -32px"></div>
    </a-col>
  </a-row>
</template>

<script setup>
import { onMounted, ref, watchEffect } from 'vue';
import * as echarts from 'echarts';
let props = defineProps({
  chartData: {
    type: Array,
    default: () => [],
  },
  weekDate: {
    type: Array,
    default: () => [],
  },
});
let weekDate = ref(props.weekDate);
let chartData = ref(props.chartData);
// 虚拟dom引用
let main = ref();
let chartInstance;
let option;
let legendData = [];
let seriesData = [];
function getInstance() {
  if (!chartInstance) {
    chartInstance = ref(echarts.init(main.value));
  }
  option = {
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: legendData,
      bottom: 10,
      orient: 'horizontal',
    },
    xAxis: {
      type: 'category',
      data: weekDate.value,
    },
    yAxis: {
      type: 'value',
    },
    series: seriesData,
    animationDuration: 200,
  };
}
function getLegend(data) {
  return data.map((item) => {
    return item.name;
  });
}
function getSeries(data) {
  return data.map((item) => ({
    name: item.name,
    data: item.data,
    type: 'line',
    smooth: true,
    color: item.color,
  }));
}
function initChart() {
  legendData = getLegend(chartData.value);
  seriesData = getSeries(chartData.value);
  getInstance();
  chartInstance.value.setOption(option);
}
defineExpose({
  initChart,
});
watchEffect(() => {
  chartData.value = props.chartData;
  weekDate.value = props.weekDate;
});
onMounted(() => {
  getInstance();
  initChart();
  window.onresize = function () {
    chartInstance.value.resize();
  };
});
</script>
