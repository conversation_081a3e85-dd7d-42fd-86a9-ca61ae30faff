<template>
  <div class="fieldItem">
    <span>{{ fieldInfo.fieldName }}：</span>
    <!-- <a-select v-if="selectFlag" :disabled="true" :defaultValue="realValue"></a-select> -->
    <span class="realValue" v-if="!richFlag">{{ realValue }}</span>
    <span v-else><ShowEditor :value="realValue" :disabled="true" /></span>
  </div>
</template>
<script setup>
import { onMounted, ref, watchEffect } from 'vue';
import { defaultDict } from '@/utils/system/common';
import ShowEditor from '../Editor/ShowEditor.vue';
const props = defineProps({
  fieldInfo: Object,
});
let richFlag = ref(false);
let fieldInfo = ref(props.fieldInfo);
let required = ref(false);
let realValue = ref(fieldInfo.value.value);
// let selectFlag = ref(false);
async function upValueByType() {
  switch (fieldInfo.value.fieldType) {
    case '1': {
      required.value = true;
      break;
    }
    // 单选列表
    case '2': {
      defaultDict(fieldInfo.value.fieldDataKey).then((res) => {
        const result = res.filter((item) => {
          return item.value === fieldInfo.value.value;
        });
        if (result && result.length > 0) {
          realValue.value = result[0].label;
          // selectFlag.value = true;
        } else {
          realValue.value = '';
        }
      });
      break;
    }
    case '3': {
      if (fieldInfo.value.value == 'Invalid date') {
        realValue.value = '';
      }
      break;
    }
    case '4': {
      richFlag.value = true;
      break;
    }
    // 多选列表
    case '5': {
      defaultDict(fieldInfo.value.fieldDataKey).then((res) => {
        if (fieldInfo.value.value) {
          const valueList = JSON.parse(fieldInfo.value.value);
          const result = valueList.map((item) => {
            const [nodeList] = res.filter((node) => {
              return node.value === item;
            });
            return nodeList.label;
          });
          if (result && result.length > 0) {
            realValue.value = result.length === 1 ? result[0] : result.join('/');
            // selectFlag.value = true;
          } else {
            realValue.value = '';
          }
        } else {
          realValue.value = '';
        }
      });
      break;
    }
    case '7': {
      if (fieldInfo.value.value) {
        realValue.value = fieldInfo.value.value + '%';
      }
      break;
    }
  }
}
onMounted(() => {
  fieldInfo.value.value = fieldInfo.value.value ? fieldInfo.value.value : '';
  upValueByType();
});
watchEffect(() => {
  fieldInfo.value = props.fieldInfo;
});
</script>
<style scoped lang="less">
.fieldItem {
  display: flex;
  .realValue {
    max-width: 240px;
    max-height: 240px;
    display: inline-flex;
  }
  /deep/ .ql-editor {
    min-width: 180px;
  }
}
</style>
