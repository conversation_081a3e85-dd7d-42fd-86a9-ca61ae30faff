<template>
  <a-upload
    v-if="attachmentList && attachmentList.length !== 0"
    name="file"
    :file-list="fileList"
    @preview="previewFile"
    :remove="() => false"
  ></a-upload>
</template>
<script setup>
import { getFile } from '@/api/common';
import { downLoadXlsx } from '@/utils/system/common';
import { ref, watchEffect } from 'vue';

const prop = defineProps({
  attachmentList: {
    type: Array,
    default: () => [],
  },
});

let fileList = ref(prop.attachmentList);
async function previewFile(file) {
  console.log(file);
  const [result] = await getFile(file.url);
  downLoadXlsx(result, file.name);
}
watchEffect(() => {
  fileList.value = prop.attachmentList;
  console.log('show', fileList.value);
});
</script>
<style>
.ant-upload-list-item-card-actions {
  display: none;
}
</style>
