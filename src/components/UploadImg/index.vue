<template>
  <div>
    <a-upload
      v-show="visible"
      name="file"
      :accept="accept"
      :file-list="fileList"
      :remove="handleRemove"
      :beforeUpload="beforeUpload"
      :customRequest="customRequestFile"
      v-bind="$attrs"
      v-on="events"
      @preview="previewFile"
    >
      <template v-if="$attrs['list-type'] === 'picture-card'">
        <a-spin :spinning="loading" v-if="fileList.length < maxNum || !disabled">
          <div>
            <a-icon type="plus" />
            <div class="ant-upload-text">上传图片</div>
          </div>
        </a-spin>
      </template>
      <template v-else>
        <a-button style="margin-right: 10px" :loading="loading">
          <a-icon type="upload" />
          {{ btnText }}
        </a-button>
      </template>
      <span>{{ disabled ? '' : finalTipText }}</span>
    </a-upload>
  </div>
</template>

<script>
import { getFile, upload } from '@/api/common.js';
import { defaultMsg, downLoadXlsx } from '@/utils/system/common';
export default {
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    /**
     * @description: 双向绑定的值，可以传单个url或者多个url的集合
     * @return {*}
     */
    value: {
      type: [String, Array],
      default: '',
    },
    btnText: {
      type: String,
      default: '选择文件',
    },
    accept: {
      type: String,
      default: '.jpg,.png,.jpeg,.gif',
    },
    tip: {
      type: [String, Boolean],
      default: '',
    },
    /**
     * 文件大小限制，单位M
     */
    maxSize: {
      type: Number,
      default() {
        return 20;
      },
    },
    /**
     * 文件数量限制
     */
    maxNum: {
      type: Number,
      default() {
        return 1;
      },
    },
    visible: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    defaultList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      fileList: [],
      file: null,
      loading: false,
    };
  },
  computed: {
    finalTipText() {
      if (this.tip === false) {
        return '';
      }
      return this.tip || ` 支持扩展名：${this.accept.split(',').join('、')}，最大上传限制：${this.maxSize}M。`;
    },
    events() {
      let { change, ...other } = this.$listeners;
      return other;
    },
  },
  methods: {
    handleRemove(file) {
      if (this.disabled) return false;
      const index = this.fileList.indexOf(file);
      this.fileList.splice(index, 1);
      // this.$emit('change', this.fileList)
      this.emitChange(this.fileList);
    },
    beforeUpload(file, fileList) {
      const fileArr = file.name.split('.');
      const fileAccept = this.accept.split(',');
      const suffix = fileArr[fileArr.length - 1];
      // 获取类型结果
      const result = fileAccept.some(function (item) {
        return item.slice(1) === suffix;
      });
      if (this.maxNum === 1) {
        this.fileList.splice(0, 1);
      }
      if (this.fileList.length >= Number(this.maxNum)) {
        this.$message.warning(`最大上传数量为${this.maxNum}`);
        return false;
      }
      if (!result) {
        this.$message.warning('上传格式不正确');
        return false;
      }

      if (file.size > this.maxSize * 1024 * 1024) {
        // 判断文件大小是否超标
        const errorMsg = `${file.name}超过${this.maxSize}M大小的限制!`;
        this.$message.warning(errorMsg);
        return false;
      }
      return true;
    },
    async customRequestFile(data) {
      console.log('🚩🚩data', data);
      this.loading = true;
      const formData = new FormData();
      formData.append('file', data.file);

      this.file = data.file;
      const [res] = await upload(formData);
      this.loading = false;
      defaultMsg(res, '上传');
      const fileKey = res.data.fileKey; // 获取文件key
      this.file.url = fileKey; // 将key当作url  用于后续下载
      this.fileList.push(this.file);
      this.emitChange(this.fileList);
    },
    // 加密文件无法预览 默认进行下载
    async previewFile(file) {
      const [result] = await getFile(file.fileKey);
      downLoadXlsx(result, file.name);
    },
    emitChange(val) {
      console.log('❤️❤️val', val);
      let nowFileList;
      if (!val.length) {
        nowFileList = undefined;
      } else if (this.maxNum === 1) {
        nowFileList = val[0];
      } else {
        nowFileList = val;
      }
      this.$emit('change', nowFileList);
    },
  },
  watch: {
    visible: {
      handler: function (val) {
        // this.fileList = val
        if (this.value?.length) {
          if (!Array.isArray(this.value)) {
            this.fileList = [
              {
                uid: -1, // 文件唯一标识，建议设置为负数，防止和内部产生的 id 冲突
                status: 'done',
                name: this.value.split('/').at(-1),
                url: this.value,
              },
            ];
            return;
          }
          // 多个
          this.fileList = this.value.map((url, index) => ({
            uid: 0 - (index + 1), // 文件唯一标识，建议设置为负数，防止和内部产生的 id 冲突
            status: 'done',
            name: url.split('/').at(-1),
            url: url,
          }));
        }
      },
      immediate: true,
    },
    // fileList: {
    //   handler: function (val) {
    //     // console.log('❤️❤️fileList', val)
    //     // debugger
    //     let url
    //     if (!val.length) {
    //       url = undefined
    //     } else if (val.length === 1) {
    //       url = val[0].url
    //     } else {
    //       url = val?.map((x) => x?.url)
    //     }
    //     this.$emit('change', url)
    //   },
    //   immediate: true,
    // },
    defaultList: {
      handler: function (val) {
        this.fileList = val === null ? [] : val;
        console.log('son', this.fileList);
      },
      immediate: true,
      deep: true,
    },
  },
};
</script>
