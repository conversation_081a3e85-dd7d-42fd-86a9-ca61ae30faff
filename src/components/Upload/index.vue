<template>
  <a-upload
    :class="['uploader', listType, { 'limit-one': limit === 1 }]"
    :list-type="listType"
    :file-list="uploadFileList"
    :accept="accept"
    :action="actionBaseUrl + action"
    :headers="headers"
    :data="data"
    :before-upload="beforeFileUpload"
    @change="handleFileChange"
  >
    <slot v-if="$scopedSlots.default || $slots.default" />
    <template v-else>
      <div v-if="uploadFileList.length < limit" class="upload-btn">
        <div v-if="fileType === 'IMAGE'">
          <a-icon :type="loading ? 'loading' : 'plus'" />
          <div class="ant-upload-text">上传图片</div>
        </div>
        <a-button v-else>上传文件</a-button>
      </div>
    </template>
  </a-upload>
</template>
<script>
import { getToken } from '@/utils/system/common/auth';
import { isArray } from '@/utils/system/common';
import { getBaseUrl } from '@/utils/system/common/routerUtil';

export default {
  props: {
    // 上传业务接口baseurl
    actionBaseUrl: {
      type: String,
      default: process.env.VUE_APP_USE_BUILD_TYPE ? getBaseUrl() : process.env.VUE_APP_SYSTEM_API,
    },
    // 上传业务接口url
    action: {
      type: String,
      default: `/api/authority/admin/upload/oss`,
    },
    // 上传业务接口所需的额外入参
    data: {
      type: Object,
    },
    actionResponseHandle: {
      type: Function,
      default: (response) => {
        return {
          url: response.data || '',
        };
      },
    },
    fileType: {
      type: String,
      default: 'IMAGE',
    },
    // 上传组件类型 text | picture-card
    listType: {
      type: String,
      default: 'picture-card',
    },
    // 上传文件个数
    limit: {
      type: Number,
      default: 1,
    },
    // 已上传文件列表
    fileList: [String, Array],
    // 页面上传前校验方法，不使用默认校验，可传此方法替换原校验规则
    beforeUpload: {
      type: Function,
    },
    // 上传前默认校验图片格式image/jpeg，png，可设置图片大小拦截开关
    limitSize: {
      type: Number,
      default: 4,
    },
    accept: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      // 上传鉴权字段
      headers: {
        Authorization: getToken(),
      },
      loading: false,
      uploadFileList: [],
    };
  },
  watch: {
    // 监听props入参，整合upload组件认知的uploadFileList
    fileList: {
      handler() {
        this.uploadFileList = this.fileList
          ? isArray(this.fileList)
            ? this.fileList.map((item, index) => {
                return {
                  uid: `-${index}`,
                  name: item,
                  status: 'done',
                  url: item,
                };
              })
            : [
                {
                  uid: this.fileList,
                  name: this.fileList,
                  status: 'done',
                  url: this.fileList,
                },
              ]
          : [];
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    handleFileChange(info) {
      const { file, fileList } = info;
      if (file.status) {
        // 有状态才会把文件赋值展示
        // beforeFileUpload拦截为false也会进入该方法，但是没有status，正好剔除此场景
        this.uploadFileList = fileList;
      }
      if (file.status === 'uploading') {
        // 上传中
        this.loading = true;
        return;
      }
      if (file.status === 'done') {
        // 上传完成，解析接口返回数据，更新fileList值
        this.loading = false;
        if (file.response) {
          const { url } = this.actionResponseHandle(file.response);
          if (url) {
            // 将接口返回url塞进列表
            file.url = url;
            if (this.limit === 1) {
              this.uploadFileList = [file];
            } else {
              this.uploadFileList.splice(this.uploadFileList.length - 1, 1, file);
            }
            // 更新外部fileList值
            this.emitUpdateFileList();
          } else {
            this.$message.error('上传失败！请重新上传');
            setTimeout(() => {
              this.uploadFileList.splice(this.uploadFileList.length - 1, 1);
            }, 400);
          }
        }
      } else if (file.status === 'error') {
        this.loading = false;
        this.$message.error('上传失败！请重新上传');
        setTimeout(() => {
          this.uploadFileList.splice(this.uploadFileList.length - 1, 1);
        }, 400);
      } else if (file.status === 'removed') {
        this.emitUpdateFileList();
      }
    },
    // 更新外部fileList值
    emitUpdateFileList() {
      let fileUrlList = '';
      if (this.limit === 1) {
        let len = this.uploadFileList.length;
        const lastItem = this.uploadFileList[len - 1] || {};
        fileUrlList = lastItem.url || '';
      } else {
        fileUrlList = this.uploadFileList.filter((item) => item.status === 'done').map((item) => item.url);
      }
      this.$emit('update:fileList', fileUrlList);
    },
    beforeFileUpload(file) {
      return new Promise((resolve, reject) => {
        const fileZiped = file;
        // 图片压缩
        // zipImg(file, zipOption)
        //   .then((fileZiped) => {
        let flag = false; // 是否可以上传标识
        if (this.beforeUpload && typeof this.beforeUpload === 'function') {
          // 自定义文件上传前的校验
          flag = this.beforeUpload(fileZiped);
        } else {
          const isJPG =
            fileZiped.type === 'image/jpeg' ||
            fileZiped.type === 'image/jpg' ||
            fileZiped.type === 'image/png' ||
            fileZiped.type === 'image/gif';

          if (!isJPG && this.fileType === 'IMAGE') {
            this.$message.error(`只能提交png.jpg.gif.jpeg格式图片!`);
          }
          // 限制上传文件大小
          const isLt2M = fileZiped.size / 1024 / 1024 < this.limitSize;
          if (!isLt2M) {
            this.$message.error(`上传资源大小不能超过 ${this.limitSize}MB!`);
          }
          flag = isLt2M && (this.fileType === 'IMAGE' ? isJPG : true);
        }
        if (flag) {
          // 校验成功，上传文件
          resolve(fileZiped);
        } else {
          // 校验失败，取消上传
          reject();
        }
        // })
        // .catch((e) => {
        //   // 图片压缩失败，取消上传
        //   this.$message.error(`图片压缩失败，上传失败`);
        //   console.error('图片压缩失败，取消上传' + e.message);
        //   reject();
        // });
      });
    },
    handleProgress() {
      this.$emit('progressing');
    },
    handleError() {
      this.$emit('error');
    },
  },
};
</script>

<style lang="less" scoped>
.uploader {
  overflow: hidden;
  width: 100%;
  &.picture-card {
    text-align: center;
  }
  &.limit-one {
    /deep/ .ant-upload-list-picture-card .ant-upload-list-item,
    /deep/ .ant-upload-list-picture-card-container {
      margin: 0;
    }
  }
  .upload-btn {
    cursor: pointer;
  }
  .uploader-icon {
    font-size: 28px;
    color: #8c939d;
  }
  .image {
    width: 100%;
    height: 148px;
    display: block;
  }
}
</style>
