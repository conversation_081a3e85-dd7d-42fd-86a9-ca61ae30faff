<!-- 饼图 -->
<template>
  <div class="pile-emissions">
    <div ref="initChart" class="carbon-echart"></div>
  </div>
</template>
<script>
import * as echarts from 'echarts';
export default {
  components: {},
  name: 'pile-emissions',
  props: {
    //饼图数据
    list: {
      type: Array,
      required: true,
    },
    //左上角单位
    unit: {
      type: String,
      default: '',
    },
    pieRadius: {
      type: Array,
      default: () => ['0%', '70%'],
    },
  },
  data() {
    return {
      textColor: 'rgba(0,0,0,0.45)',
      color: ['#5d86e5', '#7cb9c2', '#6ba364', '#ebbf5a', '#e07c41', '#3BA272', '#FC8452', '#9A60B4', '#EA7CCC'],
    };
  },
  watch: {
    list: {
      handler(val) {
        // if (val && val.length) {
        this.$nextTick(() => {
          this.setOption();
        });
        // }
      },
      immediate: true, // 立即执行
    },
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.initObj.resize);
  },
  methods: {
    initChart() {
      this.initObj = echarts.init(this.$refs.initChart);
      window.addEventListener('resize', this.initObj.resize);
    },
    showLoading() {
      this.initObj.showLoading({
        text: '加载中...',
        color: '#00B578',
        textColor: '#00B578',
        maskColor: 'rgba(255, 255, 255, 0.2)',
        zlevel: 0,
      });
    },
    hideLoading() {
      this.initObj.hideLoading();
    },
    setOption() {
      let that = this;
      that.initObj.setOption({
        // width: "80%",
        // height: "80%",
        color: this.color,
        tooltip: {
          show: true,
          // 触发类型：坐标轴触发
          trigger: 'item',
          //   borderWidth: "0",
          formatter: function (params) {
            return params.marker + params.data['name'] + '   ' + params.value;
          },
        },
        title: {
          text: `{unit|${that.unit}}  `, //主标题文本
          // subtext: this.total, //副标题文本
          left: '5%',
          top: '0%',
          textStyle: {
            rich: {
              val: {
                fontSize: 20,
                color: '#1D2129',
                align: 'center',
                lineHeight: 23,
              },
              text: {
                fontSize: 12,
                color: '#4E5969',
                fontWeight: 400,
                align: 'center',
                lineHeight: 20,
                fontFamily: 'PingFangSC-Regular',
              },
              unit: {
                fontSize: 14,
                color: 'rgba(0,0,0,0.45)',
                fontWeight: 400,
                align: 'center',
                lineHeight: 20,
                fontFamily: 'PingFangSC-Regular',
              },
            },
          },
          // subtextStyle: {
          //   fontFamily: '微软雅黑',
          //   fontSize: 16,
          //   color: '#6c7a89',
          // },
        },

        legend: {
          show: true,
          type: 'scroll',
          orient: 'horizontal', //horizintal  -   vertical
          // width: 300,
          // height: 1000,
          icon: 'roundRect',
          align: 'left',
          // top: '5',
          // itemWidth: 8,
          // itemHeight: 8,
          // y: "20",
          x: 'center',
          itemGap: 20,

          textStyle: {
            color: '#4E5969',
            width: '10%',
            rich: {
              b: {
                // color: "rgba(255,255,255,1)",
                padding: 6,
                fontSize: 12,
              },
              c: {
                color: '#1D2129',
                padding: 6,

                fontSize: 12,
                // fontWeight: 600,
              },
              d: {
                color: '#1D2129',
                padding: 6,

                fontSize: 12,
                // fontWeight: 600,
              },
            },
          },
          // formatter: function (name) {
          //   const item = that.list.filter((ele) => {
          //     if (ele.name == name) {
          //       return true;
          //     }
          //     return false;
          //   });
          //   let total = 0;
          //   for (let i = 0; i < that.list.length; i++) {
          //     total += that.list[i].value;
          //   }
          //   let per = ((item[0].value / total) * 100).toFixed(2);
          //   if (isNaN(per)) {
          //     per = '0';
          //   }
          //   const arr = ['{b|' + item[0].name + '}', '{d|' + per + '%}', '{c|' + item[0].value + '}'];
          //   // return "  " + item[0].name + item[0].value + "个";
          //   return arr.join(' ');
          // },
          data: this.list.map((ele) => ele.name),
        },
        series: [
          {
            type: 'pie',
            // startAngle: 270, //起始角度
            center: ['50%', '50%'],
            radius: that.pieRadius,
            avoidLabelOverlap: true,
            label: {
              normal: {
                // formatter: `<div>123</div>`,
                formatter: '{b|{b}}\n{c|{c}}',
                // position: 'inner',
                rich: {
                  c: {
                    color: '#4E5969',
                    lineHeight: 12,
                    fontSize: 12,
                  },
                  b: {
                    color: '#4E5969',
                    lineHeight: 12,
                    fontSize: 12,
                  },
                },
              },
            },
            labelLine: {
              show: true,
              emphasis: {
                show: true,
              },
              tooltip: {
                show: true,
              },
            },
            data: this.list,
          },
        ],
      });
    },
  },
};
</script>

<style lang="less" scoped>
.pile-emissions {
  // margin-top: 50px;
  .carbon-echart {
    width: 100%;
    height: 350px;
    display: flex;
    text-align: center;
  }
}
</style>
