<template>
  <div class="clink-toobar" :key="key">
    <div id="root" class="root-class" ref="box">
      <div class="name-q" v-show="qname">队列名称：{{ qname }}</div>
      <div class="name-q" v-show="hotLine">热线：{{ hotLine }}</div>
      <div class="name-q" v-show="qname">号码归属地：{{ customerProvince }}{{ customerCity }}</div>
    </div>
    <a-modal v-model="showModal" title="来电提示" @ok="okModal" @cancel="showModal = false">
      系统不会保存你所做的更改，是否跳转到呼叫工作台
    </a-modal>

    <!-- <div @click="toB">1111</div> -->
  </div>
</template>

<script>
import { getSaveMainUniqueId, getSaveMainUniqueIded } from '@/api/system/base.js';
import { defaultDict } from '@/utils/system/common';
import { mapState } from 'vuex';
import { merchantAuthChange } from '@/api/system/merchant';
import { setToken } from '@/utils/system/common/auth';
import { initRoute } from '@/router/guards';

export default {
  name: 'tianrun',
  data() {
    return {
      id: '',
      qname: '',
      customerProvince: '',
      customerCity: '',
      showModal: false,
      key: 0,
      hotLine: '',
    };
  },
  components: {},
  computed: {
    ...mapState({
      merchant: (state) => state.base.merchant || {},
      addRoutes: (state) => state.base.addRoutes || [],
    }),
  },
  async mounted() {
    await this.getSign();
  },
  methods: {
    getSign() {
      const options = {
        isAutoLogin: false,
        toolbarMsgShowConfiguration: {
          showEnterprise: true, // 隐藏企业编码、座席工号、密码
          showTel: true, // 展示电话类型和电话号码
          showAgentInitStatus: true, // 展示座席初始状态
          showLogout: true, // 隐藏签出按钮
          // showTelInput: true, // 登录状态下是否显示号码输入框、默认为true
          // showCallingBth: true, // 登录状态下是否显示呼叫按钮、默认为true
          // showChangeBindTel: false, // 登录状态下是否显示换绑定电话配置、默认为false
          showHold: true, // 支持保持
          showConsult: true, //支持咨询,
          showTransfer: true, //支持转移
          showSatisfaction: true, //支持满意度
          showMute: true, //支持静音
          debug: true, // 是否开启debug 默认开启
        },
      };

      const dom = this.$refs.box;
      console.log('dom', dom);

      const clinkToolbar = new ClinkToolbar(options, dom, () => {
        console.log('电话条初始化完成');
      });

      const params = {
        identifier: 'bdcs',
        cno: '',
        password: '',
        bindType: '3',
      };
      clinkToolbar.setLoginNecessaryParams(params);

      clinkToolbar.userCustomEvent.on(ClinkAgent.EventType.RINGING, (e) => {
        console.log('响铃事件回调:', e);
        if (e.mainUniqueId == this.id) return;
        this.qname = e.qname || '';
        this.customerCity = e.customerCity || '';
        this.customerProvince = e.customerProvince || '';
        this.hotLine = e?.hotline ?? '';
        // 调用同步接口，同步无论成功与否路由跳转到客服工作台页面
        if (e.qno) this.setQno(e.mainUniqueId, e.customerNumber, e.startTime, e.cno, e.qno);
        // if (e.mainUniqueId) this.saveMainUniqueId(e.mainUniqueId, e.customerNumber, e.startTime, e.cno, e.qno);
      });
      clinkToolbar.userCustomEvent.on(ClinkAgent.EventType.STATUS, (e) => {
        console.log('座席状态事件回调:', e);
        if (e.code == 'IDLE') {
          this.qname = '';
          this.customerProvince = '';
          this.customerCity = '';
          this.id = '';
          this.hotLine = '';
        }
        //外呼座席响铃
        if (e.code == 'RINGING' && e.action == 'ringingAgentOb') {
          this.qname = e.qname || '';
          this.customerCity = e.customerCity || '';
          this.customerProvince = e.customerProvince || '';
          this.hotLine = e?.hotline ?? '';
          //设置外呼电话
          this.$store.dispatch('base/callNumberFn', e.customerNumber);
          this.saveMainUniqueId(e.mainUniqueId, e.customerNumber, e.startTime, e.cno);
        }
      });
      // clinkToolbar.userCustomEvent.on(ClinkAgent.EventType.PREVIEW_OUTCALL_RINGING, (e) => {
      //   console.log('预览外呼客户响铃事件回调:', e);
      // });
      // clinkToolbar.userCustomEvent.on(ClinkAgent.EventType.PREVIEW_OUTCALL_BRIDGE, (e) => {
      //   console.log('预览外呼客户接听事件回调:', e);
      // });
    },
    async setQno(id, tel, time, cno, qno) {
      defaultDict('qno_service').then((res) => {
        console.log('res', res);
        const [nodeList] = res.filter((node) => {
          return node.value === qno;
        });
        console.log('nodeList', nodeList);
        // 当前租户无需切换
        if (this.merchant.merchantId === nodeList.label) return this.saveMainUniqueId(id, tel, time, cno, qno);
        // 调用切换租户接口
        this.onChangeMerchant(nodeList.label, id, tel, time, cno, qno);
      });
    },
    // 切换租户
    async onChangeMerchant(id, mid, tel, time, cno, qno) {
      const [res, err] = await merchantAuthChange({
        targetMerchantId: id,
      });
      if (err) return;

      // 替换全局token、重新获取用户路由信息、判断页面是否为呼叫工作台。弹窗提示并切换页面
      setToken(res.data?.token);
      this.$store.commit('base/RESET_BASE_INFO');
      await initRoute(this.$store); // 刷新左侧菜单栏
      this.$emit('onChangeMerchant'); // 刷新导航栏
      this.saveMainUniqueId(mid, tel, time, cno, qno);
      // if (window.location.hash !== '#/callbench') {
      //   this.showModal = true;
      // } else {
      //   this.$store.dispatch('base/isCallFn', true);
      // }
    },

    async saveMainUniqueId(id, tel, time, cno, qno) {
      // if (qno) await this.setQno(qno)
      const [result, error] = await getSaveMainUniqueId(id, tel, time, cno);
      this.id = id;
      console.log(result, error);
      // if (qno) return this.setQno(qno)
      if (window.location.hash !== '#/callbench') {
        this.showModal = true;
      } else {
        this.$store.dispatch('base/isCallFn', true);
      }
    },
    okModal() {
      this.$router.push(`/callbench`);
      this.showModal = false;
    },
    async saveMainUniqueIded(id) {
      const [result, error] = await getSaveMainUniqueIded(id);
    },
  },
};
</script>

<style lang="less">
.root-class {
  // height: 45px;
  border: 1px solid #ccc;
  margin: unset;
  border-radius: 0 0 5px 5px;
  // position: absolute;
  background: white;
  // display: flex;
  // align-items: center;
  // #tinet-toolbar .tinet-header .tinet-tel-input-container {
  //   height: auto;
  // }
  #tinet-toolbar .tinet-header .tinet-tel-input-container {
    border: 0;
    height: initial;
  }
  #tinet-toolbar .tinet-header .tinet-tel-input-container .tinet-phone-num {
    height: 26px;
    border: 1px solid #e1e7ec;
    padding: 3px;
    border-radius: 2px;
  }
  #tinet-toolbar .tinet-loginBar {
    border: 0;
  }
  input,
  select {
    height: 26px;
    line-height: normal;
    background: inherit;
  }
  .name-q {
    height: 30px;
    width: 100%;
    text-align: center;
    margin-top: -10px;
  }
}
</style>
