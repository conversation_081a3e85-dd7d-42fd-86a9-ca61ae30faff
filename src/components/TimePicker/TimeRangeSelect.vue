<template>
  <a-range-picker
    v-model="timeRange"
    :ranges="ranges"
    :disabled-date="disabledDate"
    @change="onChange"
    @calendarChange="panelChange"
  />
</template>
<script>
import moment from 'moment';
// startTime 起始时间
// endTime 结束时间
// allowRangeDay  允许选择的时间范围   15
// {
//   type:'range',//range front behind  范围  前  后
//   value:15//
// }
// ranges  快捷选择按钮
// const ranges = {
//   今天: [moment(), moment()],
//   昨天: [moment().subtract(1, 'days'), moment()],
//   过去3天: [moment().subtract(3, 'days'), moment()],
//   过去5天: [moment().subtract(5, 'days'), moment()],
//   最近7天: [moment().subtract(7, 'days'), moment()],
//   最近15天: [moment().subtract(15, 'days'), moment()],
// };

export default {
  name: 'TimeRangeSelect',
  props: {
    startTime: {
      type: String,
    },
    endTime: {
      type: String,
    },
    // 可选天数范围
    allowRangeDay: {
      type: Number,
      default: 99999,
    },
    ranges: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      showPop: false,
      // ranges: ranges,
      //日期范围选择的第一个点 选择时间时，动态设置可选日期
      firstSelectTime: null,
      timeRange: '',
    };
  },

  watch: {
    startTime: {
      immediate: true,
      handler() {
        this.timeRange = [this.startTime ? moment(this.startTime) : null, this.endTime ? moment(this.endTime) : null];
      },
    },
    endTime: {
      immediate: true,
      handler() {
        this.timeRange = [this.startTime ? moment(this.startTime) : null, this.endTime ? moment(this.endTime) : null];
      },
    },
  },
  methods: {
    // 选择时间时，动态设置可选日期
    panelChange(selectTime) {
      this.firstSelectTime = selectTime[0];
    },
    // 当点击选择时限制，选择好之后放开(onChange)，下次打开面版第一个点能随意选择。
    disabledDate(p) {
      const rule = this.allowRangeDay;
      if (!this.firstSelectTime && !rule) return false;

      // allowRangeDay为数字时，为范围  为obj时，分前后
      if (rule.type) {
        if (rule.type === 'front') {
          return p && p > moment(rule.value).endOf('day');
        } else if (rule.type === 'behind') {
          return p && p < moment(rule.value).endOf('day');
        }
      } else {
        const start = moment(this.firstSelectTime).subtract(this.allowRangeDay - 1, 'days');
        const end = moment(this.firstSelectTime).add(this.allowRangeDay - 1, 'days');
        return p && (p > end || p < start);
      }
    },
    onChange(val) {
      this.firstSelectTime = null;
      this.$emit('change', {
        startTime: val[0] ? val[0].format('YYYY-MM-DD') : null,
        endTime: val[1] ? val[1].format('YYYY-MM-DD') : null,
      });
    },
  },
};
</script>
