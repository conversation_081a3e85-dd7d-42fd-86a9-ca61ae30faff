import Editor from '@/components/Editor/ProductEditor';
import Tags from '@/views/home/<USER>';
import { DictCodeAPI } from '@/api/system/dict';
import { mapState } from 'vuex';
import {
  workTypeAPI,
  listAPI,
  flowTypeAPI,
  userListAPI,
  getAutoAllocationAPI,
  addAPI,
  batchAddAPI,
  stationList,
  maintenanceList,
} from '@/api/system/callBench';
import { customerListAPI } from '@/api/customer';
import moment from 'moment';
import { labelFindFn, setCustomSearchTree, valueFindFn, cutString, getFatherBySon } from '@/utils/system/common/index';
import { defaultDict } from '@/utils/system/common/index';
import AddReply from '@/components/Editor/AddReply';

/**
 * 工单创建混入
 * 提供工单创建的通用逻辑和方法
 */
export default {
  components: {
    Editor,
    Tags,
    AddReply,
  },
  data() {
    return {
      // 工单创建相关数据
      workType: [{}], //工单分类接收
      isWorkTypeFn: false, //工单分类是否选择过，避免重复发请求
      nowCategoryID: '', //当前分类受理id，用于递归判断
      isNullType: false, //判断当前接收是否没有受理人/组
      handleList: [], //没有受理组时候，用到的表
      handleUserList: [], //没有受理人时候，用到的表（先选受理组，才有受理人）
      backupHandle: { userlabel: '', userValue: '', grouplabel: '', groupValue: '' }, //自动分配备份
      flowRadio: '1', //流转方式，默认人工，有设置就改
      enabledFlag: '1', //涉及到flowRadio的显示，一个开关，默认我开了"1"

      templateId: 0, //工单模板id
      exValues: [], //模板内的值
      exList: [
        {
          fields: [],
        },
      ], //模板分类后渲染样式接收（无值）
      modChoice: 0, //选择第几个模板，避免模板v-for嵌套
      choiceValue: undefined, //选择第几个模板,写这个避免报错
      showMod: false, //因为工单模板不是必选，所以需要一开始给一个不传值状态
      contentInput: '', //富文本框内容1
      secContentInput: '', //富文本框内容2
      orderTags: [], //工单标签，来自guestTags组件
      orderName: [], //工单标签名称
      showTags: false, //是否展示Tags的select框;同时控制按钮

      modalSeen: false, //弹窗的显示与否，作用与flowRadio的是否紧急提示
      prevFlow: '3', //保存的紧急
      nowFlow: '3', //现在的紧急

      //字典部分
      selectOne: [], //模板里面的下拉对象数组
      selectCom: [], //模板下拉多选
      rules: {}, //规则，用于prop
      createFlag: '', //当前选择创建工单的状态，用于判断是否草稿
      showFlowType: false,

      autoFlag: false, //禁用自动分配
      tempRules: {},
      autoDisable: false, //受理人禁止选择
      canSub: true,
      stationOptions: [], //站点
      maintenanceOptions: [], //运维受理人
      showFailModal: false, //工单创建失败弹窗
      failOrderId: undefined,
      modalLoading: false,
      isLoading: false,
      currentPage: 1,
      stationValue: '',
      stationRequired: false,
      editStationId: '',
      editStationName: '',
      failMsg: '同步创建工单失败',

      // 销售工单相关字段
      isSaleOrder: false, // 是否为销售工单
      customerOptions: [], // 客户选项列表
      customerLoading: false, // 客户加载状态
      faceInterviewFlag: '0', // 是否需要面访 0-不需要 1-需要
      faceInterviewTime: null, // 面访时间
      outboundCallTime: null, // 预约外呼时间

      // 工单创建弹窗相关
      workOrderModalVisible: false,
      workOrderModalLoading: false,
      workOrderFormData: {
        exValues: [], // 模板字段值
      },

      // 其他必要字段
      customerNumber: '', // 客户手机号

      // 访客相关字段
      showGuest: false,
      visitorName: '',
      visitorPhone: '',
      visitorSex: 0,
      man: false,
      lady: false,
      visitorRules: {},
    };
  },
  computed: {
    ...mapState({
      merchant: (state) => state.base.merchant || {},
    }),
    NumberChoice() {
      //数字转化
      return Number(this.modChoice);
    },
    //由于buse组件不支持show内部写函数，那就用computed实现flowType为3时不显示受理人和受理组
    flowRadioShow() {
      return this.flowRadio !== '3';
    },
    //嵌套太深导致出错，无可奈何
    choiceExList() {
      return this.exList[this.NumberChoice] || [];
    },

    // 工单创建表单配置
    workOrderFormConfig() {
      return [
        {
          field: 'workTypeId',
          title: '工单分类',
          element: 'a-tree-select',
          props: {
            treeData: this.workType,
            showSearch: true,
            replaceFields: {
              title: 'categoryName',
              value: 'categoryId',
            },
            treeNodeFilterProp: 'title',
            getPopupContainer: (triggerNode) => triggerNode.parentNode,
          },
          on: {
            change: async (value) => {
              this.nowCategoryID = value;
              this.checkSaleOrder(value);
              this.flowTypeFn();
              this.showTemp = false;
              await this.getMod();
              if (this.exList && this.exList.length > 0) {
                this.showTemp = true;
                await this.handleChange(0);
              }
            },
            click: () => {
              this.workTypeFn();
              console.log('触发了点击获取工单分类');
            },
          },
          rules: [{ required: this.canSub, message: '请选择工单分类' }],
        },
        // {
        //   field: 'customerId',
        //   title: '选择客户',
        //   element: 'a-select',
        //   props: {
        //     options: this.customerOptions,
        //     showSearch: true,
        //     filterOption: false,
        //     placeholder: '选择客户管理中已维护的客户手机号，仅支持单选',
        //     loading: this.customerLoading,
        //     allowClear: true,
        //     getPopupContainer: (triggerNode) => triggerNode.parentNode,
        //   },
        //   on: {
        //     search: this.handleCustomerSearch,
        //     focus: () => {
        //       if (this.customerOptions.length === 0) {
        //         this.loadCustomerOptions();
        //       }
        //     },
        //     change: (value) => {
        //       if (value) {
        //         const selectedCustomer = this.customerOptions.find((option) => option.value === value);
        //         if (selectedCustomer) {
        //           // 使用 $set 方法更新表单数据
        //           this.$set(this.workOrderFormData, 'customerName', selectedCustomer.customerName);
        //           this.$set(this.workOrderFormData, 'phoneNumber', selectedCustomer.phoneNumber);
        //         }
        //       }
        //     },
        //   },
        //   rules: [{ required: this.isSaleOrder, message: '请选择客户' }],
        //   show: this.isSaleOrder,
        // },
        {
          field: 'faceInterviewFlag',
          title: '是否需要面访',
          element: 'a-radio-group',
          defaultValue: '0',
          props: {
            options: [
              { label: '不需要', value: '0' },
              { label: '需要', value: '1' },
            ],
          },
          on: {
            change: (e) => {
              this.faceInterviewFlag = e;
              if (e === '0') {
                this.faceInterviewTime = null;
                this.$set(this.workOrderFormData, 'faceInterviewTime', null);
              }
            },
          },
          show: this.isSaleOrder,
        },
        {
          field: 'faceInterviewTime',
          title: '面访时间',
          element: 'a-date-picker',
          props: {
            showTime: { format: 'HH:mm' },
            format: 'YYYY-MM-DD HH:mm',
            placeholder: '请选择面访时间',
            getPopupContainer: (triggerNode) => triggerNode.parentNode,
          },
          rules: [{ required: this.isSaleOrder && this.faceInterviewFlag === '1', message: '请选择面访时间' }],
          show: this.isSaleOrder && this.faceInterviewFlag === '1',
        },
        {
          field: 'outboundCallTime',
          title: '预约外呼时间',
          element: 'a-date-picker',
          props: {
            showTime: { format: 'HH:mm' },
            format: 'YYYY-MM-DD HH:mm',
            placeholder: '请选择预约外呼时间',
            getPopupContainer: (triggerNode) => triggerNode.parentNode,
          },
          show: this.isSaleOrder,
        },
        {
          field: 'problemDescription',
          title: '问题描述',
          element: 'slot',
          slotName: 'orContent',
          show: this.isSaleOrder,
        },
        // {
        //   field: 'phoneNumber',
        //   title: '工单手机号',
        //   defaultValue: this.customerNumber,
        //   rules: [
        //     {
        //       required: !this.isSaleOrder && this.merchant.merchantId != '40002',
        //       message: '请输入正确的电话号码',
        //       pattern: /^\d+$/,
        //     },
        //   ],
        //   show: !this.isSaleOrder && this.merchant.merchantId != '40002',
        // },
        {
          field: 'orderContent',
          title: '工单内容',
          element: 'slot',
          slotName: 'orContent',
          rules: [{ required: !this.isSaleOrder && this.canSub, message: '请输入工单内容' }],
          show: !this.isSaleOrder,
        },
        // {
        //   field: 'phoneNumber',
        //   title: '用户手机号',
        //   defaultValue: this.customerNumber,
        //   show: !this.isSaleOrder && this.merchant.merchantId == '40002',
        // },
        {
          field: 'userName',
          title: '用户姓名',
          element: 'a-input',
          rules: [{ max: 100, message: '100字符以内' }],
          show: !this.isSaleOrder && this.merchant.merchantId == '40002',
        },
        {
          field: 'city',
          title: '所在城市',
          element: 'a-input',
          rules: [{ max: 200, message: '200字符以内' }],
          show: !this.isSaleOrder && this.merchant.merchantId == '40002',
        },
        {
          field: 'stationId',
          title: '站点名称',
          element: 'a-select',
          props: {
            options: this.stationOptions,
            showSearch: true,
            filterOption: false,
          },
          on: {
            popupScroll: (event) => {
              const { scrollTop, offsetHeight, scrollHeight } = event.target;
              if (scrollTop + 2 + offsetHeight >= scrollHeight) {
                this.getStationOptions();
              }
            },
            search: async (val) => {
              this.stationValue = val;
              this.stationOptions = [];
              this.currentPage = 1;
              await this.getStationOptions();
            },
          },
          show: !this.isSaleOrder && this.merchant.merchantId == '40002',
          rules: [
            {
              required: !this.isSaleOrder && this.stationRequired && this.merchant.merchantId == '40002',
              message: '请选择站点名称',
            },
          ],
        },
        {
          field: 'vehicle',
          title: '车型品牌',
          element: 'slot',
          slotName: 'vehicle',
          rules: [{ max: 100, message: '100字符以内' }],
          show: !this.isSaleOrder && this.merchant.merchantId == '40002',
        },
        {
          field: 'orderStatus',
          title: '工单状态',
          element: 'a-select',
          props: {
            options: [
              { label: '处理中', value: '1' },
              { label: '处理中（重新处理）', value: '11' },
              { label: '待回访', value: '2' },
              { label: '无需处理', value: '5' },
              { label: '已完结', value: '7' },
            ],
            getPopupContainer: (triggerNode) => triggerNode.parentNode,
          },
          rules: [{ required: this.canSub, message: '请选择工单状态' }],
          on: {
            select: (value) => {
              this.showFlowType = value === '7' ? false : true;
              this.stationRequired = value == '7' || value == '2' ? false : true;
            },
            change: () => {
              this.flowTypeFn();
            },
          },
        },
        {
          field: 'urgency',
          title: '工单优先级',
          element: 'a-radio-group',
          defaultValue: '3',
          props: {
            options: [
              { label: '非常紧急', value: '1' },
              { label: '紧急', value: '2' },
              { label: '一般', value: '3' },
              { label: '低', value: '4' },
            ],
            rules: [{ required: true, message: '请选择优先级' }],
          },
          on: {
            change: (value) => {
              console.log('紧急程度', value);
              if (value == '1' || value == '2') {
                this.nowFlow = value;
                this.modalSeen = true;
              } else {
                this.prevFlow = value;
              }
            },
          },
        },
        {
          field: 'flowType',
          title: '流转方式',
          element: 'slot',
          slotName: 'flowType',
          props: {
            rules: [{ required: true, message: '请选择流转方式' }],
          },
          show: this.showFlowType,
        },
        {
          field: 'maintenanceUser',
          title: '运维受理人',
          element: 'a-select',
          props: {
            options: this.maintenanceOptions,
            showSearch: true,
            filterOption: (input, option) => {
              return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            },
          },
          show: this.merchant.merchantId == '40002' && !this.isSaleOrder,
        },
        {
          field: 'orderHandler',
          title: '工单受理人',
          element: 'slot',
          slotName: 'orderHandler',
          show: this.flowRadioShow && this.showFlowType,
        },
        {
          field: 'orderTemplateId',
          title: '工单模板',
          element: 'slot',
          slotName: 'selectOr',
          show: this.showTemp,
        },
        {
          field: 'not',
          title: '创建新访客',
          element: 'slot',
          slotName: 'newGuest',
          colProps: {
            span: 24,
          },
        },
        {
          field: 'orderTags',
          title: '工单标签',
          element: 'slot',
          slotName: 'guestTag',
        },
      ];
    },
  },
  beforeMount() {
    this.getUserList();
    this.getMaintenanceOptions();
  },
  methods: {
    // 获取用户列表
    getUserList() {
      this.$store.dispatch('base/GetUserList', { nickName: '' }).then((res) => {
        this.handleList = setCustomSearchTree(res);
        console.log('handleList', this.handleList);
      });
    },

    //获取运维人员列表
    async getMaintenanceOptions() {
      const [res] = await maintenanceList({});
      this.maintenanceOptions = res?.data?.map((x) => {
        return { ...x, label: x.userName + '-' + x.nickName, value: x.userId };
      });
    },

    // 获取站点信息列表
    async getStationOptions() {
      if (this.isLoading) {
        return;
      }
      this.isLoading = true;
      const params = { pageNum: this.currentPage, pageSize: 10, stationName: this.stationValue };
      const [res] = await stationList(params);
      const newOptions = res?.data?.map((x) => {
        return { ...x, label: x.stationName, value: x.stationId };
      });
      if (newOptions.length > 0) {
        this.stationOptions = this.stationOptions.concat(newOptions);
        this.currentPage++;
      }
      console.log(this.stationOptions);
      this.isLoading = false;
    },

    //获取工单分类
    async workTypeFn() {
      if (this.isWorkTypeFn == false) {
        let [result] = await workTypeAPI({});
        if (result.code === '10000') {
          this.workType = result.data;
          console.log(result.data);
        }
        this.isWorkTypeFn = true;
      }
    },

    //根据工单分类获取默认流转方式
    async flowTypeFn() {
      if (!this.nowCategoryID) return;
      let [result] = await flowTypeAPI(this.nowCategoryID);
      if (result.code === '10000') {
        console.log(result);
        //是否显示后两个选项
        console.log('显示1，不显示0，你是：' + result.data.enabledFlag);
        this.enabledFlag = result.data.enabledFlag;
        console.log('0就人工流转，1就按fowType，你是：' + result.data.autoFlag);
        console.log('自动流转flowType是多少', result.data.flowType);

        //默认流转方式，并且会在获取到默认流转方式时候发送一个请求用于获取受理组、受理人数据
        let autoFlag = result.data.autoFlag;
        if (autoFlag == '0') {
          this.flowRadio = '1';
          this.autoDisable = false;
          console.log(this.handleList);
        } else if (autoFlag == '1') {
          this.selectFlowType(result.data.flowType);
        }
      }
    },

    async selectFlowType(flowType) {
      this.flowRadio = flowType;
      if (!this.nowCategoryID) return;
      let [result] = await getAutoAllocationAPI(this.nowCategoryID);
      console.log(result);

      if (this.flowRadio == '2') {
        //没有受理的情况下
        if (JSON.stringify(result.data) == '{}') {
          console.log('==没有==');
          this.autoFlag = true;
          this.flowRadio = '1';
          let test = { target: { value: '1' } };
          this.choiceManFlow(test);
        } else {
          this.autoDisable = true;
          this.autoFlag = false;
          const [autoManGroup] = this.handleList.filter((item) => item.value.includes(result.data.handleGroupId));
          const [autoManInline] = autoManGroup.children.filter((item) => item.value.includes(result.data.handleUserId));
          if (autoManInline.userStatus === '3') {
            this.flowRadio = '3';
            this.autoFlag = true;
          }
          //再备份一个，避免后续选择radio获取不到值
          this.backupHandle.userlabel = result.data.handleUserName;
          this.backupHandle.userValue = result.data.handleUserId;
          this.backupHandle.grouplabel = result.data.handleGroupName;
          this.backupHandle.groupValue = result.data.handleGroupId;
        }
      } else if (this.flowRadio == '3') {
        console.log('默认选择了工单池分配！请给我请求');
      } else if (this.flowRadio == '1') {
        this.autoDisable = false;
        console.log(result);
        console.log('这里autoFlag为1之后，flowType为1，自动选择了人工流转');
      }
      console.log('按照传入参数选择flowType，还没写完逻辑，值为：' + result.data.flowType);
      console.log('这个时候是自动分配了一个受理人和受理组');
    },

    //人工流转，自动分配，工单池分配三个选择后触发事件
    async choiceManFlow({ target }) {
      //选择后需要清空受理组和受理人，避免左右横跳出错值
      this.handleUserList = [];
      if (target.value == '1') {
        this.autoDisable = false;
        console.log('人工流转列表是', this.handleList);
      } else if (target.value == '2') {
        this.autoDisable = true;
        console.log(this.backupHandle);
        this.selectFlowType('2');
      } else if (target.value == '3') {
        this.autoDisable = false;
        console.log('藏起来吧！');
      }
    },

    //获取工单模板，触发事件在选择工单分类之后
    async getMod() {
      let [result] = await listAPI(this.nowCategoryID);
      this.exList = result.data; //获取模板
      console.log('看看exList', this.exList);
      let choiceExList = this.exList[this.NumberChoice];
      try {
        //这里为了防止收到空报错
        for (const item of choiceExList.fields || []) {
          //让模版输入框获得初始值
          this.rules[item.templateFieldId] = [{ required: item.emptyFlag === '0' ? true : false, message: '请输入' }];
          this.exValues.push(item.defaultValue || undefined);
        }
        this.templateId = this.exList[0].templateId;
        this.choiceValue = this.exList[0].templateName; //获取分类的时候给予模板一个默认值
      } catch (err) {
        console.log('这个分类下没有模板');
      }
      console.log('看看exList', this.exList);
    },

    //工单模板选择第几个
    async handleChange(value) {
      this.showMod = true;
      this.rules = {};
      console.log('选择下标' + value);
      this.modChoice = value;

      // 重置模板字段值
      this.exValues = [];
      this.workOrderFormData.exValues = [];

      let choiceExList = this.exList[this.NumberChoice];
      try {
        for (const item of choiceExList.fields || []) {
          this.rules[item.templateFieldId] = [{ required: item.emptyFlag === '0' ? true : false, message: '请输入' }];
          //让模版输入框获得初始值
          if (item !== undefined) {
            this.exValues.push(item.defaultValue || null);
            this.workOrderFormData.exValues.push(item.defaultValue || null);
          }
        }
        this.templateId = this.exList[value].templateId;
        console.log('模板ID数组prop', this.rules);
      } catch (err) {
        console.log('这个分类下没有模板');
      }
    },

    // 检查是否为销售工单
    checkSaleOrder(categoryId) {
      const findCategory = (categories, id) => {
        for (const category of categories) {
          if (category.categoryId === id) {
            return category;
          }
          if (category.children && category.children.length > 0) {
            const found = findCategory(category.children, id);
            if (found) return found;
          }
        }
        return null;
      };

      const selectedCategory = findCategory(this.workType, categoryId);
      this.isSaleOrder = selectedCategory && selectedCategory.saleOrderFlag === '1';

      // 如果是销售工单，初始化客户选项
      if (this.isSaleOrder) {
        this.loadCustomerOptions();
      }
    },

    // 加载客户选项
    async loadCustomerOptions(searchKeyword = '') {
      this.customerLoading = true;
      try {
        const [result] = await customerListAPI({
          pageSize: 9999,
          pageNum: 1,
          customerName: searchKeyword,
          phoneNumber: searchKeyword,
        });

        if (result && result.data) {
          this.customerOptions = result.data.map((customer) => ({
            label: `${customer.customerName}—${customer.phoneNumber}`,
            value: customer.customerId,
            customerName: customer.customerName,
            phoneNumber: customer.phoneNumber,
          }));
        }
      } catch (error) {
        console.error('加载客户列表失败:', error);
        this.$message.error('加载客户列表失败');
      } finally {
        this.customerLoading = false;
      }
    },

    // 客户搜索
    handleCustomerSearch(value) {
      if (value) {
        this.loadCustomerOptions(value);
      }
    },

    //tag子组件
    async transTags(value) {
      this.orderName = [];
      this.orderTags = [];
      this.orderTags = value;
      console.log(value);
      let result = await DictCodeAPI('order_tag');
      console.log(result);
      try {
        this.orderName = value.map((value) => {
          const item = result.find((obj) => obj.value == value);
          return item ? item.label : null;
        });
        console.log(this.orderName);
      } catch (err) {
        console.log('看起来你没输入tag');
      }
    },

    changeAddTags(value) {
      this.showTags = !value;
    },

    //选择无模板时候，因为没有内容会报错，但是为了不改变之前的结构，选择捕获错误，顺便改showMod为false
    NoWrongHere() {
      try {
        console.log('你好，我报了个错');
      } catch {
        console.log('然后我不想改了');
      } finally {
        this.showMod = false;
      }
    },

    //针对人工选择工单时，选择工单受理组后，显示工单受理人
    choiceHandleGroup(value) {
      this.handleUserList = value;
      console.log(this.handleUserList);
    },

    //紧急弹窗确认
    modalOK() {
      this.prevFlow = this.nowFlow; //获取备份flowRadio，用于紧急时弹窗提示
      console.log('上一次的已保存', this.prevFlow);
      this.modalSeen = false;
    },

    modalCancel() {
      this.modalSeen = false;
    },

    fieldSelect(value) {
      console.log('模板内多选', value);
    },

    fieldCount(value) {
      console.log('模板内数字');
    },

    selectChange(value) {
      console.log(value);
    },

    async getModSelect(key) {
      let result = await defaultDict(key);
      console.log(result);
      this.selectOne = result;
    },

    async getModSecSelect(key) {
      let result = await defaultDict(key);
      console.log(result);
      this.selectCom = result;
    },

    // 选择预设回复模板
    handleSelect(value) {
      this.contentInput += value.content;
    },

    giveParam(value) {
      this.contentInput = value;
    },

    tryIt(params) {
      console.log('能不能拿到值', params);
    },

    /**
     * 创建单个工单
     * @param {Object} orderData 工单数据
     * @param {Object} customerData 客户数据
     */
    async createSingleOrder(orderData, customerData) {
      try {
        // 构建工单参数
        const params = this.buildOrderParams(orderData, customerData);

        // 调用创建工单API
        const [result, err] = await addAPI(params);

        if (result?.code == '10000') {
          this.$message.success('创建工单成功');
          return { success: true, data: result.data };
        } else if (err?.code == '60000') {
          this.$message.error('创建工单失败: ' + err.message);
          return { success: false, error: err };
        } else {
          this.$message.error('创建工单失败');
          return { success: false, error: result };
        }
      } catch (error) {
        console.error('创建工单异常:', error);
        this.$message.error('创建工单异常');
        return { success: false, error };
      }
    },

    /**
     * 批量创建工单
     * @param {Object} orderTemplate 工单模板数据
     * @param {Array} customerList 客户列表
     */
    async createBatchOrders(orderTemplate, customerList) {
      try {
        // 构建批量工单参数
        const params = {
          workOrderAddRequest: this.buildOrderParams(orderTemplate),
          customerInfos: customerList,
        };

        // 调用批量创建工单API
        const [result, err] = await batchAddAPI(params);

        if (result?.code == '10000') {
          this.$message.success('批量创建工单成功');
          return { success: true, data: result.data };
        } else if (err?.code == '60000') {
          this.$message.error('批量创建工单失败: ' + err.message);
          return { success: false, error: err };
        } else {
          this.$message.error('批量创建工单失败');
          return { success: false, error: result };
        }
      } catch (error) {
        console.error('批量创建工单异常:', error);
        this.$message.error('批量创建工单异常');
        return { success: false, error };
      }
    },

    /**
     * 构建工单参数
     * @param {Object} orderData 工单数据
     * @param {Object} customerData 客户数据（可选）
     */
    buildOrderParams(orderData, customerData = null) {
      let params = { ...orderData };

      // 基础参数设置
      params.orderSource = '2'; // 新电途系统
      params.categoryId = this.nowCategoryID;
      params.flowType = this.showFlowType ? this.flowRadio : '';
      params.saleOrderFlag = this.isSaleOrder ? '1' : '0';
      params.commitFlag = '1'; // 已提交

      // 处理销售工单特殊字段
      if (this.isSaleOrder) {
        if (customerData) {
          params.customerName = customerData.customerName;
          params.phoneNumber = customerData.phoneNumber;
          params.customerId = customerData.customerId;
        }

        // 面访相关字段
        params.faceInterviewFlag = this.faceInterviewFlag;
        if (this.faceInterviewFlag === '1' && params.faceInterviewTime) {
          params.faceInterviewTime = moment(params.faceInterviewTime).format('YYYY-MM-DD HH:mm:ss');
        }

        // 预约外呼时间
        if (params.outboundCallTime) {
          params.outboundCallTime = moment(params.outboundCallTime).format('YYYY-MM-DD HH:mm:ss');
        }

        // 问题描述（复用工单内容）
        if (params.problemDescription) {
          params.problemDescription = this.contentInput;
        }
      }

      // 处理工单标签
      if (this.orderTags) {
        params.orderTags = JSON.parse(JSON.stringify(this.orderTags));
        params.orderTags = this.orderTags.join(',');
      } else {
        params.orderTags = this.orderTags;
      }

      // 处理受理人信息
      if (!this.flowRadioShow) {
        params.handleGroup = '';
        params.handleUser = '';
      } else if (params.orderHandler && this.showFlowType) {
        const handler = params.orderHandler.value;
        params.handleUser = cutString(handler, '_');
        const [handleGroup] = getFatherBySon(handler, this.handleList);
        params.handleGroup = cutString(handleGroup.value, '_');
        delete params.orderHandler;
      }

      // 处理模板字段
      if (this.showMod && this.showTemp) {
        const exFields = this.exList[this.NumberChoice];
        let choiceExList = [];
        if (exFields && exFields.fields && exFields.fields.length !== 0) {
          console.log('上传exList', this.exList);
          params.templateId = this.choiceExList.templateId;
          choiceExList = this.exList[this.NumberChoice].fields;

          // 使用传入的表单数据中的 exValues，如果没有则使用组件内部的 exValues
          const formExValues = orderData?.exValues || this.workOrderFormData?.exValues || this.exValues;
          console.log('formExValues长度:', formExValues.length, 'choiceExList.fields长度:', choiceExList.length);
          console.log('formExValues内容:', formExValues);

          // 确保循环次数不超过字段数量
          const loopLength = Math.min(formExValues.length, choiceExList.length);
          for (let i = 0; i < loopLength; i++) {
            choiceExList[i] = {
              ...choiceExList[i],
              value: formExValues[i] || undefined,
            };
            // console.log('值塞进去了吗', this.exList[this.NumberChoice].fields[i].value);
          }
          console.log(choiceExList);
        }
        // 注释掉减半逻辑，保留所有字段
        // let halfLength = Math.floor(choiceExList.length / 2);
        // choiceExList = choiceExList.splice(0, halfLength);

        params.field = choiceExList || []; //加入模版参数
        console.log('看看参数', params.field);
      } else {
        params.field = null;
      }

      // 处理运维相关字段
      if (this.merchant.merchantId == '40002') {
        params.maintenanceUserName = this.maintenanceOptions?.find((x) => x.value == params.maintenanceUser)?.nickName;

        // 处理站点信息
        if (params.stationId === this.editStationName) {
          params.stationId = this.editStationId;
          params.stationName = this.editStationName;
        } else {
          params.stationName = this.stationOptions?.find((x) => x.value == params.stationId)?.stationName;
        }

        // 处理车辆年份
        if (params.vehicleYear?.startValue) {
          params.vehicleYear = moment(params.vehicleYear.startValue).format('yyyy');
        } else {
          params.vehicleYear = '';
        }
      }

      return params;
    },

    /**
     * 重置工单创建表单
     */
    resetWorkOrderForm() {
      this.showMod = false;
      this.choiceValue = undefined;
      this.orderTags = [];
      this.orderName = [];
      this.exValues = [];
      this.exList = [{ fields: [] }];
      this.rules = {};
      this.contentInput = '';
      this.showFlowType = false;
      this.showTemp = false;
      this.nowCategoryID = '';

      // 重置销售工单相关字段
      this.isSaleOrder = false;
      this.customerOptions = [];
      this.faceInterviewFlag = '0';
      this.faceInterviewTime = null;
      this.outboundCallTime = null;
    },

    // 工单标签处理
    guestTagHandler() {
      console.log('增加客户标签');
    },

    // 访客姓名处理
    nameFn(name) {
      this.visitorName = name;
      if (this.man || this.lady) {
        // 在有性别选择了的时候才会执行
        this.spell(name, 3);
      }
      console.log('访客姓名:', this.visitorName);
    },

    // 性别选择处理
    spell(name, gen) {
      if (gen == 1) {
        //点击先生时候
        if (!this.man) {
          //初次点击，以及切换
          this.visitorName = name + '先生';
          this.lady = false;
          this.man = !this.man;
          this.visitorSex = 1;
        } else if (this.man) {
          //重复点击同一性别，也就是取消
          this.visitorName = name;
          this.man = !this.man;
          this.visitorSex = 0;
        }
      } else if (gen == 2) {
        if (!this.lady) {
          this.visitorName = name + '女士';
          this.man = false;
          this.lady = !this.lady;
          this.visitorSex = 2;
        } else if (this.lady) {
          this.visitorName = name;
          this.lady = !this.lady;
          this.visitorSex = 0;
        }
      } else if (gen == 3) {
        //gen == 3代表不选，我就点着玩
        if (this.lady) {
          this.visitorName = name + '女士';
          this.visitorSex = 2;
        } else if (this.man) {
          this.visitorName = name + '先生';
          this.visitorSex = 1;
        }
      }
      console.log('现在visitorSex' + this.visitorSex);
      console.log(this.visitorName);
    },

    // 显示/隐藏新访客创建
    showGuestfn(params) {
      this.visitorName = params.visitorName;
      this.visitorPhone = params.visitorPhone;
      this.showGuest = !this.showGuest;
      this.visitorRules = {
        visitorName: [{ required: this.showGuest, message: '请填写访客名称', trigger: 'blur' }],
        visitorPhone: [
          {
            required: this.showGuest,
            message: '输入电话号码格式不正确',
            pattern: /^\d+$/,
            trigger: 'blur',
          },
        ],
      };
      if (!this.showGuest) {
        // 清空访客相关字段
        params.visitorName = '';
        params.visitorPhone = '';
        this.visitorName = '';
        this.visitorPhone = '';
        this.man = false;
        this.lady = false;
        this.visitorSex = 0;
      }
    },
  },
};
