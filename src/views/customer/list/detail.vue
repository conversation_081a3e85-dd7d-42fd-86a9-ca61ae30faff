<template>
  <div class="customer-detail">
    <!-- 页面头部 -->
    <div class="detail-header">
      <h2>客户详情</h2>
      <div class="header-actions">
        <a-button type="primary" @click="handleEdit" v-hasPermi="['system:customer:edit']">修改</a-button>
        <a-button type="primary" @click="handleCreateOrder" v-hasPermi="['system:customer:createOrder']">建单</a-button>
        <a-button @click="handleBack">返回</a-button>
      </div>
    </div>

    <!-- 详情内容 -->
    <div class="detail-content">
      <!-- 基本信息 -->
      <a-card style="margin-top: 10px">
        <template slot="title">
          <div class="card-title">基本信息</div>
        </template>
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="客户姓名">
            {{ customerDetail.customerName || '--' }}
          </a-descriptions-item>
          <a-descriptions-item label="客户类型">
            {{ customerDetail.customerType || '--' }}
          </a-descriptions-item>
          <a-descriptions-item label="联系电话">
            {{ customerDetail.phoneNumber || '--' }}
          </a-descriptions-item>
          <a-descriptions-item label="是否KP">
            {{
              customerDetail.kpFlag == '1' ? '是' : customerDetail.kpFlag == '0' ? '否' : customerDetail.kpFlag || '--'
            }}
          </a-descriptions-item>
          <a-descriptions-item label="微信">
            {{ customerDetail.weChat || '--' }}
          </a-descriptions-item>
          <a-descriptions-item label="岗位名称">
            {{ customerDetail.positionName || '--' }}
          </a-descriptions-item>
          <a-descriptions-item label="邮箱">
            {{ customerDetail.email || '--' }}
          </a-descriptions-item>
          <a-descriptions-item label="公司/行业">
            {{ customerDetail.companyIndustry || '--' }}
          </a-descriptions-item>
          <a-descriptions-item label="意向程度">
            {{ customerDetail.intentionLevel || '--' }}
          </a-descriptions-item>
          <a-descriptions-item label="公司名称">
            {{ customerDetail.companyName || '--' }}
          </a-descriptions-item>
          <a-descriptions-item label="客户等级">
            {{ customerDetail.customerLevel || '--' }}
          </a-descriptions-item>
          <a-descriptions-item label="所在省市">
            {{ customerDetail.provinceName + '-' + customerDetail.cityName || '--' }}
          </a-descriptions-item>
          <a-descriptions-item label="客户标签">
            {{ customerDetail.customerTag || '--' }}
          </a-descriptions-item>
          <a-descriptions-item label="跟进人">
            {{ customerDetail.currentFollower || '--' }}
          </a-descriptions-item>
          <a-descriptions-item label="客户备注" :span="2">
            {{ customerDetail.remark || '--' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 销售工单 -->
      <a-card style="margin-top: 10px">
        <template slot="title">
          <div class="card-title">销售工单</div>
        </template>
        <a-table
          :columns="orderColumns"
          :data-source="orderList"
          :pagination="orderPagination"
          :loading="orderLoading"
          @change="handleOrderTableChange"
          row-key="orderId"
        >
          <a slot="orderId" slot-scope="text" @click="viewOrder(text)">{{ text }}</a>
          <span slot="faceInterviewFlag" slot-scope="text">{{
            text == '1' ? '需要' : text == '0' ? '不需要' : text
          }}</span>
          <div slot="orderContent" slot-scope="text" v-html="text"></div>
        </a-table>
      </a-card>

      <!-- 操作日志 -->
      <a-card style="margin-top: 10px">
        <template slot="title">
          <div class="card-title">操作日志</div>
        </template>
        <TimeLine :list="logList" />
      </a-card>
    </div>

    <!-- 编辑弹窗 -->
    <a-modal
      :title="'修改客户信息'"
      :visible="editVisible"
      :width="800"
      :destroyOnClose="true"
      @ok="handleEditSubmit"
      @cancel="handleEditCancel"
      :confirmLoading="editLoading"
    >
      <DynamicForm ref="editForm" :config="editFormConfig" :params="editFormData" :defaultColSpan="12">
        <!-- 客户姓名输入框 -->
        <template #customerName>
          <a-input v-model="editFormData.customerName" placeholder="请输入客户姓名" :maxLength="100"></a-input>
        </template>

        <!-- 联系电话输入框（带唯一性验证） -->
        <template #phoneNumber>
          <a-input
            v-model="editFormData.phoneNumber"
            placeholder="请输入11位手机号"
            :maxLength="11"
            @blur="checkPhoneUnique(editFormData.phoneNumber, editFormData.customerId)"
          ></a-input>
          <div v-if="phoneErrorMsg" class="error-msg">{{ phoneErrorMsg }}</div>
        </template>

        <!-- 所在省市级联选择器 -->
        <template #address>
          <a-cascader
            v-model="editFormData.address"
            :options="provinceList"
            placeholder="请选择所在省市"
            :show-search="true"
            change-on-select
          />
        </template>

        <!-- 是否KP选择器 -->
        <template #kpFlag>
          <a-radio-group v-model="editFormData.kpFlag" @change="handleKpChange(editFormData)">
            <a-radio value="0">否</a-radio>
            <a-radio value="1">是</a-radio>
          </a-radio-group>
          <!-- 岗位名称输入框（当选择是/否时都显示） -->
          <div style="margin-top: 8px">
            <a-auto-complete
              v-model="editFormData.positionName"
              :data-source="positionOptions"
              placeholder="请输入或选择岗位名称"
              @search="searchPositionOptions"
              @select="onPositionSelect"
              style="width: 100%"
            />
          </div>
        </template>

        <!-- 客户类型自动完成下拉选择 -->
        <template #customerType>
          <a-auto-complete
            v-model="editFormData.customerType"
            :data-source="customerTypeOptions"
            placeholder="请输入或选择客户类型"
            @search="searchCustomerTypeOptions"
            @select="onCustomerTypeSelect"
          />
        </template>

        <!-- 公司行业自动完成下拉选择 -->
        <template #companyIndustry>
          <a-auto-complete
            v-model="editFormData.companyIndustry"
            :data-source="companyIndustryOptions"
            placeholder="请输入或选择公司行业"
            @search="searchCompanyIndustryOptions"
            @select="onCompanyIndustrySelect"
          />
        </template>

        <!-- 意向程度自动完成下拉选择 -->
        <template #intentionLevel>
          <a-auto-complete
            v-model="editFormData.intentionLevel"
            :data-source="intentionLevelOptions"
            placeholder="请输入或选择意向程度"
            @search="searchIntentionLevelOptions"
            @select="onIntentionLevelSelect"
          />
        </template>

        <!-- 客户等级自动完成下拉选择 -->
        <template #customerLevel>
          <a-auto-complete
            v-model="editFormData.customerLevel"
            :data-source="customerLevelOptions"
            placeholder="请输入或选择客户等级"
            @search="searchCustomerLevelOptions"
            @select="onCustomerLevelSelect"
          />
        </template>

        <!-- 公司名称自动完成下拉选择 -->
        <template #companyName>
          <a-auto-complete
            v-model="editFormData.companyName"
            :data-source="companyNameOptions"
            placeholder="请输入或选择公司名称"
            @search="searchCompanyNameOptions"
            @select="onCompanyNameSelect"
          />
        </template>

        <!-- 客户标签多选 -->
        <template #customerTags>
          <a-select
            v-model="editFormData.customerTag"
            mode="tags"
            :token-separators="[',']"
            placeholder="请输入或选择客户标签"
            @search="searchCustomerTagOptions"
            style="width: 100%"
            clearable
          >
            <a-select-option v-for="tag in customerTagOptions" :key="tag" :value="tag">
              {{ tag }}
            </a-select-option>
          </a-select>
        </template>
      </DynamicForm>
    </a-modal>

    <!-- 工单创建弹窗 -->
    <a-modal
      title="创建工单"
      :visible="workOrderModalVisible"
      :width="800"
      :destroyOnClose="true"
      @ok="handleWorkOrderSubmit"
      @cancel="handleWorkOrderCancel"
      :confirmLoading="workOrderModalLoading"
    >
      <DynamicForm ref="workOrderForm" :config="workOrderFormConfig" :params="workOrderFormData" :defaultColSpan="24">
        <!-- 工单内容和问题描述富文本编辑器 -->
        <template #orContent>
          <div class="orderContent">
            <AddReply :orderSort="nowCategoryID" @selectedReply="handleSelect" />
            <Editor
              :value="contentInput"
              v-model="workOrderFormData.orderContent"
              @blur="workOrderFormData.orderContent = contentInput"
              @input="giveParam(workOrderFormData.orderContent)"
            />
          </div>
        </template>

        <!-- 工单标签 -->
        <template #guestTag>
          <div class="guest-tag">
            <a-tag class="tags" color="pink" v-for="(item, index) in orderName" :key="index">{{ item }}</a-tag>
            <a-button
              v-show="!showTags"
              type="primary"
              @click="
                guestTagHandler;
                showTags = !showTags;
              "
              ><span><a-icon type="plus-circle" class="icon-plus" /></span>添加工单标签</a-button
            >

            <Tags :showTags="showTags" @custom-event="transTags" @custom-showAdd="changeAddTags" />
          </div>
        </template>

        <!-- 流转方式 -->
        <template #flowType>
          <a-radio-group v-model="flowRadio" @change="choiceManFlow">
            <a-radio value="1">人工流转</a-radio>
            <a-radio value="2" v-if="enabledFlag == '1'" :disabled="autoFlag">自动分配</a-radio>
            <a-radio value="3" v-if="enabledFlag == '1'">工单池流转</a-radio>
          </a-radio-group>
        </template>

        <!-- 工单受理人 -->
        <template #orderHandler>
          <a-tree-select
            placeholder="请选择受理人员"
            :disabled="autoDisable"
            v-model="workOrderFormData.orderHandler"
            :treeData="handleList"
            showSearch
            labelInValue
            :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
            @focus="getUserList()"
          >
            <template slot="myTitle" slot-scope="item">
              <StatuNode :newNodes="item" />
            </template>
          </a-tree-select>
        </template>

        <!-- 工单模板 -->
        <template #selectOr>
          <a-select
            placeholder="请在选择工单分类之后选择工单模板"
            @change="handleChange"
            v-model="choiceValue"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
          >
            <a-select-option v-for="(item, index) in exList || []" :key="index">
              {{ item.templateName }}
            </a-select-option>
          </a-select>

          <div class="templateFields">
            <a-button
              type="primary"
              v-if="showMod"
              @click="
                showMod = false;
                choiceValue = undefined;
              "
              ><a-icon type="minus-circle" /> 取消模板选择</a-button
            >
            <!-- :rules="rules[fielditem.templateFieldId]" :rules="rules" -->
            <a-form-model ref="templateForm" :model="workOrderFormData">
              <template v-for="(fielditem, index) in choiceExList.fields">
                <a-form-model-item
                  v-if="fielditem !== undefined && showMod && fielditem.fieldType == '1'"
                  :label-col="{ span: 10 }"
                  :wrapper-col="{ span: 14 }"
                  :label="fielditem.fieldName"
                  :key="index"
                  :rules="[{ required: fielditem.emptyFlag === '0', message: '请输入', trigger: 'blur' }]"
                  :prop="`exValues.${index}`"
                  ><a-input
                    @focus="tryIt(workOrderFormData)"
                    :defaultValue="fielditem.defaultValue"
                    @hook:mounted="workOrderFormData.exValues[index] = fielditem.defaultValue"
                    v-model="workOrderFormData.exValues[index]"
                  ></a-input>
                </a-form-model-item>

                <a-form-model-item
                  v-if="fielditem !== undefined && showMod && fielditem.fieldType == '2'"
                  :label-col="{ span: 10 }"
                  :wrapper-col="{ span: 14 }"
                  :label="fielditem.fieldName"
                  :key="index"
                  :rules="[{ required: fielditem.emptyFlag === '0', message: '请输入', trigger: 'blur' }]"
                  :prop="`exValues.${index}`"
                >
                  <a-select
                    @focus="getModSelect(fielditem.fieldDataKey)"
                    @hook:mounted="workOrderFormData.exValues[index] = fielditem.defaultValue"
                    v-model="workOrderFormData.exValues[index]"
                  >
                    <a-select-option v-for="selectItem in selectOne" :key="selectItem.value">
                      {{ selectItem.label }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>

                <a-form-model-item
                  v-if="fielditem !== undefined && showMod && fielditem.fieldType == '3'"
                  :label-col="{ span: 10 }"
                  :wrapper-col="{ span: 14 }"
                  :label="fielditem.fieldName"
                  :key="index"
                  :rules="[{ required: fielditem.emptyFlag === '0', message: '请输入', trigger: 'blur' }]"
                  :prop="`exValues.${index}`"
                  ><a-date-picker
                    :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                    style="width: 100%"
                    :defaultValue="fielditem.defaultValue"
                    @hook:mounted="workOrderFormData.exValues[index] = fielditem.defaultValue"
                    v-model="workOrderFormData.exValues[index]"
                    valueFormat="YYYY-MM-DD HH:mm:ss"
                  ></a-date-picker>
                </a-form-model-item>

                <a-form-model-item
                  v-if="fielditem !== undefined && showMod && fielditem.fieldType == '4'"
                  :label-col="{ span: 10 }"
                  :wrapper-col="{ span: 14 }"
                  :label="fielditem.fieldName"
                  :key="index"
                  :rules="[{ required: fielditem.emptyFlag === '0', message: '请输入', trigger: 'blur' }]"
                  :prop="`exValues.${index}`"
                >
                  <Editor
                    :value="secContentInput"
                    :defaultValue="fielditem.defaultValue"
                    @hook:mounted="workOrderFormData.exValues[index] = fielditem.defaultValue"
                    v-model="workOrderFormData.exValues[index]"
                  />
                </a-form-model-item>

                <a-form-model-item
                  v-if="fielditem !== undefined && showMod && fielditem.fieldType == '5'"
                  :label-col="{ span: 10 }"
                  :wrapper-col="{ span: 14 }"
                  :label="fielditem.fieldName"
                  :key="index"
                  :rules="[{ required: fielditem.emptyFlag === '0', message: '请输入', trigger: 'blur' }]"
                  :prop="`exValues.${index}`"
                >
                  <a-select
                    mode="tags"
                    style="width: 100%"
                    placeholder="请输入"
                    @change="fieldSelect"
                    :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                    @focus="getModSecSelect(fielditem.fieldDataKey)"
                    @hook:mounted="workOrderFormData.exValues[index] = fielditem.defaultValue"
                    v-model="workOrderFormData.exValues[index]"
                  >
                    <a-select-option v-for="item in selectCom" :key="item.value">
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>

                <a-form-model-item
                  v-if="fielditem !== undefined && showMod && fielditem.fieldType == '6'"
                  :label-col="{ span: 10 }"
                  :wrapper-col="{ span: 14 }"
                  :label="fielditem.fieldName"
                  :key="index"
                  :rules="[{ required: fielditem.emptyFlag === '0', message: '请输入', trigger: 'blur' }]"
                  :prop="`exValues.${index}`"
                >
                  <a-input-number
                    :defaultValue="fielditem.defaultValue"
                    @hook:mounted="workOrderFormData.exValues[index] = fielditem.defaultValue"
                    v-model="workOrderFormData.exValues[index]"
                    @change="fieldCount"
                  />
                </a-form-model-item>

                <a-form-model-item
                  v-if="fielditem !== undefined && showMod && fielditem.fieldType == '7'"
                  :label-col="{ span: 10 }"
                  :wrapper-col="{ span: 14 }"
                  :label="fielditem.fieldName"
                  :key="index"
                  :rules="[{ required: fielditem.emptyFlag === '0', message: '请输入', trigger: 'blur' }]"
                  :prop="`exValues.${index}`"
                >
                  <a-input-number
                    :defaultValue="fielditem.defaultValue"
                    @hook:mounted="workOrderFormData.exValues[index] = fielditem.defaultValue"
                    v-model="workOrderFormData.exValues[index]"
                    @change="fieldCount"
                    :formatter="(value) => `${value}%`"
                    :parser="(value) => value.replace('%', '')"
                  />
                </a-form-model-item>
              </template>
            </a-form-model>
          </div>
        </template>

        <!-- 车型品牌 -->
        <template #vehicle>
          <a-row>
            <a-col :span="8">
              <a-input v-model="workOrderFormData.vehicleBrand" placeholder="请输入品牌"></a-input>
            </a-col>
            <a-col :span="8">
              <a-input v-model="workOrderFormData.vehicleModel" placeholder="请输入车型"></a-input>
            </a-col>
            <a-col :span="8">
              <BuseRangePicker
                type="year"
                v-model="workOrderFormData.vehicleYear"
                format="YYYY"
                placeholder="请选择年份"
                :needShowSecondPicker="() => false"
              ></BuseRangePicker>
            </a-col>
          </a-row>
        </template>

        <!-- 创建新访客 -->
        <template #newGuest>
          <a-button type="primary" @click="showGuestfn(workOrderFormData)">
            <span>
              <a-icon v-if="showGuest == false" type="plus-circle" class="icon-plus" />
              <a-icon v-if="showGuest == true" type="minus-circle" class="icon-plus" />
            </span>
            <span v-if="showGuest == true">取消</span>创建新访客
          </a-button>
        </template>
      </DynamicForm>
    </a-modal>

    <!-- 紧急程度确认弹窗 -->
    <a-modal title="提示" :visible="modalSeen" @ok="modalOK" @cancel="modalCancel" okText="确定" cancelText="取消">
      <p>选择该优先级会触发SLA目标，请确认</p>
    </a-modal>
  </div>
</template>

<script>
import {
  customerDetailAPI,
  customerUpdateAPI,
  verifyMobileAPI,
  getFieldOptionsAPI,
  getFollowerListAPI,
  getProvinceTreeAPI,
} from '@/api/customer';
import { workOrderListAPI } from '@/api/workOrder';
import { getOperationLogAPI } from '@/api/operationLog';
import TimeLine from '@/components/Timeline/index.vue';
import workOrderCreateMixin from '@/mixins/workOrderCreate';
import StatuNode from '@/components/TreeNode/StatuNode.vue';
import { initParams } from '@/utils/buse';

export default {
  name: 'CustomerDetail',
  mixins: [workOrderCreateMixin],
  components: {
    TimeLine,
    StatuNode,
  },
  data() {
    return {
      customerId: '',
      phoneNumber: '',
      activeTab: 'basic',
      customerDetail: {},

      // 销售工单相关
      orderList: [],
      orderLoading: false,
      orderPagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
      },

      // 操作日志
      logList: [],

      // 编辑相关
      editVisible: false,
      editLoading: false,
      editFormData: {},
      phoneErrorMsg: '',

      // 选项数据
      provinceList: [],
      customerTypeOptions: [],
      companyIndustryOptions: [],
      intentionLevelOptions: [],
      customerLevelOptions: [],
      companyNameOptions: [],
      followerOptionsWithId: [],
      positionOptions: [],
      customerTagOptions: [],

      // 工单创建相关
      showTemp: false, // 是否显示模板字段
    };
  },
  computed: {
    // 销售工单表格列配置
    orderColumns() {
      return [
        {
          title: '工单号',
          dataIndex: 'orderId',
          key: 'orderId',
          scopedSlots: { customRender: 'orderId' },
        },
        {
          title: '客户',
          dataIndex: 'customerName',
          key: 'customerName',
        },
        {
          title: '是否需要面访',
          dataIndex: 'faceInterviewFlag',
          key: 'faceInterviewFlag',
          scopedSlots: { customRender: 'faceInterviewFlag' },
        },
        {
          title: '预约外呼时间',
          dataIndex: 'outboundCallTime',
          key: 'outboundCallTime',
        },
        {
          title: '当前处理人',
          dataIndex: 'handleUserName',
          key: 'handleUserName',
        },
        {
          title: '优先级',
          dataIndex: 'urgency',
          key: 'urgency',
        },
        {
          title: '分类',
          dataIndex: 'categoryName',
          key: 'categoryName',
        },
        {
          title: '工单状态',
          dataIndex: 'orderStatus',
          key: 'orderStatus',
        },
        {
          title: '问题描述',
          dataIndex: 'orderContent',
          key: 'orderContent',
          scopedSlots: { customRender: 'orderContent' },
        },
        {
          title: '手机号',
          dataIndex: 'phoneNumber',
          key: 'phoneNumber',
        },
        {
          title: '提交时间',
          dataIndex: 'createTime',
          key: 'createTime',
        },
        {
          title: '建单人',
          dataIndex: 'createByName',
          key: 'createByName',
        },
      ];
    },

    // 编辑表单配置
    editFormConfig() {
      return [
        {
          field: 'customerName',
          title: '客户姓名',
          element: 'slot',
          slotName: 'customerName',
          rules: [
            { required: true, message: '请输入客户姓名' },
            { max: 100, message: '客户姓名不能超过100个字符' },
            {
              pattern: /^[\u4e00-\u9fa5·]+$/,
              message: '客户姓名只能包含中文字符和"·"符号',
            },
          ],
        },
        {
          field: 'phoneNumber',
          title: '联系电话',
          element: 'slot',
          slotName: 'phoneNumber',
          rules: [
            { required: true, message: '请输入联系电话' },
            { len: 11, message: '请输入11位手机号' },
          ],
        },
        {
          field: 'customerType',
          title: '客户类型',
          element: 'slot',
          slotName: 'customerType',
          rules: [{ max: 100, message: '客户类型不能超过100个字符' }],
        },
        {
          field: 'weChat',
          title: '微信',
          rules: [{ max: 100, message: '微信不能超过100个字符' }],
        },
        {
          field: 'companyIndustry',
          title: '公司/行业',
          element: 'slot',
          slotName: 'companyIndustry',
          rules: [{ max: 100, message: '公司/行业不能超过100个字符' }],
        },
        {
          field: 'email',
          title: '邮箱',
          rules: [
            { max: 100, message: '邮箱不能超过100个字符' },
            { type: 'email', message: '请输入正确的邮箱格式' },
          ],
        },
        {
          field: 'intentionLevel',
          title: '意向程度',
          element: 'slot',
          slotName: 'intentionLevel',
          rules: [{ max: 100, message: '意向程度不能超过100个字符' }],
        },
        {
          field: 'customerLevel',
          title: '客户等级',
          element: 'slot',
          slotName: 'customerLevel',
          rules: [{ max: 100, message: '客户等级不能超过100个字符' }],
        },
        {
          field: 'address',
          title: '所在省市',
          element: 'slot',
          slotName: 'address',
        },
        {
          field: 'customerTag',
          title: '客户标签',
          element: 'slot',
          slotName: 'customerTags',
        },
        {
          field: 'kpFlag',
          title: '是否KP',
          element: 'slot',
          slotName: 'kpFlag',
        },
        {
          field: 'companyName',
          title: '公司名称',
          element: 'slot',
          slotName: 'companyName',
          rules: [{ max: 100, message: '公司名称不能超过100个字符' }],
        },
        {
          field: 'currentFollower',
          title: '跟进人',
          element: 'a-select',
          props: {
            options: this.followerOptionsWithId.map((item) => ({
              value: item.nickName,
              label: item.nickName,
            })),
            allowClear: true,
            showSearch: true,
            filterOption: false,
          },
          on: {
            search: this.searchFollowUserOptionsNew,
          },
          rules: [{ max: 100, message: '跟进人不能超过100个字符' }],
        },
        {
          field: 'remark',
          title: '客户备注信息',
          element: 'a-textarea',
          rules: [{ max: 1000, message: '客户备注信息不能超过1000个字符' }],
          props: {
            rows: 4,
            maxLength: 1000,
            showCount: true,
          },
        },
      ];
    },
  },
  created() {
    this.customerId = this.$route.query.customerId;
    this.phoneNumber = this.$route.query.phoneNumber;
    this.initData();
  },
  methods: {
    // 初始化数据
    async initData() {
      await this.initOptionsData();
      this.loadCustomerDetail();
      this.loadOrderList();
      this.loadOperationLog();
    },

    // 初始化选项数据
    async initOptionsData() {
      try {
        // 使用新的统一接口并行获取各种选项数据
        const [
          customerTypeRes,
          companyIndustryRes,
          companyNameRes,
          intentionLevelRes,
          customerLevelRes,
          customerTagRes,
          positionRes,
        ] = await Promise.all([
          getFieldOptionsAPI({ fieldName: 'customer_type' }),
          getFieldOptionsAPI({ fieldName: 'company_industry' }),
          getFieldOptionsAPI({ fieldName: 'company_name' }),
          getFieldOptionsAPI({ fieldName: 'intention_level' }),
          getFieldOptionsAPI({ fieldName: 'customer_level' }),
          getFieldOptionsAPI({ fieldName: 'customer_tag' }),
          getFieldOptionsAPI({ fieldName: 'position_name' }),
        ]);

        // 使用新接口获取跟进人数据
        const [followerRes] = await Promise.all([getFollowerListAPI({ nickName: '' })]);

        this.customerTypeOptions = customerTypeRes[0]?.data?.filter((item) => item !== null) || [];
        this.companyIndustryOptions = companyIndustryRes[0]?.data?.filter((item) => item !== null) || [];
        this.intentionLevelOptions = intentionLevelRes[0]?.data?.filter((item) => item !== null) || [];
        this.customerLevelOptions = customerLevelRes[0]?.data?.filter((item) => item !== null) || [];
        this.customerTagOptions = customerTagRes[0]?.data?.filter((item) => item !== null) || [];
        this.positionOptions = positionRes[0]?.data?.filter((item) => item !== null) || [];
        this.companyNameOptions = companyNameRes[0]?.data?.filter((item) => item !== null) || [];

        // 新的跟进人选项，包含userId和nickName
        this.followerOptionsWithId = followerRes[0]?.data?.filter((item) => item !== null) || [];

        // 获取省市数据
        await this.getProvince();
      } catch (error) {
        console.error('初始化选项数据失败:', error);
      }
    },

    // 获取省份数据
    async getProvince() {
      try {
        // 使用新的API获取省市数据
        const [result] = await getProvinceTreeAPI({});
        if (result.success && result.data) {
          this.provinceList = this.transformProvinceData(result.data);
        } else {
          // 如果新接口失败，回退到原有数据
          this.getProvinceFromLocal();
        }
      } catch (error) {
        console.error('获取省市数据失败，使用本地数据:', error);
        this.getProvinceFromLocal();
      }
    },

    // 转换新API返回的省市数据格式
    transformProvinceData(data) {
      return data.map((province) => {
        return {
          label: province.areaName,
          value: province.areaCode + '|' + province.areaName, // 存储 code|name 格式
          areaCode: province.areaCode,
          areaName: province.areaName,
          children: (province.children || []).map((city) => {
            return {
              label: city.areaName,
              value: city.areaCode + '|' + city.areaName, // 存储 code|name 格式
              areaCode: city.areaCode,
              areaName: city.areaName,
            };
          }),
        };
      });
    },

    // 回退方法：使用本地省份数据
    getProvinceFromLocal() {
      // 这里可以添加本地省份数据的处理逻辑
      this.provinceList = [];
    },

    // 加载客户详情
    async loadCustomerDetail() {
      try {
        const [result] = await customerDetailAPI({
          customerId: this.customerId,
        });
        if (result.success && result.data) {
          this.customerDetail = result.data;
        }
      } catch (error) {
        console.error('加载客户详情失败:', error);
        this.$message.error('加载客户详情失败');
      }
    },

    // 加载销售工单列表
    async loadOrderList() {
      this.orderLoading = true;
      try {
        const [result] = await workOrderListAPI({
          saleOrderFlag: '1',
          phoneNumber: this.phoneNumber,
          pageNum: this.orderPagination.current,
          pageSize: this.orderPagination.pageSize,
        });

        if (result.success) {
          this.orderList = result.data || [];
          this.orderPagination.total = result.count || 0;
        }
      } catch (error) {
        console.error('加载销售工单失败:', error);
        this.$message.error('加载销售工单失败');
      } finally {
        this.orderLoading = false;
      }
    },

    // 加载操作日志
    async loadOperationLog() {
      try {
        const [result] = await getOperationLogAPI({
          businessNo: this.customerId,
        });

        if (result.success) {
          this.logList = result.data || [];
        }
      } catch (error) {
        console.error('加载操作日志失败:', error);
        this.$message.error('加载操作日志失败');
      }
    },

    // 处理销售工单表格变化
    handleOrderTableChange(pagination) {
      this.orderPagination.current = pagination.current;
      this.orderPagination.pageSize = pagination.pageSize;
      this.loadOrderList();
    },

    // 查看工单详情
    viewOrder(orderId) {
      // 这里可以跳转到工单详情页面
      this.$router.push({
        path: '/workorderdetail',
        query: {
          orderId: orderId,
        },
      });
    },

    // 处理修改按钮点击
    handleEdit() {
      this.editFormData = { ...this.customerDetail };

      // 处理地址数据
      if (
        this.customerDetail.provinceCode &&
        this.customerDetail.provinceName &&
        this.customerDetail.cityCode &&
        this.customerDetail.cityName
      ) {
        this.editFormData.address = [
          this.customerDetail.provinceCode + '|' + this.customerDetail.provinceName,
          this.customerDetail.cityCode + '|' + this.customerDetail.cityName,
        ];
      }

      // 处理客户标签数据
      if (this.customerDetail.customerTag) {
        this.editFormData.customerTag = this.customerDetail.customerTag.split(',');
      }

      this.editVisible = true;
    },

    // 处理返回按钮点击
    handleBack() {
      this.$router.go(-1);
    },

    // 处理编辑表单提交
    async handleEditSubmit() {
      try {
        // 验证表单
        const valid = await this.$refs.editForm.validate();
        if (!valid) return;

        this.editLoading = true;

        const formData = { ...this.editFormData };

        // 处理地址数据
        if (Array.isArray(formData.address)) {
          if (formData.address.length >= 2) {
            const provinceData = formData.address[0].split('|');
            const cityData = formData.address[1].split('|');

            if (provinceData.length === 2 && cityData.length === 2) {
              formData.provinceCode = provinceData[0];
              formData.provinceName = provinceData[1];
              formData.cityCode = cityData[0];
              formData.cityName = cityData[1];
            }
          }
          formData.address = formData.address.join(',');
        }

        // 处理跟进人数据
        if (formData.currentFollower) {
          const followerInfo = this.followerOptionsWithId.find((item) => item.nickName === formData.currentFollower);
          if (followerInfo) {
            formData.currentFollowerId = followerInfo.userId;
          }
        }

        // 处理客户标签数据
        if (Array.isArray(formData.customerTag)) {
          formData.customerTag = formData.customerTag.join(',');
        }

        await customerUpdateAPI(formData);

        this.$message.success('修改客户信息成功');
        this.editVisible = false;
        this.loadCustomerDetail(); // 刷新详情
      } catch (error) {
        console.error('修改客户信息失败:', error);
        this.$message.error('修改客户信息失败');
      } finally {
        this.editLoading = false;
      }
    },

    // 处理编辑弹窗取消
    handleEditCancel() {
      this.editVisible = false;
      this.phoneErrorMsg = '';
    },

    // 检查手机号唯一性
    async checkPhoneUnique(phone, customerId) {
      this.phoneErrorMsg = '';
      if (!phone || phone.length !== 11) {
        return;
      }

      try {
        const [result] = await verifyMobileAPI({
          mobile: phone,
          customerId,
        });
        if (result.success && result.data === true) {
          this.phoneErrorMsg = '该手机号已存在！';
          this.$message.error('该手机号已存在！');
        }
      } catch (error) {
        console.error('检查手机号唯一性失败:', error);
      }
    },

    // 处理KP选择变化
    handleKpChange(params) {
      console.log('KP选择变化:', params.kpFlag);
    },

    // 自动完成搜索方法
    async searchCustomerTypeOptions(value) {
      const [result] = await getFieldOptionsAPI({ fieldName: 'customer_type', fieldValue: value });
      this.customerTypeOptions = result.data?.filter((item) => item !== null) || [];
    },

    async searchCompanyIndustryOptions(value) {
      const [result] = await getFieldOptionsAPI({ fieldName: 'company_industry', fieldValue: value });
      this.companyIndustryOptions = result.data?.filter((item) => item !== null) || [];
    },

    async searchIntentionLevelOptions(value) {
      const [result] = await getFieldOptionsAPI({ fieldName: 'intention_level', fieldValue: value });
      this.intentionLevelOptions = result.data?.filter((item) => item !== null) || [];
    },

    async searchCustomerLevelOptions(value) {
      const [result] = await getFieldOptionsAPI({ fieldName: 'customer_level', fieldValue: value });
      this.customerLevelOptions = result.data?.filter((item) => item !== null) || [];
    },

    async searchCompanyNameOptions(value) {
      const [result] = await getFieldOptionsAPI({ fieldName: 'company_name', fieldValue: value });
      this.companyNameOptions = result.data?.filter((item) => item !== null) || [];
    },

    async searchFollowUserOptionsNew(value) {
      try {
        const [result] = await getFollowerListAPI({ nickName: value });
        this.followerOptionsWithId = result.data?.filter((item) => item !== null) || [];
      } catch (error) {
        console.error('搜索跟进人失败:', error);
      }
    },

    async searchPositionOptions(value) {
      const [result] = await getFieldOptionsAPI({ fieldName: 'position_name', fieldValue: value });
      this.positionOptions = result.data?.filter((item) => item !== null) || [];
    },

    async searchCustomerTagOptions(value) {
      const [result] = await getFieldOptionsAPI({ fieldName: 'customer_tag', fieldValue: value });
      this.customerTagOptions = result.data?.filter((item) => item !== null) || [];
    },

    // 自动完成选择事件
    onCustomerTypeSelect(value) {
      console.log('选择客户类型:', value);
    },

    onCompanyIndustrySelect(value) {
      console.log('选择公司行业:', value);
    },

    onIntentionLevelSelect(value) {
      console.log('选择意向程度:', value);
    },

    onCustomerLevelSelect(value) {
      console.log('选择客户等级:', value);
    },

    onCompanyNameSelect(value) {
      console.log('选择公司名称:', value);
    },

    onPositionSelect(value) {
      console.log('选择岗位名称:', value);
    },

    // 创建工单
    handleCreateOrder() {
      this.workOrderModalVisible = true;
      this.workOrderFormData = {
        ...initParams(this.workOrderFormConfig),
        customerName: this.customerDetail.customerName,
        phoneNumber: this.customerDetail.phoneNumber,
        customerId: this.customerDetail.customerId,
      };
      this.resetWorkOrderForm();
    },

    // 工单创建弹窗提交
    async handleWorkOrderSubmit() {
      try {
        // 验证表单
        const valid = await this.$refs.workOrderForm.validate();
        if (!valid) return;

        // 验证模板表单
        if (this.showMod && this.showTemp) {
          try {
            await this.$refs.templateForm.validate();
          } catch {
            this.$message.error('请完善模板字段');
            return;
          }
        }

        this.workOrderModalLoading = true;

        // 创建单个工单
        const customerData = {
          customerId: this.customerDetail.customerId,
          customerName: this.customerDetail.customerName,
          phoneNumber: this.customerDetail.phoneNumber,
        };

        const result = await this.createSingleOrder(this.workOrderFormData, customerData);

        if (result.success) {
          this.workOrderModalVisible = false;
          this.resetWorkOrderForm();
          // 刷新工单列表
          this.loadOrderList();
        }
      } catch (error) {
        console.error('工单创建失败:', error);
        this.$message.error('工单创建失败');
      } finally {
        this.workOrderModalLoading = false;
      }
    },

    // 工单创建弹窗取消
    handleWorkOrderCancel() {
      this.workOrderModalVisible = false;
      this.resetWorkOrderForm();
    },
  },
};
</script>

<style lang="less" scoped>
.customer-detail {
  padding: 24px;
  background: #fff;
  min-height: calc(100vh - 64px);

  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
    }

    .header-actions {
      .ant-btn {
        margin-left: 8px;
      }
    }
  }

  .detail-content {
    .ant-descriptions {
      .ant-descriptions-item-label {
        background: #fafafa;
        font-weight: 500;
      }
    }
  }
}

.error-msg {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

#components-layout-demo-basic {
  text-align: center;
}
#components-layout-demo-basic .ant-layout-header,
#components-layout-demo-basic .ant-layout-footer {
  background: #fff;
  color: rgba(0, 0, 0, 0.65);
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  background-color: #fafafa;
}
#components-layout-demo-basic .ant-layout-footer {
  line-height: 1.5;
}
#components-layout-demo-basic .ant-layout-sider {
  background: #fff;
  color: rgba(0, 0, 0, 0.65);
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  padding: 8px;
}
#components-layout-demo-basic .ant-layout-content {
  background: #fff;
  color: rgba(0, 0, 0, 0.65);
  min-height: 120px;
  line-height: 80px;
}
#components-layout-demo-basic > .ant-layout {
  margin-bottom: 48px;
}
#components-layout-demo-basic > .ant-layout:last-child {
  margin: 0;
}
.header {
  display: flex;
  justify-content: flex-end;
}

//头像以及名字等
.a-box {
  margin-top: 32px;
  display: inline-block;
  position: relative;
  min-width: 250px;
  .name {
    font-size: 35px;
    margin-left: 16px;
  }
  .area {
    font-size: 20px;
    margin-left: 16px;
  }
  .tel {
    font-size: 18px;
    display: block;
    position: absolute;
    top: 40px;
    left: 112px;
    white-space: nowrap;
  }
}
//后续加需求的右侧刷新按钮
.refresh {
  position: absolute;
  right: 16px;
  display: inline-block;
}
//侧边标题
.sider-title {
  font-size: 16px;
  text-align: left;
  color: #333333;
  line-height: 32px;
  height: 32px;
}
//左侧盒子
.call-box {
  .call {
    height: 102px;
    border-radius: 8px;
    background-color: #fafafa;
    margin-top: 12px;
    margin-bottom: 12px;
    padding: 10px;
    font-size: 12px;
    cursor: pointer; //悬浮时变手指
    .line-a {
      display: flex;
      justify-content: space-between;
      line-height: 20px;
      height: 20px;
    }
  }
  // 左侧选中时候的表现
  .leftActive {
    border: 2px solid #1b58f4;
    transform: translateY(-2px);
    animation-duration: 0.5s;
  }
}
//关注按钮
.attention {
  display: inline-block;
  margin-right: 10px;
  color: #165dff;
  cursor: pointer;
  padding: 0;
  min-width: 38px;
  height: 30px;
  line-height: 30px;
}
//tab大盒子
.content-tab-box {
  margin-top: 48px;
}
.tab1-box {
  padding-left: 80px;
  padding-right: 80px;
  //访客详情大标题
  .line-b-title {
    border-left: 4px solid #1b58f4;
    padding-left: 10px; /* 可选，用于控制文字与边框的间距 */
    text-align: left;
    color: color rgb(51, 51, 51);
    font-weight: bold;
    margin-top: 16px;
  }
  //访客详情内容
  .line-b {
    height: 38px;
    line-height: 38px;
    text-align: left;
    font-size: 13px;
    color: rgb(170, 170, 170);
    span {
      color: rgb(51, 51, 51);
    }
  }
}
//紧急字体
.exgency {
  color: red;
}
//访客名称
.tag-place {
  display: flex;
  justify-content: flex-end;
  top: -36px;
  position: relative;
  .tag {
    position: absolute;
    height: 32px;
    line-height: 32px;
  }
}
.name-button {
  margin-right: 4px;
}
.guest-tag {
  //标签样式
  // display: flex;
  // justify-content: space-around;
  .tags {
    white-space: nowrap;
    line-height: 32px;
    height: 32px;
  }
}

.icon-plus {
  margin-right: 4px;
}
.button-flex {
  margin-top: 16px;
  display: flex;
  justify-content: space-around;
}

.templateFields {
  // /deep/ .ant-form-item label {
  //   white-space: break-spaces;
  //   text-align: justify;
  //   display: inline-flex;
  // }

  /deep/ .ant-form-item {
    margin-bottom: 4px;
    display: flex;
  }
  /deep/ .ant-form-item label {
    white-space: break-spaces;
    text-align: justify;
    display: inline-flex;
    font-size: 12px;
    color: #999;
  }
}

.replyMark {
  margin-bottom: 8px;
}

//左侧角标
.head-icon {
  position: absolute;
  border-radius: 2px;
  transform: translate(168px, -20px) scale(0.8);
  min-width: 36px;
}

// /deep/ .ant-form-item-label {
//   text-align: left;
//   width: fit-content;
//   display: inline-block;
// }
.templateFields /deep/ .ant-form-item label {
  display: inline-block;
  // white-space: nowrap;
}
/deep/ .ant-form-item-control {
  text-align: left;
}
.orderContent {
  padding-top: 24px;
  /deep/ .replyLine {
    margin-bottom: 4px;
  }
  /deep/ .quillWrapper .ql-toolbar.ql-snow .ql-formats {
    transform: scale(0.9);
    display: inline-flex;
    margin: 0 -1px;
  }
}

.visitorForm {
  /deep/ .ant-form-item-label > label {
    color: #999;
    font-size: 12px;
  }
}
</style>
