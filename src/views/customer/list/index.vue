<template>
  <div>
    <BuseCrud
      ref="crud"
      title=""
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :modalConfig="modalConfig"
      @modalCancel="modalCancelHandler"
      @modalSubmit="modalSubmit"
      @modalConfirm="modalConfirmHandler"
      @handleReset="handleReset"
      @rowEdit="rowEdit"
      @loadData="loadData"
      @handleCreate="rowAdd"
      @rowView="rowView"
    >
      <template #defaultHeader>
        <span class="button-right">
          <a-button @click="exportList" v-hasPermi="['system:customer:export']">导出</a-button>
        </span>
        <span class="button-right">
          <a-button type="primary" @click="openBatchImport" v-hasPermi="['system:customer:import']">批量导入</a-button>
        </span>
        <span class="button-right">
          <a-button type="primary" @click="openAssignFollower" v-hasPermi="['system:customer:distribute']"
            >分配跟进人</a-button
          >
        </span>
        <span class="button-right">
          <a-button type="primary" @click="batchCreateOrder" v-hasPermi="['system:customer:createSale']"
            >创建工单</a-button
          >
        </span>
      </template>

      <template #datePick>
        <a-range-picker v-model="params.createTime" @change="changeTime(params.createTime)" />
      </template>

      <!-- 客户姓名输入框 -->
      <template slot="customerName" slot-scope="{ params }">
        <a-input v-model="params.customerName" placeholder="请输入客户姓名" :maxLength="100"></a-input>
      </template>

      <!-- 联系电话输入框（带唯一性验证） -->
      <template slot="phoneNumber" slot-scope="{ params }">
        <a-input
          v-model="params.phoneNumber"
          placeholder="请输入11位手机号"
          :maxLength="11"
          @blur="checkPhoneUnique(params.phoneNumber, params.customerId)"
        ></a-input>
        <div v-if="phoneErrorMsg" class="error-msg">{{ phoneErrorMsg }}</div>
      </template>

      <!-- 所在省市级联选择器 -->
      <template slot="address" slot-scope="{ params }">
        <a-cascader
          v-model="params.address"
          :options="provinceList"
          placeholder="请选择所在省市"
          :show-search="true"
          change-on-select
        />
      </template>

      <!-- 是否KP选择器 -->
      <template slot="kpFlag" slot-scope="{ params }">
        <a-radio-group v-model="params.kpFlag" @change="handleKpChange(params)">
          <a-radio value="0">否</a-radio>
          <a-radio value="1">是</a-radio>
        </a-radio-group>
        <!-- 岗位名称输入框（当选择是/否时都显示） -->
        <div style="margin-top: 8px">
          <a-auto-complete
            v-model="params.positionName"
            :data-source="positionOptions"
            placeholder="请输入或选择岗位名称"
            @search="searchPositionOptions"
            @select="onPositionSelect"
            style="width: 100%"
          />
        </div>
      </template>

      <!-- 客户类型自动完成下拉选择 -->
      <template slot="customerType" slot-scope="{ params }">
        <a-auto-complete
          v-model="params.customerType"
          :data-source="customerTypeOptions"
          placeholder="请输入或选择客户类型"
          @search="searchCustomerTypeOptions"
          @select="onCustomerTypeSelect"
        />
      </template>

      <!-- 公司行业自动完成下拉选择 -->
      <template slot="companyIndustry" slot-scope="{ params }">
        <a-auto-complete
          v-model="params.companyIndustry"
          :data-source="companyIndustryOptions"
          placeholder="请输入或选择公司行业"
          @search="searchCompanyIndustryOptions"
          @select="onCompanyIndustrySelect"
        />
      </template>

      <!-- 意向程度自动完成下拉选择 -->
      <template slot="intentionLevel" slot-scope="{ params }">
        <a-auto-complete
          v-model="params.intentionLevel"
          :data-source="intentionLevelOptions"
          placeholder="请输入或选择意向程度"
          @search="searchIntentionLevelOptions"
          @select="onIntentionLevelSelect"
        />
      </template>

      <!-- 客户等级自动完成下拉选择 -->
      <template slot="customerLevel" slot-scope="{ params }">
        <a-auto-complete
          v-model="params.customerLevel"
          :data-source="customerLevelOptions"
          placeholder="请输入或选择客户等级"
          @search="searchCustomerLevelOptions"
          @select="onCustomerLevelSelect"
        />
      </template>

      <!-- 公司名称自动完成下拉选择 -->
      <template slot="companyName" slot-scope="{ params }">
        <a-auto-complete
          v-model="params.companyName"
          :data-source="companyNameOptions"
          placeholder="请输入或选择公司名称"
          @search="searchCompanyNameOptions"
          @select="onCompanyNameSelect"
        />
      </template>

      <!-- 客户标签多选 -->
      <template slot="customerTags" slot-scope="{ params }">
        <a-select
          v-model="params.customerTag"
          mode="tags"
          :token-separators="[',']"
          placeholder="请输入或选择客户标签"
          @search="searchCustomerTagOptions"
          style="width: 100%"
          clearable
        >
          <a-select-option v-for="tag in customerTagOptions" :key="tag" :value="tag">
            {{ tag }}
          </a-select-option>
        </a-select>
      </template>
    </BuseCrud>

    <!-- 批量导入弹窗 -->
    <BatchUpload
      ref="batchUpload"
      title="批量导入客户信息"
      :uploadApi="'/customer-ticket/customerInfo/importCustomerInfo'"
      :templateUrl="templateDownloadUrl"
      @beforeUpload="handleBeforeUpload"
      @uploadSuccess="handleUploadSuccess"
    />

    <!-- 数据导入处理方式选择弹窗 -->
    <ImportMethodModal
      :visible="importMethodVisible"
      @confirm="handleImportMethodConfirm"
      @cancel="handleImportMethodCancel"
    />

    <!-- 分配跟进人弹窗 -->
    <AssignFollower
      :visible="assignFollowerVisible"
      :selectedCustomers="selectedCustomers"
      @success="handleAssignSuccess"
      @cancel="handleAssignCancel"
    />

    <!-- 工单创建弹窗 -->
    <a-modal
      :title="workOrderModalTitle"
      :visible="workOrderModalVisible"
      width="50%"
      :destroyOnClose="true"
      @ok="handleWorkOrderSubmit"
      @cancel="handleWorkOrderCancel"
      :confirmLoading="workOrderModalLoading"
    >
      <DynamicForm ref="workOrderForm" :config="workOrderFormConfig" :params="workOrderFormData" :defaultColSpan="24">
        <!-- 工单内容和问题描述富文本编辑器 -->
        <template #orContent>
          <div class="orderContent">
            <AddReply :orderSort="nowCategoryID" @selectedReply="handleSelect" />
            <Editor
              :value="contentInput"
              v-model="workOrderFormData.orderContent"
              @blur="workOrderFormData.orderContent = contentInput"
              @input="giveParam(workOrderFormData.orderContent)"
            />
          </div>
        </template>

        <!-- 工单标签 -->
        <template #guestTag>
          <div class="guest-tag">
            <a-tag class="tags" color="pink" v-for="(item, index) in orderName" :key="index">{{ item }}</a-tag>
            <a-button
              v-show="!showTags"
              type="primary"
              @click="
                guestTagHandler;
                showTags = !showTags;
              "
              ><span><a-icon type="plus-circle" class="icon-plus" /></span>添加工单标签</a-button
            >

            <Tags :showTags="showTags" @custom-event="transTags" @custom-showAdd="changeAddTags" />
          </div>
        </template>

        <!-- 流转方式 -->
        <template #flowType>
          <a-radio-group v-model="flowRadio" @change="choiceManFlow">
            <a-radio value="1">人工流转</a-radio>
            <a-radio value="2" v-if="enabledFlag == '1'" :disabled="autoFlag">自动分配</a-radio>
            <a-radio value="3" v-if="enabledFlag == '1'">工单池流转</a-radio>
          </a-radio-group>
        </template>

        <!-- 工单受理人 -->
        <template #orderHandler>
          <a-tree-select
            placeholder="请选择受理人员"
            :disabled="autoDisable"
            v-model="workOrderFormData.orderHandler"
            :treeData="handleList"
            showSearch
            labelInValue
            :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
            @focus="getUserList()"
          >
            <template slot="myTitle" slot-scope="item">
              <StatuNode :newNodes="item" />
            </template>
          </a-tree-select>
        </template>

        <!-- 工单模板 -->
        <template #selectOr>
          <a-select
            placeholder="请在选择工单分类之后选择工单模板"
            @change="handleChange"
            v-model="choiceValue"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
          >
            <a-select-option v-for="(item, index) in exList || []" :key="index">
              {{ item.templateName }}
            </a-select-option>
          </a-select>

          <div class="templateFields">
            <a-button
              type="primary"
              v-if="showMod"
              @click="
                showMod = false;
                choiceValue = undefined;
              "
              ><a-icon type="minus-circle" /> 取消模板选择</a-button
            >
            <!-- :rules="rules[fielditem.templateFieldId]" :rules="rules" -->
            <a-form-model ref="templateForm" :model="workOrderFormData">
              <template v-for="(fielditem, index) in choiceExList.fields">
                <a-form-model-item
                  v-if="fielditem !== undefined && showMod && fielditem.fieldType == '1'"
                  :label-col="{ span: 10 }"
                  :wrapper-col="{ span: 14 }"
                  :label="fielditem.fieldName"
                  :key="index"
                  :rules="[{ required: fielditem.emptyFlag === '0', message: '请输入', trigger: 'blur' }]"
                  :prop="`exValues.${index}`"
                  ><a-input
                    @focus="tryIt(workOrderFormData)"
                    :defaultValue="fielditem.defaultValue"
                    @hook:mounted="workOrderFormData.exValues[index] = fielditem.defaultValue"
                    v-model="workOrderFormData.exValues[index]"
                  ></a-input>
                </a-form-model-item>

                <a-form-model-item
                  v-if="fielditem !== undefined && showMod && fielditem.fieldType == '2'"
                  :label-col="{ span: 10 }"
                  :wrapper-col="{ span: 14 }"
                  :label="fielditem.fieldName"
                  :key="index"
                  :rules="[{ required: fielditem.emptyFlag === '0', message: '请输入', trigger: 'blur' }]"
                  :prop="`exValues.${index}`"
                >
                  <a-select
                    @focus="getModSelect(fielditem.fieldDataKey)"
                    @hook:mounted="workOrderFormData.exValues[index] = fielditem.defaultValue"
                    v-model="workOrderFormData.exValues[index]"
                  >
                    <a-select-option v-for="selectItem in selectOne" :key="selectItem.value">
                      {{ selectItem.label }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>

                <a-form-model-item
                  v-if="fielditem !== undefined && showMod && fielditem.fieldType == '3'"
                  :label-col="{ span: 10 }"
                  :wrapper-col="{ span: 14 }"
                  :label="fielditem.fieldName"
                  :key="index"
                  :rules="[{ required: fielditem.emptyFlag === '0', message: '请输入', trigger: 'blur' }]"
                  :prop="`exValues.${index}`"
                  ><a-date-picker
                    :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                    style="width: 100%"
                    :defaultValue="fielditem.defaultValue"
                    @hook:mounted="workOrderFormData.exValues[index] = fielditem.defaultValue"
                    v-model="workOrderFormData.exValues[index]"
                    valueFormat="YYYY-MM-DD HH:mm:ss"
                  ></a-date-picker>
                </a-form-model-item>

                <a-form-model-item
                  v-if="fielditem !== undefined && showMod && fielditem.fieldType == '4'"
                  :label-col="{ span: 10 }"
                  :wrapper-col="{ span: 14 }"
                  :label="fielditem.fieldName"
                  :key="index"
                  :rules="[{ required: fielditem.emptyFlag === '0', message: '请输入', trigger: 'blur' }]"
                  :prop="`exValues.${index}`"
                >
                  <Editor
                    :value="secContentInput"
                    :defaultValue="fielditem.defaultValue"
                    @hook:mounted="workOrderFormData.exValues[index] = fielditem.defaultValue"
                    v-model="workOrderFormData.exValues[index]"
                  />
                </a-form-model-item>

                <a-form-model-item
                  v-if="fielditem !== undefined && showMod && fielditem.fieldType == '5'"
                  :label-col="{ span: 10 }"
                  :wrapper-col="{ span: 14 }"
                  :label="fielditem.fieldName"
                  :key="index"
                  :rules="[{ required: fielditem.emptyFlag === '0', message: '请输入', trigger: 'blur' }]"
                  :prop="`exValues.${index}`"
                >
                  <a-select
                    mode="tags"
                    style="width: 100%"
                    placeholder="请输入"
                    @change="fieldSelect"
                    :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                    @focus="getModSecSelect(fielditem.fieldDataKey)"
                    @hook:mounted="workOrderFormData.exValues[index] = fielditem.defaultValue"
                    v-model="workOrderFormData.exValues[index]"
                  >
                    <a-select-option v-for="item in selectCom" :key="item.value">
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>

                <a-form-model-item
                  v-if="fielditem !== undefined && showMod && fielditem.fieldType == '6'"
                  :label-col="{ span: 10 }"
                  :wrapper-col="{ span: 14 }"
                  :label="fielditem.fieldName"
                  :key="index"
                  :rules="[{ required: fielditem.emptyFlag === '0', message: '请输入', trigger: 'blur' }]"
                  :prop="`exValues.${index}`"
                >
                  <a-input-number
                    :defaultValue="fielditem.defaultValue"
                    @hook:mounted="workOrderFormData.exValues[index] = fielditem.defaultValue"
                    v-model="workOrderFormData.exValues[index]"
                    @change="fieldCount"
                  />
                </a-form-model-item>

                <a-form-model-item
                  v-if="fielditem !== undefined && showMod && fielditem.fieldType == '7'"
                  :label-col="{ span: 10 }"
                  :wrapper-col="{ span: 14 }"
                  :label="fielditem.fieldName"
                  :key="index"
                  :rules="[{ required: fielditem.emptyFlag === '0', message: '请输入', trigger: 'blur' }]"
                  :prop="`exValues.${index}`"
                >
                  <a-input-number
                    :defaultValue="fielditem.defaultValue"
                    @hook:mounted="workOrderFormData.exValues[index] = fielditem.defaultValue"
                    v-model="workOrderFormData.exValues[index]"
                    @change="fieldCount"
                    :formatter="(value) => `${value}%`"
                    :parser="(value) => value.replace('%', '')"
                  />
                </a-form-model-item>
              </template>
            </a-form-model>
          </div>
        </template>

        <!-- 车型品牌 -->
        <template #vehicle>
          <a-row>
            <a-col :span="8">
              <a-input v-model="workOrderFormData.vehicleBrand" placeholder="请输入品牌"></a-input>
            </a-col>
            <a-col :span="8">
              <a-input v-model="workOrderFormData.vehicleModel" placeholder="请输入车型"></a-input>
            </a-col>
            <a-col :span="8">
              <BuseRangePicker
                type="year"
                v-model="workOrderFormData.vehicleYear"
                format="YYYY"
                placeholder="请选择年份"
                :needShowSecondPicker="() => false"
              ></BuseRangePicker>
            </a-col>
          </a-row>
        </template>

        <!-- 创建新访客 -->
        <template #newGuest>
          <a-button type="primary" @click="showGuestfn(workOrderFormData)"
            ><span>
              <a-icon v-if="showGuest == false" type="plus-circle" class="icon-plus" />
              <a-icon v-if="showGuest == true" type="minus-circle" class="icon-plus" /> </span
            ><span v-if="showGuest == true">取消</span>创建新访客</a-button
          >
          <a-form-model
            ref="visitorForm"
            :model="workOrderFormData"
            v-show="showGuest"
            :rules="visitorRules"
            class="visitorForm"
          >
            <a-form-model-item
              label="访客名称"
              prop="visitorName"
              key="visitorName"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 17 }"
            >
              <a-input
                v-model="workOrderFormData.visitorName"
                placeholder="请输入访客名称"
                @blur="nameFn(workOrderFormData.visitorName)"
                @change="visitorName = workOrderFormData.visitorName"
              />
              <div class="tag-place">
                <a-tag color="blue" class="tag" v-if="man">先生</a-tag>
                <a-tag color="blue" class="tag" v-if="lady">女士</a-tag>
              </div>
              <a-button class="name-button" size="small" @click="spell(workOrderFormData.visitorName, 1)">
                先生
              </a-button>
              <a-button class="name-button" size="small" @click="spell(workOrderFormData.visitorName, 2)">
                女士
              </a-button>
            </a-form-model-item>
            <a-form-model-item
              label="访客电话"
              prop="visitorPhone"
              key="visitorPhone"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 17 }"
            >
              <a-input
                v-model="workOrderFormData.visitorPhone"
                @change="visitorPhone = workOrderFormData.visitorPhone"
              />
            </a-form-model-item>
          </a-form-model>
        </template>
      </DynamicForm>
    </a-modal>

    <!-- 紧急程度确认弹窗 -->
    <a-modal title="提示" :visible="modalSeen" @ok="modalOK" @cancel="modalCancel" okText="确定" cancelText="取消">
      <p>选择该优先级会触发SLA目标，请确认</p>
    </a-modal>
  </div>
</template>

<script>
import {
  customerListAPI,
  customerAddAPI,
  customerUpdateAPI,
  customerExportAPI,
  verifyMobileAPI,
  getFieldOptionsAPI,
  getUserListAPI,
  getFollowerListAPI,
  getProvinceTreeAPI,
} from '@/api/customer';
import moment from 'moment';
import province from '@/views/home/<USER>/province';
import { downLoadXlsx, isHasPermi } from '@/utils/system/common';
import BatchUpload from '@/components/BatchUpload/index.vue';
import ImportMethodModal from '@/components/BatchUpload/ImportMethodModal.vue';
import AssignFollower from '@/components/AssignFollower/index.vue';
import workOrderCreateMixin from '@/mixins/workOrderCreate';
import StatuNode from '@/components/TreeNode/StatuNode.vue';
import { initParams } from '@/utils/buse';

export default {
  name: 'CustomerList',
  mixins: [workOrderCreateMixin],
  components: {
    BatchUpload,
    ImportMethodModal,
    AssignFollower,
    StatuNode,
  },
  data() {
    return {
      loading: false,
      tableData: [],
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      startCreateTime: '', // 开始日期
      endCreateTime: '', // 结束日期
      provinceList: [], // 省份列表
      phoneErrorMsg: '', // 手机号错误提示信息

      // 自动完成选项数据
      customerTypeOptions: [],
      companyIndustryOptions: [],
      intentionLevelOptions: [],
      customerLevelOptions: [],
      companyNameOptions: [],
      positionOptions: [],
      customerTagOptions: [],

      // 新增的选项数据
      followerOptionsWithId: [], // 包含userId和nickName的跟进人选项

      // 筛选参数
      params: {
        customerName: '',
        phoneNumber: '',
        customerType: '',
        companyIndustry: '',
        intentionLevel: '',
        customerLevel: '',
        companyName: '',
        currentFollower: '',
        creator: '',
        createTime: [],
        startCreateTime: '',
        endCreateTime: '',
      },

      // 批量导入相关
      importMethodVisible: false,
      currentUploadFile: null,
      templateDownloadUrl: '/work-order-system/static/客户信息导入模版.xlsx', // 模板下载地址，需要根据实际情况配置

      // 分配跟进人相关
      assignFollowerVisible: false,
      selectedCustomers: [], // 选中的客户列表

      // 工单创建相关
      workOrderModalTitle: '创建工单',
      showTemp: false, // 是否显示模板字段
    };
  },
  computed: {
    // 筛选器配置
    filterOptions() {
      return {
        config: [
          {
            field: 'customerName',
            title: '客户姓名',
            props: { placeholder: '请输入客户姓名' },
          },
          {
            field: 'phoneNumber',
            title: '联系电话',
            props: { placeholder: '请输入联系电话' },
          },
          {
            field: 'customerType',
            title: '客户类型',
            element: 'a-select',
            props: {
              options: this.customerTypeOptions.map((item) => ({ value: item, label: item })),
              allowClear: true,
            },
          },
          {
            field: 'companyIndustry',
            title: '公司行业',
            element: 'a-select',
            props: {
              options: this.companyIndustryOptions.map((item) => ({ value: item, label: item })),
              allowClear: true,
            },
          },
          {
            field: 'intentionLevel',
            title: '意向程度',
            element: 'a-select',
            props: {
              options: this.intentionLevelOptions.map((item) => ({ value: item, label: item })),
              allowClear: true,
            },
          },
          {
            field: 'currentFollower',
            title: '当前跟进人',
            element: 'a-select',
            props: {
              options: this.followerOptionsWithId.map((item) => ({
                value: item.nickName,
                label: item.nickName,
              })),
              allowClear: true,
              showSearch: true,
              filterOption: (input, option) => {
                return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              },
            },
          },
          {
            field: 'createTime',
            title: '创建时间',
            element: 'slot',
            slotName: 'datePick',
          },
        ],
        params: this.params,
      };
    },

    // 表格列配置
    tableColumn() {
      return [
        { type: 'checkbox' },
        {
          field: 'customerName',
          title: '客户姓名',
        },
        {
          field: 'phoneNumber',
          title: '联系电话',
        },
        {
          field: 'customerType',
          title: '客户类型',
        },
        {
          field: 'kpFlagName',
          title: '是否KP',
        },
        {
          field: 'positionName',
          title: '岗位名称',
        },
        {
          field: 'intentionLevel',
          title: '意向程度',
        },
        {
          field: 'companyIndustry',
          title: '公司行业',
        },
        {
          field: 'companyName',
          title: '公司名称',
        },
        {
          field: 'cityInfo',
          title: '所在省市',
        },
        {
          field: 'currentFollower',
          title: '当前跟进人',
        },
        {
          field: 'creator',
          title: '创建人',
        },
        {
          field: 'createTime',
          title: '创建时间',
        },
      ];
    },

    // 弹窗配置
    modalConfig() {
      return {
        formConfig: [
          {
            field: 'customerName',
            title: '客户姓名',
            element: 'slot',
            slotName: 'customerName',
            rules: [
              { required: true, message: '请输入客户姓名' },
              { max: 100, message: '客户姓名不能超过100个字符' },
              {
                pattern: /^[\u4e00-\u9fa5·]+$/,
                message: '客户姓名只能包含中文字符和"·"符号',
              },
            ],
          },
          {
            field: 'phoneNumber',
            title: '联系电话',
            element: 'slot',
            slotName: 'phoneNumber',
            rules: [
              { required: true, message: '请输入联系电话' },
              { len: 11, message: '请输入11位手机号' },
            ],
          },
          {
            field: 'customerType',
            title: '客户类型',
            element: 'slot',
            slotName: 'customerType',
            rules: [{ max: 100, message: '客户类型不能超过100个字符' }],
          },
          {
            field: 'weChat',
            title: '微信',
            rules: [{ max: 100, message: '微信不能超过100个字符' }],
          },
          {
            field: 'companyIndustry',
            title: '公司/行业',
            element: 'slot',
            slotName: 'companyIndustry',
            rules: [{ max: 100, message: '公司/行业不能超过100个字符' }],
          },
          {
            field: 'email',
            title: '邮箱',
            rules: [
              { max: 100, message: '邮箱不能超过100个字符' },
              { type: 'email', message: '请输入正确的邮箱格式' },
            ],
          },
          {
            field: 'intentionLevel',
            title: '意向程度',
            element: 'slot',
            slotName: 'intentionLevel',
            rules: [{ max: 100, message: '意向程度不能超过100个字符' }],
          },
          {
            field: 'customerLevel',
            title: '客户等级',
            element: 'slot',
            slotName: 'customerLevel',
            rules: [{ max: 100, message: '客户等级不能超过100个字符' }],
          },
          {
            field: 'address',
            title: '所在省市',
            element: 'slot',
            slotName: 'address',
          },
          {
            field: 'customerTag',
            title: '客户标签',
            element: 'slot',
            slotName: 'customerTags',
          },
          {
            field: 'kpFlag',
            title: '是否KP',
            element: 'slot',
            slotName: 'kpFlag',
          },
          {
            field: 'companyName',
            title: '公司名称',
            element: 'slot',
            slotName: 'companyName',
            rules: [{ max: 100, message: '公司名称不能超过100个字符' }],
          },
          {
            field: 'currentFollower',
            title: '跟进人',
            element: 'a-select',
            props: {
              options: this.followerOptionsWithId.map((item) => ({
                value: item.nickName,
                label: item.nickName,
              })),
              allowClear: true,
              showSearch: true,
              filterOption: false,
            },
            on: {
              search: this.searchFollowUserOptionsNew,
            },
            rules: [{ max: 100, message: '跟进人不能超过100个字符' }],
          },
          {
            field: 'remark',
            title: '客户备注信息',
            element: 'a-textarea',
            slotName: 'customerRemark',
            rules: [{ max: 1000, message: '客户备注信息不能超过1000个字符' }],
            props: {
              rows: 4,
              maxLength: 1000,
              showCount: true,
            },
          },
        ],
        menu: true,
        menuTitle: '操作',
        addBtn: isHasPermi(['system:customer:add']),
        delBtn: false,
        viewBtn: isHasPermi(['system:customer:view']),
        viewBtnText: '详情',
        editBtn: isHasPermi(['system:customer:edit']),
        editBtnText: '修改',
        addTitle: '新增客户信息',
        editTitle: '修改客户信息',
        menuFixed: 'right',
        //自定义操作
        customOperationTypes: [
          {
            //定义操作类型名称
            typeName: 'createOrder',
            title: '建单',
            event: (row) => {
              this.handleCreateOrder(row);
              return Promise.resolve('建单');
            },
            condition: () => {
              return isHasPermi(['system:customer:createOrder']);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 12,
        },
      };
    },
  },

  beforeMount() {
    // 初始化各种选项数据
    this.initOptionsData();
  },

  mounted() {
    this.loadData();
    this.getProvince();
  },

  methods: {
    rowView(row) {
      // 跳转到客户详情页面
      this.$router.push({
        path: '/customerDetail',
        query: {
          customerId: row.customerId,
          phoneNumber: row.phoneNumber,
        },
      });
    },
    // 初始化选项数据
    async initOptionsData() {
      try {
        // 使用新的统一接口并行获取各种选项数据
        const [
          customerTypeRes,
          companyIndustryRes,
          companyNameRes,
          intentionLevelRes,
          customerLevelRes,
          customerTagRes,
          positionRes,
        ] = await Promise.all([
          getFieldOptionsAPI({ fieldName: 'customer_type' }),
          getFieldOptionsAPI({ fieldName: 'company_industry' }),
          getFieldOptionsAPI({ fieldName: 'company_name' }),
          getFieldOptionsAPI({ fieldName: 'intention_level' }),
          getFieldOptionsAPI({ fieldName: 'customer_level' }),
          getFieldOptionsAPI({ fieldName: 'customer_tag' }),
          getFieldOptionsAPI({ fieldName: 'position_name' }),
        ]);

        // 使用新接口获取跟进人数据
        const [followerRes] = await Promise.all([getFollowerListAPI({ nickName: '' })]);

        this.customerTypeOptions = customerTypeRes[0]?.data?.filter((item) => item !== null) || [];
        this.companyIndustryOptions = companyIndustryRes[0]?.data?.filter((item) => item !== null) || [];
        this.intentionLevelOptions = intentionLevelRes[0]?.data?.filter((item) => item !== null) || [];
        this.customerLevelOptions = customerLevelRes[0]?.data?.filter((item) => item !== null) || [];
        this.customerTagOptions = customerTagRes[0]?.data?.filter((item) => item !== null) || [];
        this.positionOptions = positionRes[0]?.data?.filter((item) => item !== null) || [];
        this.companyNameOptions = companyNameRes[0]?.data?.filter((item) => item !== null) || [];

        // 新的跟进人选项，包含userId和nickName
        this.followerOptionsWithId = followerRes[0]?.data?.filter((item) => item !== null) || [];
      } catch (error) {
        console.error('初始化选项数据失败:', error);
      }
    },

    // 加载数据
    async loadData() {
      this.loading = true;
      try {
        const pageDetail = {
          pageNum: Number(this.tablePage.currentPage),
          pageNumSize: Number(this.tablePage.pageSize),
        };
        this.params.startCreateTime = this.startCreateTime;
        this.params.endCreateTime = this.endCreateTime;

        const [result] = await customerListAPI({ ...this.params, ...pageDetail });
        console.log('客户列表数据:', result.data);

        this.loading = false;
        const data = result.data.map((item) => {
          return {
            ...item,
            createTime: moment(item.createTime).format('YYYY-MM-DD HH:mm:ss'),
          };
        });
        this.tableData = data;
        this.tablePage.total = +result.count;
      } catch (error) {
        this.loading = false;
        console.error('加载客户列表失败:', error);
        this.$message.error('加载客户列表失败');
      }
    },

    // 导出列表
    async exportList() {
      try {
        const [result] = await customerExportAPI(this.params);
        downLoadXlsx(new Blob([result]), '客户列表导出.xlsx');
      } catch (error) {
        console.error('导出客户列表失败:', error);
        this.$message.error('导出客户列表失败');
      }
    },

    // 获取省份数据
    async getProvince() {
      try {
        // 使用新的API获取省市数据
        const [result] = await getProvinceTreeAPI({});
        console.log('省市API返回数据:', result); // 调试日志
        if (result.success && result.data) {
          this.provinceList = this.transformProvinceData(result.data);
          console.log('转换后的省市数据:', this.provinceList); // 调试日志
        } else {
          console.log('新接口返回失败，使用本地数据'); // 调试日志
          // 如果新接口失败，回退到原有数据
          this.getProvinceFromLocal();
        }
      } catch (error) {
        console.error('获取省市数据失败，使用本地数据:', error);
        this.getProvinceFromLocal();
      }
    },

    // 回退方法：使用本地省份数据
    getProvinceFromLocal() {
      const obj = JSON.parse(JSON.stringify(province));
      const result = Object.entries(obj).map(([province, cities]) => ({
        province: province,
        cities: Object.entries(cities).map(([city, districts]) => ({
          city,
          districts,
        })),
      }));
      this.provinceList = this.transformData(result);
    },

    // 转换新API返回的省市数据格式
    transformProvinceData(data) {
      return data.map((province) => {
        return {
          label: province.areaName,
          value: province.areaCode + '|' + province.areaName, // 存储 code|name 格式
          areaCode: province.areaCode,
          areaName: province.areaName,
          children: (province.children || []).map((city) => {
            return {
              label: city.areaName,
              value: city.areaCode + '|' + city.areaName, // 存储 code|name 格式
              areaCode: city.areaCode,
              areaName: city.areaName,
            };
          }),
        };
      });
    },

    // 转换本地省份数据格式
    transformData(data) {
      return data.map((province) => {
        return {
          label: province.province,
          value: province.province,
          children: province.cities.map((city) => {
            return {
              label: city.city,
              value: city.city,
              children: city.districts.map((district) => {
                return {
                  label: district,
                  value: district,
                };
              }),
            };
          }),
        };
      });
    },

    // 时间范围变化处理
    changeTime(value) {
      if (value && value.length === 2) {
        const [startDate, endDate] = value;
        this.startCreateTime = moment(startDate).format('YYYY-MM-DD HH:mm:ss');
        this.endCreateTime = moment(endDate).format('YYYY-MM-DD HH:mm:ss');
      } else {
        this.startCreateTime = '';
        this.endCreateTime = '';
      }
    },

    // 检查手机号唯一性
    async checkPhoneUnique(phone, customerId) {
      this.phoneErrorMsg = '';
      if (!phone || phone.length !== 11) {
        return;
      }

      try {
        const [result] = await verifyMobileAPI({
          mobile: phone,
          customerId,
        });
        if (result.success && result.data === true) {
          this.phoneErrorMsg = '该手机号已存在！';
          this.$message.error('该手机号已存在！');
        }
      } catch (error) {
        console.error('检查手机号唯一性失败:', error);
      }
    },

    // 处理KP选择变化
    handleKpChange(params) {
      // KP选择变化时的处理逻辑
      console.log('KP选择变化:', params.kpFlag);
    },

    // 自动完成搜索方法
    async searchCustomerTypeOptions(value) {
      const [result] = await getFieldOptionsAPI({ fieldName: 'customer_type', fieldValue: value });
      this.customerTypeOptions = result.data?.filter((item) => item !== null) || [];
    },

    async searchCompanyIndustryOptions(value) {
      const [result] = await getFieldOptionsAPI({ fieldName: 'company_industry', fieldValue: value });
      this.companyIndustryOptions = result.data?.filter((item) => item !== null) || [];
    },

    async searchIntentionLevelOptions(value) {
      const [result] = await getFieldOptionsAPI({ fieldName: 'intention_level', fieldValue: value });
      this.intentionLevelOptions = result.data?.filter((item) => item !== null) || [];
    },

    async searchCustomerLevelOptions(value) {
      const [result] = await getFieldOptionsAPI({ fieldName: 'customer_level', fieldValue: value });
      this.customerLevelOptions = result.data?.filter((item) => item !== null) || [];
    },

    async searchCompanyNameOptions(value) {
      const [result] = await getFieldOptionsAPI({ fieldName: 'company_name', fieldValue: value });
      this.companyNameOptions = result.data?.filter((item) => item !== null) || [];
    },

    // 新的搜索跟进人方法（使用新API）
    async searchFollowUserOptionsNew(value) {
      try {
        const [result] = await getFollowerListAPI({ nickName: value });
        this.followerOptionsWithId = result.data?.filter((item) => item !== null) || [];
      } catch (error) {
        console.error('搜索跟进人失败:', error);
      }
    },

    async searchPositionOptions(value) {
      const [result] = await getFieldOptionsAPI({ fieldName: 'position_name', fieldValue: value });
      this.positionOptions = result.data?.filter((item) => item !== null) || [];
    },

    async searchCustomerTagOptions(value) {
      const [result] = await getFieldOptionsAPI({ fieldName: 'customer_tag', fieldValue: value });
      this.customerTagOptions = result.data?.filter((item) => item !== null) || [];
    },

    // 自动完成选择事件
    onCustomerTypeSelect(value) {
      console.log('选择客户类型:', value);
    },

    onCompanyIndustrySelect(value) {
      console.log('选择公司行业:', value);
    },

    onIntentionLevelSelect(value) {
      console.log('选择意向程度:', value);
    },

    onCustomerLevelSelect(value) {
      console.log('选择客户等级:', value);
    },

    onCompanyNameSelect(value) {
      console.log('选择公司名称:', value);
    },

    onPositionSelect(value) {
      console.log('选择岗位名称:', value);
    },

    // 弹窗确认处理
    async modalConfirmHandler(formData) {
      try {
        // 处理地址数据
        if (Array.isArray(formData.address)) {
          // 新的省市数据格式处理
          if (formData.address.length >= 1) {
            const provinceData = formData.address[0].split('|');

            if (provinceData.length === 2) {
              // 新API格式：包含code和name
              formData.provinceCode = provinceData[0];
              formData.provinceName = provinceData[1];
            } else {
              // 兼容旧格式
              formData.provinceName = formData.address[0];
            }

            // 处理市级数据（如果选择了）
            if (formData.address.length >= 2) {
              const cityData = formData.address[1].split('|');

              if (cityData.length === 2) {
                // 新API格式：包含code和name
                formData.cityCode = cityData[0];
                formData.cityName = cityData[1];
              } else {
                // 兼容旧格式
                formData.cityName = formData.address[1];
              }
            }
          }
          formData.address = formData.address.join(',');
        }

        // 处理跟进人数据
        if (formData.currentFollower) {
          // 查找对应的userId
          const followerInfo = this.followerOptionsWithId.find((item) => item.nickName === formData.currentFollower);
          if (followerInfo) {
            formData.currentFollowerId = followerInfo.userId;
          }
        }

        // 处理客户标签数据
        if (Array.isArray(formData.customerTag)) {
          formData.customerTag = formData.customerTag.join(',');
        }

        // 判断是新增还是编辑
        const isEdit = !!formData.customerId;
        const apiMethod = isEdit ? customerUpdateAPI : customerAddAPI;

        const [result] = await apiMethod(formData);
        console.log('保存客户信息结果:', result);

        this.$message.success(isEdit ? '修改客户信息成功' : '新增客户信息成功');
        this.loadData(); // 刷新列表
      } catch (error) {
        console.error('保存客户信息失败:', error);
        this.$message.error('保存客户信息失败');
      }
    },

    // 弹窗提交处理
    modalSubmit() {
      console.log('弹窗提交');
    },

    // 弹窗取消处理
    modalCancelHandler() {
      this.phoneErrorMsg = '';
      console.log('弹窗取消');
    },

    // 新增按钮处理
    rowAdd() {
      this.$refs.crud.switchModalView(true, 'ADD');
    },

    // 编辑行处理
    async rowEdit(row) {
      const customerData = { ...row };

      console.log('编辑客户数据:', customerData); // 调试日志

      // 处理地址数据 - 列表接口返回的是provinceCode, provinceName, cityCode, cityName字段
      if (customerData.provinceCode && customerData.provinceName) {
        // 构建地址数组，优先使用省市代码和名称格式
        customerData.address = [customerData.provinceCode + '|' + customerData.provinceName];

        // 如果有市级数据，也添加进去
        if (customerData.cityCode && customerData.cityName) {
          customerData.address.push(customerData.cityCode + '|' + customerData.cityName);
        }

        console.log('使用新格式地址数据:', customerData.address); // 调试日志
      } else if (customerData.cityInfo) {
        // 如果有cityInfo字段，尝试解析（兼容旧格式）
        const cityInfoParts = customerData.cityInfo.split(',');
        if (cityInfoParts.length >= 1) {
          customerData.address = cityInfoParts;
          console.log('使用cityInfo地址数据:', customerData.address); // 调试日志
        }
      } else {
        console.log('没有找到地址相关数据'); // 调试日志
      }

      // 处理客户标签数据
      if (customerData.customerTag) {
        customerData.customerTag = customerData.customerTag.split(',');
      }

      this.$refs.crud.switchModalView(true, 'UPDATE', customerData);
    },

    // 重置筛选
    handleReset() {
      this.startCreateTime = '';
      this.endCreateTime = '';
      this.tablePage.currentPage = 1;
      this.params = {
        customerName: '',
        phoneNumber: '',
        customerType: '',
        companyIndustry: '',
        intentionLevel: '',
        customerLevel: '',
        companyName: '',
        currentFollower: '',
        creator: '',
        createTime: [],
        startCreateTime: '',
        endCreateTime: '',
      };
      this.loadData();
    },

    // 批量导入相关方法
    openBatchImport() {
      this.$refs.batchUpload.open();
    },

    handleBeforeUpload(file, extraData) {
      // 存储上传文件，显示处理方式选择弹窗
      this.currentUploadFile = file;
      this.importMethodVisible = true;
    },

    handleImportMethodConfirm(saveMethod) {
      // 用户选择了处理方式，执行实际上传
      this.importMethodVisible = false;

      if (this.currentUploadFile) {
        // 调用批量导入组件的上传方法
        this.$refs.batchUpload.doUpload(this.currentUploadFile, { saveMethod });
      }
    },

    handleImportMethodCancel() {
      // 取消选择处理方式
      this.importMethodVisible = false;
      this.currentUploadFile = null;
      this.$refs.batchUpload.submitLoading = false;
    },

    handleUploadSuccess() {
      // 上传成功后刷新列表
      this.loadData();
      this.$message.success('批量导入成功');
    },

    // 分配跟进人相关方法
    openAssignFollower() {
      this.selectedCustomers = this.$refs.crud.getCheckboxRecords() || [];
      if (this.selectedCustomers.length === 0) {
        this.$message.warning('请先选择要分配的客户');
        return;
      }
      this.assignFollowerVisible = true;
    },

    handleAssignSuccess() {
      // 分配成功后刷新列表并清空选择
      this.loadData();
      this.selectedCustomers = [];
      this.assignFollowerVisible = false;
    },

    handleAssignCancel() {
      this.assignFollowerVisible = false;
    },

    // 批量创建工单
    batchCreateOrder() {
      this.selectedCustomers = this.$refs.crud.getCheckboxRecords() || [];
      if (this.selectedCustomers.length === 0) {
        this.$message.warning('请先选择要创建工单的客户');
        return;
      }
      this.workOrderModalTitle = '批量创建工单';
      this.workOrderModalVisible = true;
      this.workOrderFormData = initParams(this.workOrderFormConfig);
      this.resetWorkOrderForm();
    },

    // 单个客户建单
    handleCreateOrder(customerData) {
      this.selectedCustomers = [customerData];
      this.workOrderModalTitle = '创建工单';
      this.workOrderModalVisible = true;
      this.workOrderFormData = {
        ...initParams(this.workOrderFormConfig),
        customerName: customerData.customerName,
        phoneNumber: customerData.phoneNumber,
        customerId: customerData.customerId,
        exValues: [],
      };
      console.log('单个客户建单数据:', this.workOrderFormData);
      this.resetWorkOrderForm();
    },

    // 工单创建弹窗提交
    async handleWorkOrderSubmit() {
      try {
        // 验证表单
        const valid = await this.$refs.workOrderForm.validate();
        if (!valid) return;

        // 验证模板表单
        if (this.showMod && this.showTemp) {
          try {
            await this.$refs.templateForm.validate();
          } catch {
            this.$message.error('请完善模板字段');
            return;
          }
        }

        this.workOrderModalLoading = true;

        let result;
        if (this.selectedCustomers.length === 1) {
          // 单个工单创建
          result = await this.createSingleOrder(this.workOrderFormData, this.selectedCustomers[0]);
        } else {
          // 批量工单创建
          result = await this.createBatchOrders(this.workOrderFormData, this.selectedCustomers);
        }

        if (result.success) {
          this.workOrderModalVisible = false;
          this.resetWorkOrderForm();
          // 刷新列表
          this.loadData();
        }
      } catch (error) {
        console.error('工单创建失败:', error);
        this.$message.error('工单创建失败');
      } finally {
        this.workOrderModalLoading = false;
      }
    },

    // 工单创建弹窗取消
    handleWorkOrderCancel() {
      this.workOrderModalVisible = false;
      this.resetWorkOrderForm();
    },
  },
};
</script>

<style lang="less" scoped>
.button-right {
  margin-right: 16px;
}

.error-msg {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

#components-layout-demo-basic {
  text-align: center;
}
#components-layout-demo-basic .ant-layout-header,
#components-layout-demo-basic .ant-layout-footer {
  background: #fff;
  color: rgba(0, 0, 0, 0.65);
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  background-color: #fafafa;
}
#components-layout-demo-basic .ant-layout-footer {
  line-height: 1.5;
}
#components-layout-demo-basic .ant-layout-sider {
  background: #fff;
  color: rgba(0, 0, 0, 0.65);
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  padding: 8px;
}
#components-layout-demo-basic .ant-layout-content {
  background: #fff;
  color: rgba(0, 0, 0, 0.65);
  min-height: 120px;
  line-height: 80px;
}
#components-layout-demo-basic > .ant-layout {
  margin-bottom: 48px;
}
#components-layout-demo-basic > .ant-layout:last-child {
  margin: 0;
}
.header {
  display: flex;
  justify-content: flex-end;
}

//头像以及名字等
.a-box {
  margin-top: 32px;
  display: inline-block;
  position: relative;
  min-width: 250px;
  .name {
    font-size: 35px;
    margin-left: 16px;
  }
  .area {
    font-size: 20px;
    margin-left: 16px;
  }
  .tel {
    font-size: 18px;
    display: block;
    position: absolute;
    top: 40px;
    left: 112px;
    white-space: nowrap;
  }
}
//后续加需求的右侧刷新按钮
.refresh {
  position: absolute;
  right: 16px;
  display: inline-block;
}
//侧边标题
.sider-title {
  font-size: 16px;
  text-align: left;
  color: #333333;
  line-height: 32px;
  height: 32px;
}
//左侧盒子
.call-box {
  .call {
    height: 102px;
    border-radius: 8px;
    background-color: #fafafa;
    margin-top: 12px;
    margin-bottom: 12px;
    padding: 10px;
    font-size: 12px;
    cursor: pointer; //悬浮时变手指
    .line-a {
      display: flex;
      justify-content: space-between;
      line-height: 20px;
      height: 20px;
    }
  }
  // 左侧选中时候的表现
  .leftActive {
    border: 2px solid #1b58f4;
    transform: translateY(-2px);
    animation-duration: 0.5s;
  }
}
//关注按钮
.attention {
  display: inline-block;
  margin-right: 10px;
  color: #165dff;
  cursor: pointer;
  padding: 0;
  min-width: 38px;
  height: 30px;
  line-height: 30px;
}
//tab大盒子
.content-tab-box {
  margin-top: 48px;
}
.tab1-box {
  padding-left: 80px;
  padding-right: 80px;
  //访客详情大标题
  .line-b-title {
    border-left: 4px solid #1b58f4;
    padding-left: 10px; /* 可选，用于控制文字与边框的间距 */
    text-align: left;
    color: color rgb(51, 51, 51);
    font-weight: bold;
    margin-top: 16px;
  }
  //访客详情内容
  .line-b {
    height: 38px;
    line-height: 38px;
    text-align: left;
    font-size: 13px;
    color: rgb(170, 170, 170);
    span {
      color: rgb(51, 51, 51);
    }
  }
}
//紧急字体
.exgency {
  color: red;
}
//访客名称
.tag-place {
  display: flex;
  justify-content: flex-end;
  top: -36px;
  position: relative;
  .tag {
    position: absolute;
    height: 32px;
    line-height: 32px;
  }
}
.name-button {
  margin-right: 4px;
}
.guest-tag {
  //标签样式
  // display: flex;
  // justify-content: space-around;
  .tags {
    white-space: nowrap;
    line-height: 32px;
    height: 32px;
  }
}

.icon-plus {
  margin-right: 4px;
}
.button-flex {
  margin-top: 16px;
  display: flex;
  justify-content: space-around;
}

.templateFields {
  // /deep/ .ant-form-item label {
  //   white-space: break-spaces;
  //   text-align: justify;
  //   display: inline-flex;
  // }

  /deep/ .ant-form-item {
    margin-bottom: 4px;
    display: flex;
  }
  /deep/ .ant-form-item label {
    white-space: break-spaces;
    text-align: justify;
    display: inline-flex;
    font-size: 12px;
    color: #999;
  }
}

.replyMark {
  margin-bottom: 8px;
}

//左侧角标
.head-icon {
  position: absolute;
  border-radius: 2px;
  transform: translate(168px, -20px) scale(0.8);
  min-width: 36px;
}

// /deep/ .ant-form-item-label {
//   text-align: left;
//   width: fit-content;
//   display: inline-block;
// }
.templateFields /deep/ .ant-form-item label {
  display: inline-block;
  // white-space: nowrap;
}
/deep/ .ant-form-item-control {
  text-align: left;
}
.orderContent {
  padding-top: 24px;
  /deep/ .replyLine {
    margin-bottom: 4px;
  }
  /deep/ .quillWrapper .ql-toolbar.ql-snow .ql-formats {
    transform: scale(0.9);
    display: inline-flex;
    margin: 0 -1px;
  }
}

.visitorForm {
  /deep/ .ant-form-item-label > label {
    color: #999;
    font-size: 12px;
  }
}
</style>
