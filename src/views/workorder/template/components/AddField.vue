<template>
  <a-drawer
    :title="props.nowTitle"
    placement="right"
    :closable="false"
    :visible="visible"
    :width="640"
    closable
    @close="onClose"
  >
    <a-form-model
      layout="horizontal"
      v-bind="formItemLayout"
      :model="fieldForm"
      ref="form"
      :rules="rules"
    >
      <a-form-model-item
        label="字段名称"
        prop="fieldName"
      >
        <a-input
          v-model="fieldForm.fieldName"
          placeholder="请输入字段名称"
        />
      </a-form-model-item>
      <a-form-model-item
        label="字段类型"
        prop="fieldType"
      >
        <a-select
          :options="fieldType"
          v-model="fieldForm.fieldType"
          placeholder="请选择字段类型"
        />
      </a-form-model-item>
      <a-form-model-item
        label="提示文本"
        prop="fieldDesc"
      >
        <a-input
          v-model="fieldForm.fieldDesc"
          placeholder="请输入提示文本"
        />
      </a-form-model-item>
      <a-form-model-item
        v-if="fieldForm.fieldType === '2'"
        label="单选列表"
        prop="fieldDataKey"
      >
        <a-select
          :options="dictType"
          v-model="fieldForm.fieldDataKey"
          placeholder="请选择单选列表类型"
        />
      </a-form-model-item>
      <a-form-model-item
        v-if="fieldForm.fieldType === '3'"
        label="默认日期"
        prop="defaultValue"
      >
        <a-date-picker
          :getPopupContainer="(triggerNode) => triggerNode.parentNode"
          v-model="fieldForm.defaultValue"
          @change="selectDate"
        />
      </a-form-model-item>
      <a-form-model-item
        v-if="fieldForm.fieldType === '1'"
        label="单行文本"
        prop="defaultValue"
      >
        <a-input
          v-model="fieldForm.defaultValue"
          placeholder="请输入"
        />
      </a-form-model-item>
      <a-form-model-item
        v-if="fieldForm.fieldType === '4'"
        label="多行文本"
        prop="defaultValue"
      >
        <ProductEditor
          :value="fieldForm.defaultValue"
          :uploadVisible="false"
          @input="handleInput"
        />
      </a-form-model-item>
      <a-form-model-item
        v-if="fieldForm.fieldType === '5'"
        label="多选列表"
        prop="fieldDataKey"
      >
        <a-select
          :options="dictType"
          v-model="fieldForm.fieldDataKey"
          placeholder="请选择多选表列类型"
        />
      </a-form-model-item>
      <a-form-model-item
        v-if="fieldForm.fieldType === '6'"
        label="数字"
        prop="defaultValue"
      >
        <a-input-number
          v-model="fieldForm.defaultValue"
          placeholder="请输入"
        />
      </a-form-model-item>
      <a-form-model-item
        v-if="fieldForm.fieldType === '7'"
        label="百分比"
        prop="defaultValue"
      >
        <a-input-number
          v-model="fieldForm.defaultValue"
          placeholder="请输入"
          :formatter="(value) => `${value}%`"
          :parser="(value) => value.replace('%', '')"
        />
      </a-form-model-item>
      <a-form-model-item label="是否可空">
        <a-switch v-model="emptyFlag" />
      </a-form-model-item>
      <a-form-model-item :wrapper-col="{ span: 12, offset: 6 }">
        <a-button
          type="primary"
          @click="addFieldConfirm"
        >保存</a-button>
      </a-form-model-item>
    </a-form-model>
  </a-drawer>
</template>
<script setup>
import moment from 'moment';
import { onMounted, ref, watchEffect } from 'vue';
import { newAllDict } from '@/api/system/dict';
import { defaultDict } from '@/utils/system/common';
import ProductEditor from '@/components/Editor/ProductEditor.vue';
let props = defineProps({
  drawerShow: Boolean,
  nowAddFields: Array,
  nowEditField: Object,
  nowTitle: String,
});
let fieldType = ref();
let fieldForm = ref({});
let form = ref(); // 表单校验
let visible = ref(props.drawerShow);
let nowAddFields = ref(props.nowAddFields);
let nowEditField = ref(props.nowEditField);
let emptyFlag = ref(props.nowEditField.emptyFlag === '1');
let dictType = ref();
const emits = defineEmits(['drawerClose', 'addFieldFinish']);
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};
defaultDict('field_type').then((res) => {
  fieldType.value = res;
});
// 表单校验
let rules = {
  fieldName: [
    {
      validator: (rule, value, callback) => {
        if (nowAddFields.value.some((item) => item.fieldName === value)) {
          callback('请不要输入重复字段名！');
        } else {
          callback();
        }
      },
      trigger: 'blur',
    },
    { required: true, message: '请输入字段名称' },
  ],
  fieldType: [{ required: true, message: '请选择字段类型' }],
};
function onClose () {
  if (JSON.stringify(nowEditField.value) !== '{}' && props.nowTitle !== '添加字段') {
    addFieldConfirm();
  } else {
    visible.value = false;
    drawerClose();
  }
}
function addFieldConfirm () {
  form.value.validate((valid) => {
    if (valid) {
      fieldForm.value.emptyFlag = emptyFlag.value ? '1' : '0';
      addFieldFinish(fieldForm.value);
      fieldForm.value = {};
      visible.value = false;
      drawerClose();
    }
  });
}
function selectDate (value) {
  fieldForm.value.defaultValue = value ? moment(value).format('YYYY-MM-DD') : null;
}
function drawerClose () {
  emits('drawerClose', visible.value);
}
function addFieldFinish (value) {
  emits('addFieldFinish', value);
}
// 获取全部字典
async function getAllDict () {
  const [result] = await newAllDict();
  dictType.value = result.data.map((item) => {
    return {
      label: item.dictName,
      value: item.dictCode,
    };
  });
}
function handleInput (param) {
  fieldForm.value.defaultValue = param;
}
onMounted(() => {
  getAllDict();
});
watchEffect(() => {
  visible.value = props.drawerShow;
  nowAddFields.value = props.nowAddFields;
  nowEditField.value = props.nowEditField;
  fieldForm.value = nowEditField.value;
  emptyFlag.value = props.nowEditField.emptyFlag === '1';
});
</script>
<style scoped lang="less">
/deep/ .quillWrapper .ql-snow.ql-toolbar {
  display: flex;
}
/deep/ .quillWrapper .ql-toolbar.ql-snow .ql-formats {
  transform: scale(0.7);
  display: inline-flex;
  margin: 0 -6px;
}
</style>
