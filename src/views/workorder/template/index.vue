<template>
  <a-card style="margin: 16px 24px 60px">
    <a-row>
      <a-col
        :span="6"
        :offset="1"
      >
        <h2>工单模板</h2>
      </a-col>
    </a-row>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :modalConfig="modalConfig"
      :tableData="tableData"
      @modalCancel="modalCancel"
      @modalConfirm="modalConfirm"
      @handleCreate="rowAdd"
      @rowEdit="rowEdit"
      @loadData="loadData"
    >
      <template slot="addField">
        <div v-if="nowAddFields.length > 0">
          <div
            v-for="item in nowAddFields"
            :key="item.id"
          >
            <a-space>
              {{ item.fieldName }}
              <a @click="showDrawer(item)"><a-icon type="edit" /></a>
              <a @click="deleteField(item)"><a-icon type="delete" /></a>
            </a-space>
          </div>
        </div>
        <div>
          <a @click="showDrawer(null)"><a-icon type="plus" />添加自定义字段</a>
        </div>
      </template>
      <template
        slot="enableSwitch"
        slot-scope="{ params }"
      >
        <a-switch
          :defaultChecked="params.enableFlag === '1'"
          @change="switchEnable(params)"
          class="mySwitch"
        >
          <span
            slot="checkedChildren"
            type="check"
          >开启</span>
          <span
            slot="unCheckedChildren"
            type="close"
          >关闭</span>
        </a-switch>
      </template>
      <AddField
        :drawerShow="drawerShow"
        :nowTitle="nowTitle"
        :nowAddFields="nowAddFields"
        :nowEditField="nowEditField"
        @drawerClose="drawerClose"
        @addFieldFinish="addFieldFinish"
      />
    </BuseCrud>
  </a-card>
</template>
<script setup>
import { computed, onMounted, ref } from 'vue';
import AddField from './components/AddField.vue';
import { addOrderTemplate, getOrderTemplate } from '@/api/system/settings';
import store from '@/store';
import { defaultMsg, isHasPermi } from '@/utils/system/common/index';
let loading = ref(true);
let tableData = ref();
let crud = ref();
let msg;
let drawerShow = ref(false);
let nowAddFields = ref([]);
let nowEditField = ref({});
let nowTitle = ref();
let orderSort = ref();
let tablePage = ref({ total: 0, currentPage: 1, pageSize: 10 });
const tableColumn = ref([
  {
    field: 'templateName',
    title: '模板名称',
  },
  {
    field: 'templateFields',
    title: '模板字段',
    formatter: ({ cellValue }) => {
      if (cellValue && cellValue.length > 0) {
        const res = cellValue.map((item) => {
          return item.fieldName;
        });
        return res.join('/');
      }
    },
    showOverflow: 'ellipsis',
  },
  {
    field: 'updateTime',
    title: '最近编辑时间',
  },
  {
    field: 'updateByName',
    title: '最近编辑人',
  },
]);

const modalConfig = computed(() => {
  return {
    viewBtn: false,
    submitBtn: false,
    delBtn: false,
    addBtn: isHasPermi(['system:setting:orderTempAdd']),
    addTitle: '新建工单模板',
    editTitle: '修改工单模板',
    formConfig: [
      {
        field: 'templateName',
        title: '模板名称',
        rules: [{ required: true, message: '请输入模板名称' }],
      },
      {
        field: 'enableFlag',
        title: '是否启用',
        element: 'slot',
        slotName: 'enableSwitch',
      },
      {
        field: 'fieldRequests',
        title: '模板字段',
        element: 'slot',
        slotName: 'addField',
        rules: [{ required: true, message: '请添加模板字段' }],
      },
      {
        field: 'categoryList',
        title: '工单分类',
        element: 'a-tree-select',
        props: {
          multiple: true,
          showSearch: true,
          treeData: orderSort.value,
          treeNodeFilterProp: 'title',
          replaceFields: {
            title: 'categoryName',
            value: 'categoryId',
          },
          dropdownStyle: { maxHeight: '400px', overflow: 'auto' },
          getPopupContainer: (triggerNode) => triggerNode.parentNode,
        },
      },
    ],
  };
});
// 数据加载
async function loadData () {
  const finalParam = {
    pageNum: tablePage.value.currentPage,
    pageSize: tablePage.value.pageSize,
  };
  const [result] = await getOrderTemplate(finalParam);
  tableData.value = result.data;
  tablePage.value.total = result.count;
  loading.value = false;
}
function rowEdit (row) {
  msg = '更新';
  nowAddFields.value = row.templateFields;
  crud.value.switchModalView(true, 'UPDATE', {
    ...row,
    fieldRequests: nowAddFields.value,
    categoryList: row.categoryIdList,
  });
}
function modalCancel () {
  nowAddFields.value = [];
  loadData();
}
function switchEnable (data) {
  crud.value.setFormFields({ enableFlag: data.enableFlag === '1' ? false : true });
}
// 确认添加模板
async function modalConfirm (params) {
  params.fieldRequests = nowAddFields.value;
  params.enableFlag = params.enableFlag ? '1' : '0';
  params.templateId = params.orderTemplateId;
  delete params.orderTemplateId;
  const [result] = await addOrderTemplate(params);
  defaultMsg(result, msg);
  loadData();
}
function rowAdd () {
  msg = '添加';
  nowAddFields.value = [];
  crud.value.switchModalView(true, 'ADD');
}
// 添加字段弹窗
function showDrawer (item) {
  if (item) {
    nowEditField.value = item;
    nowTitle.value = '编辑字段';
    deleteField(item);
  } else {
    nowTitle.value = '添加字段';
  }
  drawerShow.value = true;
}
function drawerClose () {
  drawerShow.value = false;
}
// 自定义字段添加完成赋值给params
function addFieldFinish (value) {
  nowAddFields.value.push({ ...value });
  crud.value.setFormFields({ fieldRequests: nowAddFields.value });
  nowEditField.value = {};
}
// 删除自定义字段
function deleteField (item) {
  const delIndex = nowAddFields.value.indexOf(item);
  nowAddFields.value.splice(delIndex, 1);
}
onMounted(() => {
  loadData();
  store.dispatch('base/GetOrderSort').then((result) => {
    orderSort.value = result;
  });
});
</script>
<style lang="less" scoped>
.mySwitch {
  width: 64px;
  margin-right: 8px;
  span {
    color: white;
    margin: unset;
  }
}
</style>
