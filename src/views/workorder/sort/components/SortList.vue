<template>
  <div>
    <a-list class="sortList">
      <div slot="header">
        {{ props.listTitle }}
        <a v-hasPermi="['system:setting:addCategory']" @click="showAddModal(listLevel)">
          <a-tooltip>
            <template slot="title">添加同级分类</template>
            <a-icon type="plus" />
          </a-tooltip>
        </a>
      </div>
      <TransitionGroup name="list" tag="div" class="container">
        <div
          v-for="(item, index) in listData"
          :key="item.categoryId"
          draggable="true"
          @dragstart="dragstart($event, index)"
          @dragenter="dragenter($event, index)"
          @dragend="dragend"
          @dragover="dragover"
          :class="['sortItem', isActive === item.categoryId ? 'active' : '']"
        >
          <a-row>
            <a-col :span="8">
              <a @click="chooseSort(item)">
                {{ item.categoryName }}
              </a>
            </a-col>
            <a-col :span="12">
              <a-tooltip>
                <template slot="title">当前受理组&人员</template>
                {{ item.handleGroupName }}
                {{ item.handleUserName }}
              </a-tooltip>
            </a-col>
            <a-col :span="4">
              <a-space>
                <a v-hasPermi="['system:setting:addCategory']" @click="showAddModal(listLevel, item)">
                  <a-tooltip>
                    <template slot="title">为该分类绑定受理人员</template>
                    <a-icon type="user" />
                  </a-tooltip>
                </a>
                <a v-hasPermi="['system:setting:addCategory']" @click="showDeleteModal(listLevel, item)">
                  <a-tooltip>
                    <template slot="title">删除</template>
                    <a-icon type="minus" style="color: red" />
                  </a-tooltip>
                </a>

                <!-- 删除分类 -->
                <!-- <a-popconfirm
                  title="确认删除该分类吗？"
                  ok-text="是"
                  cancel-text="否"
                  @confirm="deleteConfirm(item.categoryId)"
                >
                  <a>
                    <a-tooltip placement="bottom">
                      <template slot="title">删除该分类</template>
                      <a-icon type="delete" />
                    </a-tooltip>
                  </a>
                </a-popconfirm> -->
              </a-space>
            </a-col>
          </a-row>
        </div>
      </TransitionGroup>
    </a-list>
  </div>
</template>

<script setup>
import { ref, watchEffect } from 'vue';
let props = defineProps({
  listTitle: String,
  listData: Array,
  listLevel: String,
});
let listData = ref(props.listData);
let isActive = ref();
let nowSort = ref([]);
// 为父组件传递分类id和层级
const emits = defineEmits(['showNextLevel', 'showAddModal', 'saveLevelSort', 'showDeleteModal']);
let dragIndex = 0;
function dragstart(e, index) {
  e.stopPropagation();
  dragIndex = index;
  e.target.classList.add('moveing');
}
function dragenter(e, index) {
  e.preventDefault();
  if (dragIndex !== index) {
    let newlist = listData.value;
    const source = newlist[dragIndex];
    newlist.splice(dragIndex, 1);
    newlist.splice(index, 0, source);
    dragIndex = index;
    // 修改sort重新排序
    nowSort.value = newlist.map((item, index) => ({
      ...item,
      sort: index + 1,
    }));
    emits('saveLevelSort', nowSort.value, props.listLevel);
  }
}
function dragover(e) {
  e.preventDefault();
  e.dataTransfer.dropEffect = 'move';
}
function dragend(e) {
  e.target.classList.remove('moveing');
}
function showDeleteModal(listLevel, item) {
  emits('showDeleteModal', listLevel, item);
}
// 弹框配置 根据listlevel判断能否添加分类
function showAddModal(listLevel, item) {
  emits('showAddModal', listLevel, item);
}
function showNextLevel(categoryId, listLevel) {
  emits('showNextLevel', categoryId, listLevel);
}
function chooseSort(item) {
  isActive.value = item.categoryId;
  showNextLevel(item.categoryId, props.listLevel);
}
// 确认删除
// function deleteConfirm(categoryId) {
//   console.log(categoryId);
// }
watchEffect(() => {
  if (props.listData) {
    listData.value = props.listData;
    listData.value = listData.value.sort((a, b) => (a.sort > b.sort ? 1 : -1));
  }
});
</script>
<style lang="less" scoped>
.sortList {
  border: 1px solid rgb(154, 154, 215);
  border-radius: 5px;
  height: 480px;
  margin: 0 16px;
  padding: 16px;
  overflow-y: scroll;
  .sortItem {
    margin: 8px 0;
    padding: 8px;
    border-bottom: 1px solid #e8e8e8;
  }
  .active {
    background-color: #edeef3;
  }
  .container {
    position: relative;
    padding: 0;
    .moving {
      box-shadow: 0px 4px 24px #c3ccd0;
    }
  }
}
</style>
