<template>
  <a-card style="margin: 16px 24px 60px">
    <a-row>
      <a-col :span="6" :offset="1">
        <h2>工单分类</h2>
      </a-col>
    </a-row>
    <a-row type="flex" justify="space-around">
      <a-col :span="7">
        <SortList
          listTitle="一级分类"
          :listData="firstSort"
          listLevel="1"
          @showNextLevel="showNextLevel"
          @showAddModal="showAddModal"
          @saveLevelSort="saveLevelSort"
          @showDeleteModal="showDeleteModal"
        />
      </a-col>
      <a-col :span="7">
        <SortList
          listTitle="二级分类"
          :listData="secondSort"
          listLevel="2"
          @showNextLevel="showNextLevel"
          @showAddModal="showAddModal"
          @saveLevelSort="saveLevelSort"
          @showDeleteModal="showDeleteModal"
        />
      </a-col>
      <a-col :span="7">
        <SortList
          listTitle="三级分类"
          :listData="thirdSort"
          listLevel="3"
          @showNextLevel="showNextLevel"
          @showAddModal="showAddModal"
          @saveLevelSort="saveLevelSort"
          @showDeleteModal="showDeleteModal"
        />
      </a-col>
    </a-row>
    <a-modal v-model="addSortModal" :title="categoryTitle" @cancel="handleCancel" width="50%">
      <a-form-model layout="horizontal" v-bind="formItemLayout" :model="addSortForm" ref="form" :rules="rules">
        <a-form-model-item label="分类名称" prop="categoryName" required>
          <a-input v-model="addSortForm.categoryName" placeholder="请输入分类名称" />
        </a-form-model-item>
        <a-form-model-item label="建单时是否显示该分类" prop="displayFlag">
          <a-radio-group v-model="addSortForm.displayFlag">
            <a-radio value="1"> 显示 </a-radio>
            <a-radio value="0"> 不显示 </a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="是否为销售工单" prop="saleOrderFlag">
          <a-radio-group v-model="addSortForm.saleOrderFlag">
            <a-radio value="1"> 是 </a-radio>
            <a-radio value="0"> 否 </a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="受理人员">
          <a-tree-select
            placeholder="请选择受理人员"
            v-model="addSortForm.handleUser"
            :treeData="userList"
            showSearch
            labelInValue
            allowClear
            :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
          >
            <template slot="myTitle" slot-scope="item">
              <StatuNode :newNodes="item" />
            </template>
          </a-tree-select>
        </a-form-model-item>
        <a-form-model-item label="绑定场站">
          <a-switch v-model="addSortForm.bindStation" :defaultChecked="addSortForm.bindStation" />
        </a-form-model-item>
        <a-form-model-item label="是否必填场站" v-show="addSortForm.bindStation">
          <a-switch v-model="addSortForm.stationEmptyFlag" :defaultChecked="addSortForm.stationEmptyFlag" />
        </a-form-model-item>
        <a-form-model-item label="绑定充电信息">
          <a-switch v-model="addSortForm.bindChargeInfo" :defaultChecked="addSortForm.bindChargeInfo" />
        </a-form-model-item>
        <a-form-model-item label="是否必填充电信息" v-show="addSortForm.bindChargeInfo">
          <a-switch v-model="addSortForm.chargeInfoEmptyFlag" :defaultChecked="addSortForm.chargeInfoEmptyFlag" />
        </a-form-model-item>
      </a-form-model>
      <template slot="footer">
        <a-button type="primary" @click="addSortConfirm">保存</a-button>
      </template>
    </a-modal>
  </a-card>
</template>
<script setup>
import { onMounted, ref } from 'vue';
import SortList from './components/SortList.vue';
import { message, Modal } from 'ant-design-vue';
import { saveOrderSort, deleteOrderSort } from '@/api/system/settings';
import { cutString, defaultMsg, setCustomSearchTree } from '@/utils/system/common';
import StatuNode from '@/components/TreeNode/StatuNode.vue';
import { getTypeList } from '@/api/system/workorder';
import { getFatherBySon } from '@/utils/system/common/index';
import store from '@/store';
import { throttle } from 'xe-utils';
let firstSort = ref();
let secondSort = ref();
let thirdSort = ref();
let firstParentId; // 第一级parentId
let secondParentId; // 第二级parentId
let activeLevel; // 上一次展开的层级
let nowLevel; // 点击弹框的层级
let nowCategory; // 选中的编辑分类
let categoryTitle = ref('新建分类');
let addSortModal = ref(false);
let addSortForm = ref({});
let form = ref();
let userList = ref([]);
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};
const rules = {
  categoryName: [{ required: true, message: '请输入分类名称' }],
};
async function loadData() {
  const [result] = await getTypeList();
  firstSort.value = result.data;
  if (firstParentId) {
    const result = firstSort.value.filter((item) => {
      return item.categoryId === firstParentId;
    });
    secondSort.value = result[0]?.children || [];
    if (secondParentId) {
      const result = secondSort.value.filter((item) => {
        return item.categoryId === secondParentId;
      });
      thirdSort.value = result[0]?.children || [];
    }
  }
}
// 展示选中层级的下级数据
function showNextLevel(categoryId, listLevel) {
  activeLevel = listLevel;
  switch (listLevel) {
    case '1': {
      // 切换之前保存上一个二级分类
      if (secondSort.value && secondSort.value?.length !== 0) {
        saveNowSort(secondSort.value);
      }
      // 根据点击id从一级分类中过滤出二级分类
      const result = firstSort.value.filter((item) => {
        return item.categoryId === categoryId;
      });
      firstParentId = categoryId;
      secondSort.value = result[0].children;
      thirdSort.value = [];
      break;
    }
    case '2': {
      if (thirdSort.value && thirdSort.value.length !== 0) {
        saveNowSort(thirdSort.value);
      }
      const result = secondSort.value.filter((item) => {
        return item.categoryId === categoryId;
      });
      secondParentId = categoryId;
      thirdSort.value = result[0].children;
      break;
    }
  }
}

function getCategoryIds(data) {
  const categoryIds = [data.categoryId]; // 存储当前节点的 categoryId

  if (data.children && data.children.length > 0) {
    // 遍历子节点并递归获取它们的 categoryId
    for (const child of data.children) {
      const childCategoryIds = getCategoryIds(child); // 递归获取子节点的 categoryId
      categoryIds.push(...childCategoryIds); // 将子节点的 categoryId 添加到结果数组中
    }
  }

  return categoryIds;
}
function showDeleteModal(clickLevel, item) {
  const categoryIds = getCategoryIds(item);
  console.log('showDeleteModal', categoryIds);
  Modal.confirm({
    title: '确定删除该工单分类吗？',
    content: '删除后子集分类也将一起删除哦，请知悉！',
    okText: '确定',
    cancelText: '取消',
    async onOk() {
      const [res] = await deleteOrderSort({ categoryIds: categoryIds });
      if (res?.code == '10000') {
        message.success('删除成功');
        loadData();
      }
    },
  });
}
// 通过与已选层级比较是否弹出弹框
function showAddModal(clickLevel, item) {
  store.dispatch('base/GetUserList', { nickName: '' }).then((result) => {
    userList.value = setCustomSearchTree(result);
  });
  nowLevel = clickLevel;
  if (clickLevel - activeLevel === 1 || clickLevel == 1 || clickLevel == activeLevel) {
    if (item) {
      categoryTitle.value = '编辑分类';
      addSortForm.value = {
        ...item,
        handleUser: item.handleUser ? { value: item.handleUser + '_' + item.handleUserName } : undefined,
        bindStation: item.bindStation === '1',
        stationEmptyFlag: item.stationEmptyFlag === '1',
        bindChargeInfo: item.bindChargeInfo === '1',
        chargeInfoEmptyFlag: item.chargeInfoEmptyFlag === '1',
      };
      nowCategory = item;
    } else {
      categoryTitle.value = '新建分类';
      nowCategory = {};
      addSortForm.value = {
        displayFlag: '1',
        saleOrderFlag: '0',
      };
    }
    addSortModal.value = true;
  } else {
    message.info('请先选择上级分类');
  }
}
// 确认添加分类
function addSortConfirm() {
  form.value.validate(async (valid) => {
    if (valid) {
      let nowParentId;
      let nowLevelLength = firstSort.value.length; // 该类所在层级长度
      if (nowLevel === '2') {
        nowParentId = firstParentId;
        nowLevelLength = secondSort.value?.length;
      } else if (nowLevel === '3') {
        nowParentId = secondParentId;
        nowLevelLength = thirdSort.value?.length;
      }
      let finalParam = {
        categoryId: nowCategory.categoryId, // 编辑时必传
        categoryName: addSortForm.value.categoryName,
        displayFlag: addSortForm.value.displayFlag,
        saleOrderFlag: addSortForm.value.saleOrderFlag,
        categoryLevel: nowLevel,
        parentId: nowParentId, // 子层级必传
        bindStation: addSortForm.value.bindStation ? '1' : '0',
        stationEmptyFlag: addSortForm.value.stationEmptyFlag ? '1' : '0',
        bindChargeInfo: addSortForm.value.bindChargeInfo ? '1' : '0',
        chargeInfoEmptyFlag: addSortForm.value.chargeInfoEmptyFlag ? '1' : '0',
        sort: nowCategory ? addSortForm.value.sort : nowLevelLength + 1,
      };
      const handleUser = addSortForm.value.handleUser;
      if (handleUser && handleUser.value) {
        const userName = getLabelById(userList.value, addSortForm.value.handleUser.value);
        const groupName = getFatherBySon(userName.value, userList.value);
        const handler = cutString(userName.value, '_');
        const handleGroup = cutString(groupName[0].value, '_');
        finalParam.handleGroup = handleGroup;
        finalParam.handleGroupName = groupName[0].valueName;
        finalParam.handleUser = handler;
        finalParam.handleUserName = userName.valueName;
      } else {
        finalParam = {
          ...finalParam,
          handleGroup: '',
          handleGroupName: '',
          handleUser: '',
          handleUserName: '',
        };
      }
      const [result] = await saveOrderSort([finalParam]);
      defaultMsg(result, nowCategory ? '保存' : '添加');
      handleCancel();
      loadData();
    }
  });
}

function getLabelById(list, target) {
  for (let i = 0; i < list.length; i++) {
    if (list[i].value === target) {
      return list[i];
    }
    if (list[i].children && list[i].children.length > 0) {
      const result = getLabelById(list[i].children, target);
      if (result) return result;
    }
  }
}

// 关闭弹窗清除数据
function handleCancel() {
  addSortForm.value = {};
  addSortModal.value = false;
}
// 保存当前排序
async function saveNowSort(nowSortList) {
  await saveOrderSort(nowSortList);
  // 保存完毕需重新获取全部分类，不然排序展示效果不变
  loadData();
}
// 拖动排序后保存当前层级的排序
function saveLevelSort(list, level) {
  throttle(saveNowSort(list), 200);
  switch (level) {
    case '1': {
      firstSort.value = list;
      break;
    }
    case '2': {
      secondSort.value = list;
      break;
    }
    case '3': {
      thirdSort.value = list;
      break;
    }
  }
}
onMounted(() => {
  loadData();
});
</script>
