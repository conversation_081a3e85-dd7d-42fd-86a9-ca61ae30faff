<template>
  <div>
    <!-- <a-button @click="handleBack" style="margin: 0 24px">返回</a-button> -->
    <a-card style="margin: 16px 24px 60px">
      <a-spin :spinning="spin">
        <WorkFlowReply :workDetail="workDetail" :addFields="addFields" @submitFinish="submitFinish" />
        <FlowReplyRecord :repliesData="repliesData" :flowRecord="flowRecord" />
      </a-spin>
    </a-card>
  </div>
</template>

<script setup>
import FlowReplyRecord from './components/FlowReplyRecord.vue';
import WorkFlowReply from './components/WorkFlowReply.vue';
import { useRoute } from 'vue-router/composables';
import { getOrderDetail } from '@/api/system/workorder';
import { onMounted, ref } from 'vue';
// import { useRouter } from 'vue-router/composables';

// const goRoute = useRouter();

let toRoute = useRoute();
let workDetail = ref({});
let repliesData = ref([]);
let flowRecord = ref([]);
let addFields = ref([]);
let spin = ref(true);
const props = defineProps({
  orderId: String,
});
async function initData() {
  console.log(props.orderId);
  let orderId = '';
  if (props.orderId) {
    orderId = props.orderId;
  } else {
    orderId = toRoute.query.orderId;
  }
  const [result] = await getOrderDetail({ orderId: orderId });
  workDetail.value = result.data.detail;
  repliesData.value = result.data.reply;
  flowRecord.value = result.data.flow;
  try {
    addFields.value = JSON.parse(result.data.detail.extend);
  } catch (error) {
    console.log('无效');
  }
  spin.value = false;
}
// 提交完成刷新页面
function submitFinish() {
  initData();
}
// function handleBack() {
//   goRoute.back();
// }
onMounted(() => {
  initData();
});
</script>
