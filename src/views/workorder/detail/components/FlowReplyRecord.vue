<template>
  <a-row>
    <a-col :span="20" :offset="1">
      <a-tabs default-active-key="replyRecord">
        <a-tab-pane key="replyRecord" tab="全部沟通记录">
          <a-timeline class="time-line">
            <a-timeline-item v-for="item in repliesData" :key="item.id">
              <a-card :bordered="false">
                <a-row style="margin-bottom: 16px">
                  <a-col :span="5">
                    <span class="timeline-title">{{ item.operatorName }}</span>
                  </a-col>
                  <a-col :span="5">
                    <span>操作人：{{ item.createByName }}</span>
                  </a-col>
                  <a-col :span="5" :offset="9">
                    <span>{{ item.createTime }}</span>
                  </a-col>
                </a-row>
                <a-row>
                  <template v-if="item.operatorName === '处理工单'">
                    <span style="display: block; margin-top: 10px">
                      {{ (item.replyContent && item.replyContent.split('&'))[0] }}
                    </span>
                    <div style="display: block; margin-top: 10px" v-if="showDocList(item.replyContent)">
                      <a-row type="flex" style="flex-wrap: wrap">
                        <div
                          v-for="(o, index) in item.replyContent.split('&')[1].split('|')"
                          :key="index"
                          class="file-item"
                        >
                          <img
                            style="width: 150px; height: 150px; margin-right: 10px"
                            :src="o"
                            alt="加载失败"
                            class="avatar"
                            v-if="
                              o.toLowerCase().indexOf('.jpg') > 0 ||
                              o.toLowerCase().indexOf('.jpeg') > 0 ||
                              o.toLowerCase().indexOf('.png') != -1
                            "
                            @click.stop="clickImg(o)"
                          />
                          <video
                            style="width: 150px; height: 150px; margin-right: 10px"
                            v-if="o.toLowerCase().indexOf('.mp4') > 0"
                            :src="o"
                            controls
                          ></video>
                        </div>
                      </a-row>
                    </div>
                  </template>
                  <span v-else v-html="item.replyContent"></span>
                </a-row>
              </a-card>
            </a-timeline-item>
          </a-timeline>
          <!-- <a-list class="comment-list" item-layout="horizontal">
            <a-list-item v-for="item in repliesData" :key="item.id">
              <a-comment :author="item.createByName" :avatar="item.avatar ? item.avatar : null">
                <p slot="content" class="replyContent" v-html="item.replyContent" @click="clickRecord"></p>
                <ShowUploads :attachment-list="transformAttach(item.attachment)" />
                <a-tooltip slot="datetime">
                  <a-space>
                    <span>{{ item.createTime }}</span>
                  </a-space>
                </a-tooltip>
              </a-comment>
            </a-list-item>
          </a-list> -->
        </a-tab-pane>
        <a-tab-pane key="flowRecord" tab="全部流转记录">
          <a-timeline class="time-line">
            <a-timeline-item v-for="item in flowRecord" :key="item.id">
              <a-card :bordered="false">
                <a-row style="margin-bottom: 16px">
                  <a-col :span="5">
                    <span class="timeline-title">{{ item.operationTypeName }}</span>
                  </a-col>
                  <a-col :span="5">
                    <span>操作人：{{ item.operator }}</span>
                  </a-col>
                  <a-col :span="5" :offset="9">
                    <span>{{ item.operatorTime }}</span>
                  </a-col>
                </a-row>
                <a-row>
                  <template v-if="item.operationTypeName === '处理工单'">
                    <span style="display: block; margin-top: 10px">{{
                      (item.remark && item.remark.split('&'))[0]
                    }}</span>
                    <div style="display: block; margin-top: 10px" v-if="showDocList(item.remark)">
                      <a-row type="flex" style="flex-wrap: wrap">
                        <div v-for="(o, index) in item.remark.split('&')[1].split('|')" :key="index" class="file-item">
                          <img
                            style="width: 150px; height: 150px; margin-right: 10px"
                            :src="o"
                            alt="加载失败"
                            class="avatar"
                            v-if="
                              o.toLowerCase().indexOf('.jpg') > 0 ||
                              o.toLowerCase().indexOf('.jpeg') > 0 ||
                              o.toLowerCase().indexOf('.png') != -1
                            "
                            @click.stop="clickImg(o)"
                          />
                          <video
                            style="width: 150px; height: 150px; margin-right: 10px"
                            v-if="o.toLowerCase().indexOf('.mp4') > 0"
                            :src="o"
                            controls
                          ></video>
                        </div>
                      </a-row>
                    </div>
                  </template>
                  <span v-else>{{ item.remark }}</span>
                </a-row>
              </a-card>
              <!-- <a-card :bordered="false">
                <a-row>
                  <a-space>
                    <span>操作人：{{ item.groupName }} - {{ item.operator }}</span>
                    <span>操作：{{ item.operatorType }}</span>
                  </a-space>
                </a-row>
                <a-row v-if="!item.operatorType.includes('编辑')">
                  <a-space>
                    <span>状态变更：</span>
                    <span v-if="!item.orderStatus">未变更，当前状态：</span>
                    <span>{{ item.orderStatus }}</span>
                  </a-space>
                </a-row>
                <a-row v-if="!item.operatorType.includes('编辑') && (item.currentHandleUser || item.nextHandleUser)">
                  <a-space>
                    <span>分配处理人：</span>
                    <span v-if="!item.currentHandleUser && item.nextHandleUser">
                      {{ item.nextHandleUser }}
                    </span>
                    <span v-if="item.currentHandleUser && !item.nextHandleUser">
                      未分配，当前处理人：{{ item.currentHandleUser }}
                    </span>
                    <span v-if="item.nextHandleUser && item.currentHandleUser">
                      {{ item.currentHandleUser }} -> {{ item.nextHandleUser }}
                    </span>
                  </a-space>
                </a-row>
                <a-row>
                  <span>操作时间：{{ item.operatorTime }}</span>
                </a-row>
              </a-card> -->
            </a-timeline-item>
          </a-timeline>
        </a-tab-pane>
      </a-tabs>
    </a-col>
    <ImgViewer :visible="show" :imgView="imgView" @cancelModal="cancelModal" />
  </a-row>
</template>
<script setup>
import ShowUploads from '@/components/UploadImg/ShowUploads.vue';
import { getFile } from '@/api/common';
import ImgViewer from '@/components/ImgModal/ImgViewer';
import { downLoadXlsx } from '@/utils/system/common';
import { ref, watchEffect } from 'vue';
const props = defineProps({
  repliesData: {
    type: Array,
    default: () => [],
  },
  flowRecord: {
    type: Array,
    default: () => [],
  },
});
let repliesData = ref(props.repliesData);
let flowRecord = ref(props.flowRecord);
let show = ref();
let imgView = ref();
function transformAttach(fileList) {
  return fileList ? JSON.parse(fileList) : [];
}
async function clickRecord(e) {
  // 图片预览
  if (e.target.src) {
    imgView.value = e.target.src;
    show.value = true;
  }
  // 附件下载
  if (e.target.href) {
    e.preventDefault();
    const a = e.target.outerHTML;
    const reg = /(?<=<a.*href=")([^"]*)(?=")/g;
    const url = a.match(reg)[0];
    const fileType = url.slice(url.lastIndexOf('.') + 1); // 获取文件类型
    const name = '附件.' + fileType;
    const [result] = await getFile(url);
    downLoadXlsx(result, name);
  }
}
function cancelModal() {
  show.value = false;
  imgView.value = '';
}
function showDocList(remark) {
  const arr = remark.split('&');
  return arr && arr[1];
}
function clickImg(o) {
  //点击预览图片
  imgView.value = o;
  show.value = true;
}
watchEffect(() => {
  repliesData.value = props.repliesData;
  flowRecord.value = props.flowRecord;
  // flowRecord.value = flowRecord.value.sort((oldFlow, newFlow) =>
  //   oldFlow.operatorTime > newFlow.operatorTime ? 1 : -1
  // );
});
</script>
<style scoped lang="less">
.replyContent {
  margin: 16px 0 0 16px;
  font-size: 13px;
  /deep/ img {
    max-width: 20%;
  }
}
/deep/ .ant-comment-inner {
  padding: 8px 0 0 0;
}
/deep/ .ant-comment-nested {
  margin: 0 0 8px 24px;
}
/deep/ .ant-card-body {
  padding: 4px 16px;
}
/deep/ .ant-list-item {
  padding: unset;
}
.time-line {
  /deep/ .ant-row {
    font-size: 13px;
    color: #999;
    .timeline-title {
      font-size: 15px;
    }
  }
}
</style>
