<template>
  <a-card style="margin: 16px 24px 60px">
    <a-row>
      <a-col :span="6" :offset="1">
        <h2>SLA服务目标配置</h2>
      </a-col>
    </a-row>
    <a-form-model :model="slaForm" layout="horizontal" v-bind="formItemLayout" ref="form" :rules="rules">
      <a-form-model-item label="服务目标名称" prop="slaName" required>
        <a-input v-model="slaForm.slaName" style="width: 256px" placeholder="请输入服务目标名称" />
      </a-form-model-item>
      <a-form-model-item label="服务质量">
        <a-row>
          <a-col :span="10" :offset="2">首次相应时间</a-col>
          <a-col :span="10">完结时间</a-col>
        </a-row>
        <a-row>
          <a-col :span="10">
            <span class="slaSort">非常紧急：</span>
            <a-input-number v-model="slaForm.list[0].responseLimitHour" /><span class="spanGap">小时</span>
            <a-input-number v-model="slaForm.list[0].responseLimitMin" /><span class="spanGap">分钟</span>
          </a-col>
          <a-col :span="10">
            <span class="slaSort">非常紧急：</span>
            <a-input-number v-model="slaForm.list[0].finishLimitHour" /><span class="spanGap">小时</span>
            <a-input-number v-model="slaForm.list[0].finishLimitMin" /><span class="spanGap">分钟</span>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="10">
            <span class="slaSort">紧急：</span>
            <a-input-number v-model="slaForm.list[1].responseLimitHour" /><span class="spanGap">小时</span>
            <a-input-number v-model="slaForm.list[1].responseLimitMin" /><span class="spanGap">分钟</span>
          </a-col>
          <a-col :span="10">
            <span class="slaSort">紧急：</span>
            <a-input-number v-model="slaForm.list[1].finishLimitHour" /><span class="spanGap">小时</span>
            <a-input-number v-model="slaForm.list[1].finishLimitMin" /><span class="spanGap">分钟</span>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="10">
            <span class="slaSort">一般：</span>
            <a-input-number /><span class="spanGap">小时</span><a-input-number /><span class="spanGap">分钟</span>
          </a-col>
          <a-col :span="10">
            <span class="slaSort">一般：</span>
            <a-input-number /><span class="spanGap">小时</span><a-input-number /><span class="spanGap">分钟</span>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="10">
            <span class="slaSort">低：</span>
            <a-input-number /><span class="spanGap">小时</span><a-input-number /><span class="spanGap">分钟</span>
          </a-col>
          <a-col :span="10">
            <span class="slaSort">低：</span>
            <a-input-number /><span class="spanGap">小时</span><a-input-number /><span class="spanGap">分钟</span>
          </a-col>
        </a-row>
      </a-form-model-item>
      <a-form-model-item label="无受理人处理方式" required prop="noHandlerType">
        <a-radio-group v-model="slaForm.noHandlerType" :options="noAcceptorOptions" />
      </a-form-model-item>
      <!-- 二期处理 -->
      <!-- <a-form-model-item label="服务方式" required>
        <a-radio-group v-model="slaForm.serviceType" :options="serviceOptions" />
      </a-form-model-item> -->
      <a-form-model-item label="是否启用">
        <a-switch v-model="slaForm.enabled" :defaultChecked="slaForm.enabled === '1'" />
      </a-form-model-item>
      <a-form-model-item :wrapper-col="{ span: 4, offset: 4 }">
        <a-button v-hasPermi="['system:setting:saveSla']" type="primary" @click="submitSla(slaForm)">保存</a-button>
      </a-form-model-item>
    </a-form-model>
  </a-card>
</template>
<script setup>
import { onMounted, ref } from 'vue';
import { updateSla, getSlaDetail } from '@/api/system/settings';
import { defaultDict, defaultMsg } from '@/utils/system/common';
let slaForm = ref({
  serviceId: undefined,
  slaName: undefined,
  list: [
    {
      urgency: 1,
      slaDetailId: undefined,
      responseLimitHour: undefined,
      responseLimitMin: undefined,
      finishLimitHour: undefined,
      finishLimitMin: undefined,
    },
    {
      urgency: 2,
      slaDetailId: undefined,
      responseLimitHour: undefined,
      responseLimitMin: undefined,
      finishLimitHour: undefined,
      finishLimitMin: undefined,
    },
  ],
  nohandleUserNameType: undefined,
  enabled: false,
});
let noAcceptorOptions = ref();
let form = ref();
const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 18 },
};
// 表单校验
const rules = {
  slaName: [{ required: true, message: '请输入服务名称' }],
  noHandlerType: [{ required: true, message: '请选择无受理人处理方式' }],
};
async function submitSla() {
  form.value.validate(async (valid) => {
    if (valid) {
      const finalParam = {
        ...slaForm.value,
        list: [
          {
            urgency: 1,
            ...slaForm.value.list[0],
          },
          {
            urgency: 2,
            ...slaForm.value.list[1],
          },
        ],
        enabled: slaForm.value.enabled ? '1' : '2',
      };
      const [result] = await updateSla(finalParam);
      defaultMsg(result, '更新');
      initSla();
    }
  });
}
async function initSla() {
  const [result] = await getSlaDetail();
  const { data } = result;
  if (data.slaName) {
    slaForm.value = {
      ...data,
      enabled: data.enabled === '1',
    };
  }
}
onMounted(() => {
  initSla();
  defaultDict('no_handler_type').then((res) => {
    noAcceptorOptions.value = res;
  });
});
</script>
<style lang="less" scoped>
.slaSort {
  width: 72px;
  display: inline-block;
  text-align: right;
}
.spanGap {
  margin: 0 8px;
}
</style>
