<template>
  <a-card style="margin: 16px 24px 60px">
    <a-row>
      <a-col :span="20" :offset="1">
        <a-row>
          <h2>工单自动分配</h2>
        </a-row>
        <a-form ref="orderAllot" layout="horizontal" v-bind="formItemLayout">
          <a-form-item label="是否启用">
            <a-switch v-model="enabledFlag" />
            <h6>开启后，分配到受理组的工单将逐一分配给组内开启了”状态在线“开关的客服。</h6>
          </a-form-item>
          <a-form-item label="分配规则">
            <a-radio-group :options="options" v-model="allotRule" />
          </a-form-item>
        </a-form>
        <a-row>
          <h6>当天平均分配：对当天流转的工单进行平均分配，如当天流转10单，在线接单客服2人，则每人5单。</h6>
          <h6>
            所有平均分配：对当天处于接单状态单客服，计算当前其所有待处理单工单数量平均分配，如A已有待处理3单，B有待处理1单，当天流转4单，则A分配1单，B分配3单。
          </h6>
        </a-row>
        <a-form-item :wrapperCol="{ span: 2 }">
          <a-button v-hasPermi="['system:setting:setAllot']" type="primary" @click="updateAllot()">更新</a-button>
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="20" :offset="1">
        <a-space>
          <a @click="goList(1)">查看自动分配人员列表</a>
          <a @click="goList(2)">查看工单池分配人员列表</a>
        </a-space>
      </a-col>
    </a-row>
  </a-card>
</template>
<script setup>
import { onMounted, ref } from 'vue';
import { updateOrderAssign, getOrderAssign } from '@/api/system/settings';
import { useRouter } from 'vue-router/composables';
import { defaultMsg, defaultDict } from '@/utils/system/common';
let orderAssignId;
let goRoute = useRouter();
let allotRule = ref(1);
let enabledFlag = ref();
const formItemLayout = {
  labelCol: { span: 2 },
  wrapperCol: { span: 8 },
};
let options = ref();
async function updateAllot() {
  const param = {
    assignRule: allotRule.value,
    orderAssignId: orderAssignId,
    enabledFlag: enabledFlag.value ? 1 : 0,
  };
  const [result] = await updateOrderAssign(param);
  defaultMsg(result, '配置');
}
function goList(assignType) {
  goRoute.push({ path: '/orderAllotList' });
  localStorage.setItem('assignType', assignType);
}
async function initAllot() {
  defaultDict('assign_rule').then((res) => {
    options.value = res;
  });
  const [result] = await getOrderAssign();
  const { data } = result;
  orderAssignId = data.orderAssignId;
  allotRule.value = data.assignRule;
  enabledFlag.value = data.enabledFlag === '1';
}
onMounted(() => {
  initAllot();
});
</script>
<style scoped lang="less">
h6 {
  color: #a9afb6;
  margin: 8px 0;
}
</style>
