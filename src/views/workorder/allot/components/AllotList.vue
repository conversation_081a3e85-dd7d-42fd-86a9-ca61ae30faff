<template>
  <a-card style="margin: 16px 24px 60px">
    <a-row>
      <a-col :span="6" :offset="1">
        <h2>{{ allotListType }}</h2>
      </a-col>
    </a-row>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :modalConfig="modalConfig"
      @loadData="loadData"
      @handleSubmit="handleSubmit"
      @handleCreate="rowAdd"
      @modalConfirm="modalConfirm"
    >
      <template slot="menu" slot-scope="{ row }">
        <a-switch :defaultChecked="row.enabled === '1'" @change="switchEnable(row)" class="mySwitch">
          <span slot="checkedChildren" type="check">开启</span>
          <span slot="unCheckedChildren" type="close">关闭</span>
        </a-switch>
      </template>
    </BuseCrud>
  </a-card>
</template>
<script setup>
import { computed, h, onMounted, ref } from 'vue';
import moment from 'moment';
import { getOrderTakerList, switchTakerEnable, addAssignUser } from '@/api/system/settings';
import StatuNode from '@/components/TreeNode/StatuNode.vue';
import { defaultMsg, isHasPermi, setCustomTree } from '@/utils/system/common';
import store from '@/store';
const assignType = localStorage.getItem('assignType');
let userList = ref();
let crud = ref();
let loading = ref(true);
let tableData = ref([]);
let tablePage = ref({ total: 0, currentPage: 1, pageSize: 10 });
let selectedNodes;
let allotListType = ref('自动分配人员列表');
const params = ref({
  orderTakerName: undefined,
  allotTimeRange: undefined,
});
const tableColumn = [
  {
    field: 'orderTakerName',
    title: '人员姓名',
  },
  {
    field: 'groupName',
    title: '人员部门',
  },
  {
    field: 'assignCount',
    title: '系统分配次数',
  },
  {
    field: 'lastAssignTime',
    title: '上次系统分配时间',
  },
];
const filterOptions = ref({
  config: [
    {
      field: 'orderTakerName',
      title: '人员',
      element: 'a-tree-select',
      props: {
        treeData: userList,
        mode: 'multiple',
        treeCheckable: true,
        dropdownStyle: { maxHeight: '400px', overflow: 'auto' },
        getPopupContainer: (triggerNode) => triggerNode.parentNode,
      },
      scopedSlots: {
        myTitle: (item) => h(StatuNode, { props: { newNodes: item } }),
      },
      on: {
        focus: () => {
          store.dispatch('base/GetUserList', { nickName: '' }).then((result) => {
            userList.value = setCustomTree(result);
          });
        },
      },
    },
    {
      field: 'allotTimeRange',
      title: '分配时间区间',
      element: 'a-range-picker',
    },
  ],
  params: params,
});
const modalConfig = computed(() => {
  return {
    menu: isHasPermi(['system:setting:enableSwitch']),
    delBtn: false,
    viewBtn: false,
    editBtn: false,
    addBtnText: '新增接单人员',
    addBtn: isHasPermi(['system:setting:addOrderTaker']),
    addTitle: '新增接单人员',
    formConfig: [
      {
        field: 'orderTakerName',
        title: '接单人员选择',
        rules: [{ required: true, message: '请选择接单人员' }],
        element: 'a-tree-select',
        props: {
          treeData: userList.value,
          mode: 'multiple',
          treeCheckable: true,
          dropdownStyle: { maxHeight: '400px', overflow: 'auto' },
          getPopupContainer: (triggerNode) => triggerNode.parentNode,
        },
        scopedSlots: {
          myTitle: (item) => h(StatuNode, { props: { newNodes: item } }),
        },
        on: {
          focus: () => {
            store.dispatch('base/GetUserList', { nickName: '' }).then((result) => {
              userList.value = setCustomTree(result);
            });
          },
          change: (value) => {
            // 获取所有选中的id
            selectedNodes = value.map((item) => ({
              value: item,
            }));
          },
        },
      },
    ],
  };
});
function rowAdd() {
  crud.value.switchModalView(true, 'ADD');
}
// 确认新增
async function modalConfirm() {
  const selectedTree = getTreeByValues(userList.value, selectedNodes);
  const finalParam = {
    groupNameDataList: selectedTree,
    assignType: assignType,
  };
  const [result, err] = await addAssignUser(finalParam);
  defaultMsg(result, '新增', err);
  loadData();
}
// 根据所有选中节点获取选中树并定制
function getTreeByValues(tree, list) {
  let newTree = [];
  for (let i = 0; i < tree.length; i++) {
    const father = tree[i];
    const sonList = father.children;
    let nowFather = {};
    if (sonList && sonList.length !== 0) {
      let nowSon = [];
      for (let sonItem of list) {
        const [son] = sonList.filter((node) => {
          return node.value === sonItem.value;
        });
        if (son) {
          nowSon.push({
            orderTakerId: son.value,
            orderTakerName: son.valueName,
          });
        }
      }
      console.log('nowson', nowSon);
      if (nowSon !== [] && nowSon.length !== 0) {
        nowFather = {
          groupId: father.value,
          groupName: father.valueName,
          takerNameList: [],
        };
        nowFather.takerNameList = nowSon;
      }
      if (JSON.stringify(nowFather) !== '{}') {
        newTree.push(nowFather);
      }
    }
  }
  return newTree;
}
async function loadData(params, search) {
  tablePage.value.currentPage = search ? 1 : tablePage.value.currentPage;
  const finalParam = {
    ...params,
    assignType: assignType,
    pageNum: tablePage.value.currentPage,
    pageSize: tablePage.value.pageSize,
  };
  const [result] = await getOrderTakerList(finalParam);
  tableData.value = result.data;
  tablePage.value.total = result.count;
  loading.value = false;
}
// 查询事件
function handleSubmit() {
  const finalParam = {
    assignType: assignType,
  };
  if (params.value.orderTakerName) {
    finalParam.orderTakerIds = params.value.orderTakerName;
  }
  if (params.value.allotTimeRange) {
    finalParam.startTime = moment(params.value.allotTimeRange[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss');
    finalParam.endTime = moment(params.value.allotTimeRange[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss');
  }
  loadData(finalParam, true);
}

async function switchEnable(row) {
  const param = {
    id: row.id,
    enabled: row.enabled === '1' ? '0' : '1',
  };
  const [result] = await switchTakerEnable(param);
  defaultMsg(result, row.enabled === '1' ? '停用' : '启用');
  row.enabled = row.enabled === '1' ? '0' : '1';
}
onMounted(() => {
  //  根据跳转类型来获取列表
  const data = { assignType: assignType };
  if (assignType === '2') {
    allotListType.value = '工单池分配人员列表';
  }
  loadData(data);
});
</script>
<style scoped lang="less">
.mySwitch {
  width: 64px;
  span {
    color: white;
    margin: unset;
  }
}
</style>
