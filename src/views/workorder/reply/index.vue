<template>
  <a-card style="margin: 16px 24px 60px">
    <a-row>
      <a-col :span="6" :offset="1">
        <h2>预设回复模板</h2>
      </a-col>
    </a-row>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :modalConfig="modalConfig"
      @loadData="loadData"
      @handleCreate="rowAdd"
      @handleSubmit="loadData(true)"
      @modalSubmit="modalSubmit"
      @rowDel="rowDel"
      @rowEdit="rowEdit"
    >
      <template slot="replyContent" slot-scope="{ params }">
        <ProductEditor :value="params.replyContent" :uploadVisible="false" v-model="params.replyContent" />
      </template>
      <template slot="menu" slot-scope="{ row }">
        <a-switch :defaultChecked="row.enableFlag === '1'" @change="switchEnable(row)" class="mySwitch">
          <span slot="checkedChildren" type="check">开启</span>
          <span slot="unCheckedChildren" type="close">关闭</span>
        </a-switch>
      </template>
      <template slot="enableSwitch" slot-scope="{ params }">
        <a-switch :defaultChecked="params.enableFlag === '1'" class="mySwitch">
          <span slot="checkedChildren" type="check">开启</span>
          <span slot="unCheckedChildren" type="close">关闭</span>
        </a-switch>
      </template>
    </BuseCrud>
  </a-card>
</template>
<script setup>
import ProductEditor from '@/components/Editor/ProductEditor.vue';
import { computed, onMounted, ref } from 'vue';
import { getReplyList, updateReplyStatus, replyAdd } from '@/api/system/settings';
import { defaultMsg, isHasPermi } from '@/utils/system/common';
import store from '@/store';
let loading = ref(true);
let crud = ref();
let orderSort = ref();
const params = {
  replyTitle: undefined,
};
const tableColumn = ref([
  {
    field: 'replyTitle',
    title: '标题',
  },
  // {
  //   field: 'replyContent',
  //   title: '内容',
  //   showOverflow: 'ellipsis',
  // },
  {
    field: 'categoryIdList',
    title: '适用分类',
    showOverflow: 'ellipsis',
    formatter: ({ cellValue }) => {
      return tranformCell(cellValue);
    },
  },
  {
    field: 'lastUpdateTime',
    title: '最近编辑时间',
  },
  {
    field: 'updateByName',
    title: '最近编辑人',
  },
]);
const filterOptions = ref({
  config: [
    {
      field: 'replyTitle',
      title: '搜索标题',
    },
  ],
  params: params,
});
const modalConfig = computed(() => {
  return {
    menu: isHasPermi(['system:setting:updateReplyStatus']),
    viewBtn: false,
    okBtn: false,
    addBtn: isHasPermi(['system:setting:editReply']),
    addTitle: '新增回复模板',
    editTitle: '编辑回复模板',
    delBtn: isHasPermi(['system:setting:updateReplyStatus']),
    submitBtn: true,
    formConfig: [
      {
        field: 'replyTitle',
        title: '模板名称',
        rules: [{ required: true, message: '请输入模板名称' }],
      },
      {
        field: 'enableFlag',
        title: '是否启用',
        element: 'slot',
        slotName: 'enableSwitch',
      },
      {
        field: 'replyContent',
        title: '工单回复内容',
        element: 'slot',
        slotName: 'replyContent',
        rules: [{ required: true, message: '请输入工单回复内容' }],
      },
      {
        field: 'categoryList',
        title: '适用分类',
        element: 'a-tree-select',
        props: {
          multiple: true,
          treeData: orderSort.value,
          showSearch: true,
          treeNodeFilterProp: 'title',
          replaceFields: {
            title: 'categoryName',
            value: 'categoryId',
          },
          dropdownStyle: { maxHeight: '400px', overflow: 'auto' },
          getPopupContainer: (triggerNode) => triggerNode.parentNode,
        },
        rules: [{ required: true, message: '请选择适用分类' }],
      },
    ],
  };
});
let tableData = ref([]);
let tablePage = ref({ total: 0, currentPage: 1, pageSize: 10 });
// 数据加载
async function loadData(search) {
  tablePage.value.currentPage = search ? 1 : tablePage.value.currentPage;
  const finalParam = {
    ...params,
    pageNum: tablePage.value.currentPage,
    pageSize: tablePage.value.pageSize,
  };
  const [result] = await getReplyList(finalParam);
  tableData.value = result.data;
  tablePage.value.total = result.count;
  loading.value = false;
}
// 切换开关
async function switchEnable(row) {
  const finalParam = {
    defaultReplyId: row.defaultReplyId,
    editFlag: 1,
    editValue: row.enableFlag === '1' ? '0' : '1',
  };
  const [result] = await updateReplyStatus(finalParam);
  defaultMsg(result, row.enableFlag === '1' ? '禁用' : '启用');
  row.enableFlag = row.enableFlag === '1' ? '0' : '1';
  //loadData();
}
// 递归查找
function getLabelById(list, target) {
  for (let i = 0; i < list.length; i++) {
    if (list[i].categoryId === target) {
      return list[i];
    }
    if (list[i].children && list[i].children.length > 0) {
      const result = getLabelById(list[i].children, target);
      if (result) return result;
    }
  }
}
// 转换表格中的分类
function tranformCell(cellValue) {
  if (cellValue && cellValue.length > 0) {
    const result = cellValue.map((item) => {
      // 加载顺序导致刚开始瞬间获取不到orderSort
      if (orderSort.value && orderSort.value.length > 0) {
        const res = getLabelById(orderSort.value, item);
        return res ? res.categoryName : '';
      }
    });
    if (result && result.length > 1) {
      return result.join('/');
    } else {
      return result;
    }
  }
}
// 弹框事件
async function modalSubmit(params) {
  // 由于分类传递参数类型不同，故根据弹框类型选择查找的属性
  const cateList = params.categoryList.map((item) => {
    let result = {};
    result = getLabelById(orderSort.value, item);
    return result;
  });
  params.categoryList = cateList.map((item) => {
    return {
      categoryId: item.categoryId,
      categoryName: item.categoryName,
    };
  });
  params.enableFlag = params.enableFlag ? '1' : '0';
  const [result] = await replyAdd(params);
  defaultMsg(result, '添加');
  loadData();
}
// 操作事件
function rowAdd() {
  crud.value.switchModalView(true, 'ADD');
}
function rowEdit(row) {
  row.categoryList = row.categoryIdList;
  delete row.categoryIdList;
  crud.value.switchModalView(true, 'UPDATE', row);
}
async function rowDel(row) {
  const finalParam = {
    defaultReplyId: row.defaultReplyId,
    editFlag: 0,
  };
  const [result] = await updateReplyStatus(finalParam);
  defaultMsg(result, '删除');
  loadData();
}
onMounted(() => {
  store.dispatch('base/GetOrderSort').then((result) => {
    orderSort.value = result;
  });
  loadData();
});
</script>
<style lang="less" scoped>
.mySwitch {
  width: 64px;
  margin-right: 8px;
  span {
    color: white;
    margin: unset;
  }
}
/deep/ .quillWrapper .ql-snow.ql-toolbar {
  display: flex;
}
/deep/ .quillWrapper .ql-toolbar.ql-snow .ql-formats {
  transform: scale(0.9);
  display: inline-flex;
  margin: 0 -1px;
}
</style>
