<template>
  <a-card style="margin: 16px 24px 60px">
    <a-row>
      <a-col :span="6" :offset="1">
        <h2>工单质检模板</h2>
      </a-col>
    </a-row>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :tableData="tableData"
      :tableColumn="tableColumn"
      :tablePage="tablePage"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirm"
      @modalCancel="modalCancel"
      @loadData="loadData"
      @handleCreate="rowAdd"
      @rowDel="rowDel"
      @rowEdit="rowEdit"
    >
    </BuseCrud>
  </a-card>
</template>
<script setup>
import { addInspectTemp, getAllInspect } from '@/api/system/settings';
import { defaultMsg, isHasPermi } from '@/utils/system/common';
import { computed, ref, onMounted } from 'vue';
let crud = ref();
let loading = ref(true);
let templateKey = ref();
let tableData = ref([]);
let tablePage = ref({ total: 0, currentPage: 1, pageSize: 10 });
let allTemp = []; // 全部模板
let searchTemp = ref([]); // 搜索模板
let disableKey = ref(false);
let disableName = ref(false);
// const params = ref({
//   checkTemplateId: undefined,
//   templateName: undefined,
//   templateKey: undefined,
//   projectName: undefined,
//   projectScore: undefined,
//   createTime: undefined,
//   createByName: undefined,
// });
const tableColumn = [
  {
    field: 'templateName',
    title: '模板名称',
  },
  {
    field: 'templateKey',
    title: '模板Key',
  },
  {
    field: 'projectName',
    title: '质检项目',
  },
  {
    field: 'projectScore',
    title: '项目分数',
  },
  {
    field: 'time',
    title: '创建时间',
  },
  {
    field: 'createByName',
    title: '创建人',
  },
];
const modalConfig = computed(() => {
  return {
    viewBtn: false,
    addTitle: '新建质检模板',
    addBtn: isHasPermi(['system:setting:inspectAdd']),
    editBtn: isHasPermi(['system:setting:inspectAdd']),
    delBtn: false,
    formConfig: [
      {
        field: 'templateName',
        title: '模板名称',
        element: 'a-select',
        props: {
          options: searchTemp.value,
          showSearch: true,
          mode: 'combobox',
          disabled: disableName.value,
          getPopupContainer: (triggerNode) => triggerNode.parentNode,
        },
        rules: [{ required: true, message: '请输入模版名称' }],
        on: {
          search: (value) => {
            console.log('this is search', value);
            searchTemp.value = allTemp.filter((item) => {
              if (item.label.includes(value)) {
                return item;
              }
            });
            crud.value.setFormFields({ templateName: value });
            disableKey.value = false;
          },
          select: (value) => {
            // 从列表中选择则禁止输入key
            const [res] = allTemp.filter((item) => {
              return item.value === value;
            });
            crud.value.setFormFields({
              templateKey: value,
              templateName: res.label,
            });
            disableKey.value = true;
          },
        },
      },
      {
        field: 'templateKey',
        title: '模板Key',
        props: {
          disabled: disableKey.value,
        },
        rules: [{ required: true, message: '请输入模板Key' }],
        on: {
          change: (value) => {
            templateKey.value = value;
          },
        },
      },
      {
        field: 'projectName',
        title: '质检项目',
        rules: [{ required: true, message: '请输入质检项目' }],
      },
      {
        field: 'projectScore',
        title: '项目分数',
        element: 'a-input-number',
        rules: [
          { required: true, message: '请输入项目分数' },
          { validator: validateInspectTotal, trigger: 'blur' },
        ],
      },
    ],
  };
});
function rowAdd() {
  crud.value.switchModalView(true, 'ADD');
}
function rowDel(row) {
  console.log(row);
}
// 禁止编辑name和key
function rowEdit(row) {
  disableKey.value = true;
  disableName.value = true;
  crud.value.switchModalView(true, 'UPDATE', row);
}
function modalCancel() {
  disableKey.value = false;
  disableName.value = false;
}
async function modalConfirm(params) {
  const [result] = await addInspectTemp(params);
  defaultMsg(result, '新增');
  loadData();
}
// 质检总分校验
function validateInspectTotal(rule, value, callback) {
  let totalNum = value;
  tableData.value.map((item) => {
    if (item.templateKey === templateKey.value) {
      totalNum += Number(item.projectScore);
    }
  });
  if (totalNum > 100) {
    callback('质检项目总分不能超过100分！');
  } else {
    callback();
  }
}
async function loadData() {
  const finalParam = {
    pageNum: tablePage.value.currentPage,
    pageSize: tablePage.value.pageSize,
  };
  const [result] = await getAllInspect(finalParam);
  tableData.value = result.data;
  tablePage.value.total = result.count;
  loading.value = false;
  tableData.value.forEach((item) => {
    const res = allTemp.find((temp) => {
      return temp.value === item.templateKey;
    });
    if (!res) {
      allTemp.push({
        label: item.templateName,
        value: item.templateKey,
      });
    }
  });
  searchTemp.value = allTemp;
}
onMounted(() => {
  loadData();
});
</script>
