//短信统计
<template>
  <div style="margin: 0 24px; width: calc(100vw - 256px)">
    <h2>短信统计</h2>
    <AutoFilters
      :config="config"
      @handleSubmit="loadData"
      @handleReset="handleReset"
      :params="params"
      :gridCol="{ span: 8 }"
    >
      <template slot="timeSelect">
        <a-range-picker value-format="YYYY-MM-DD" v-model="params.timeRange"></a-range-picker>
        <a-radio-group v-model="selectedValue" style="margin-left: 10px">
          <a-radio-button :value="item.value" v-for="(item, index) in timeArr" :key="index">{{
            item.label
          }}</a-radio-button>
        </a-radio-group>
      </template>
    </AutoFilters>
    <a-card style="margin: 16px 0">
      <template slot="title">
        <div class="card-title">数据概括</div>
      </template>
      <div class="card-content">
        <div class="card-left">
          <div class="card-left-item" v-for="(item, index) in statisticList" :key="index">
            <div>{{ item.title }}</div>
            <div>
              <a-statistic
                :valueStyle="{
                  'font-size': '48px',
                  color: index > 3 ? 'rgba(253, 188, 6, 1)' : 'rgba(39, 98, 248, 1)',
                }"
                :value="item.value"
              >
                <template slot="suffix">
                  <span style="color: rgba(0, 0, 0, 0.65)">{{ item.unit }}</span>
                </template>
              </a-statistic>
            </div>
          </div>
        </div>
        <div class="card-right">
          <PieChartSolid :list="briefPieList" v-if="briefPieList && briefPieList.length > 0"></PieChartSolid>
          <a-empty v-else></a-empty>
        </div>
      </div>
    </a-card>
    <a-card style="margin: 16px 0">
      <template slot="title">
        <div class="card-title">发送趋势</div>
      </template>
      <LineChart
        :axisData="trendObj.time"
        :serieData="trendObj.tendencyArr"
        lineType="line"
        symbol=""
        v-if="trendObj.time && trendObj.time.length > 0"
      ></LineChart>
      <a-empty v-else></a-empty>
    </a-card>
    <div class="two-cards-box">
      <a-card>
        <template slot="title">
          <div class="card-title">业务类型</div>
        </template>
        <PieChartSolid
          :list="businessPieList"
          v-if="businessPieList && businessPieList.length > 0"
          :pieRadius="['50%', '70%']"
        ></PieChartSolid>
        <a-empty v-else></a-empty>
      </a-card>
      <a-card>
        <template slot="title">
          <div class="card-title">短信签名</div>
        </template>
        <PieChartSolid
          :list="signPieList"
          v-if="signPieList && signPieList.length > 0"
          :pieRadius="['50%', '70%']"
        ></PieChartSolid>
        <a-empty v-else></a-empty>
      </a-card>
      <a-card>
        <template slot="title">
          <div class="card-title">发送账号</div>
        </template>
        <PieChartSolid
          :list="accountPieList"
          v-if="accountPieList && accountPieList.length > 0"
          :pieRadius="['50%', '70%']"
        ></PieChartSolid>
        <a-empty v-else></a-empty>
      </a-card>
    </div>
  </div>
</template>

<script>
import { initParams } from '@/utils/buse';
import moment from 'moment';
import PieChartSolid from '@/components/Echarts/pieChartSolid.vue';
import LineChart from '@/components/Echarts/LineChart.vue';
import api from '@/api/sms/smsStatistics.js';
import sendApi from '@/api/sms/sendRecord.js';
import { DictCodeAPI } from '@/api/system/dict.js';

export default {
  components: { PieChartSolid, LineChart },
  data() {
    return {
      params: {},
      businessOptions: [],
      signOptions: [],
      dataObj: { count1: 1999 },
      briefPieList: [
        { value: 10, name: '成功', itemStyle: { color: '#3c6af6' } },
        { value: 10, name: '失败', itemStyle: { color: '#e83425' } },
        { value: 10, name: '未知', itemStyle: { color: '#f7d047' } },
      ],
      trendObj: {
        time: ['1', '2', '3', '4', '5'],
        tendencyArr: [
          { name: '发送数', data: [230, 120, 120, 431, 431] },
          { name: '失败数', data: [120, 100, 230, 240, 250] },
          { name: '成功数', data: [100, 120, 90, 60, 40] },
          { name: '未知数', data: [100, 90, 130, 180, 200] },
        ],
      },
      businessPieList: [
        {
          value: 10,
          name: '业务类型1',
          itemStyle: {
            borderRadius: '3%',
          },
        },
        {
          value: 20,
          name: '业务类型2',
          itemStyle: {
            borderRadius: '3%',
          },
        },
        {
          value: 30,
          name: '业务类型3',
          itemStyle: {
            borderRadius: '3%',
          },
        },
        {
          value: 40,
          name: '业务类型4',
          itemStyle: {
            borderRadius: '3%',
          },
        },
        {
          value: 50,
          name: '业务类型5',
          itemStyle: {
            borderRadius: '3%',
          },
        },
      ],
      signPieList: [
        {
          value: 10,
          name: '签名1',
          itemStyle: {
            borderRadius: '3%',
          },
        },
        {
          value: 20,
          name: '签名2',
          itemStyle: {
            borderRadius: '3%',
          },
        },
        {
          value: 30,
          name: '签名3',
          itemStyle: {
            borderRadius: '3%',
          },
        },
        {
          value: 40,
          name: '签名4',
          itemStyle: {
            borderRadius: '3%',
          },
        },
        {
          value: 50,
          name: '签名5',
          itemStyle: {
            borderRadius: '3%',
          },
        },
      ],
      accountPieList: [
        {
          value: 40,
          name: '能源维保通',
          itemStyle: {
            borderRadius: '3%',
          },
        },
        {
          value: 50,
          name: '邦道行短',
          itemStyle: {
            borderRadius: '3%',
          },
        },
      ],
    };
  },
  computed: {
    statisticList() {
      return [
        { title: '累计发送短信条数', value: this.dataObj?.sendCount, unit: '条' },
        { title: '计费条数', value: this.dataObj?.chargeCount, unit: '条' },
        { title: '计费金额', value: this.dataObj?.chargeAmount, unit: '元' },
        { title: '成功数', value: this.dataObj?.successCount, unit: '条' },
        { title: '失败数', value: this.dataObj?.failCount, unit: '条' },
        { title: '未知数', value: this.dataObj?.unknownCount, unit: '条' },
      ];
    },
    selectedValue: {
      get() {
        // 判断当前 params.timeRange 与 timeArr 中哪个 value 匹配
        const selected = this.timeArr.find(
          (item) => JSON.stringify(item.value) === JSON.stringify(this.params.timeRange)
        );
        return selected ? selected.value : null;
      },
      set(value) {
        // 选择后设置 params.timeRange
        this.params.timeRange = value;
      },
    },
    timeArr() {
      return [
        {
          label: '今日',
          value: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        },
        {
          label: '昨日',
          value: [moment().subtract(1, 'days').format('YYYY-MM-DD'), moment().subtract(1, 'days').format('YYYY-MM-DD')],
        },
        {
          label: '近7日',
          value: [moment().subtract(6, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        },
        {
          label: '近30日',
          value: [moment().subtract(29, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        },
        {
          label: '近3个月',
          value: [moment().subtract(3, 'months').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        },
        {
          label: '近半年',
          value: [moment().subtract(6, 'months').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        },
        {
          label: '近一年',
          value: [moment().subtract(12, 'months').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        },
      ];
    },
    config() {
      return [
        {
          field: 'accountName',
          title: '发送账号',
          element: 'a-select',
          props: {
            options: [
              { value: '邦道【能源维保通】', label: '邦道【能源维保通】' },
              { value: '邦道【行短】', label: '邦道【行短】' },
            ],
            showSearch: true,
          },
        },
        {
          field: 'businessType',
          title: '业务类型',
          element: 'a-select',
          props: {
            options: this.businessOptions,
            showSearch: true,
          },
        },
        {
          field: 'smsSign',
          title: '短信签名',
          element: 'a-select',
          props: {
            showSearch: true,
            options: this.signOptions,
          },
        },
        {
          field: 'timeRange',
          title: '时间',
          element: 'slot',
          slotName: 'timeSelect',
          colSpan: { span: 20 },
          itemProps: {
            labelCol: { span: 3 },
            wrapperCol: { span: 21 },
          },
          defaultValue: [moment().subtract(29, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        },
      ];
    },
  },
  created() {
    // sendApi.queryBusinessName({}).then(([res]) => {
    //   this.businessOptions = res.data?.map((x) => {
    //     return { value: x, label: x };
    //   });
    // });
    DictCodeAPI('sms_business_type').then((res) => {
      this.businessOptions = res;
    });
    DictCodeAPI('sms_sign').then((res) => {
      this.signOptions = res;
    });
    // sendApi.querySignName({}).then(([res]) => {
    //   this.signOptions = res.data?.map((x) => {
    //     return { value: x, label: x };
    //   });
    // });
    this.params = initParams(this.config);
    this.loadData();
  },
  methods: {
    async getData(params) {
      const [res] = await api.queryData(params);
      if (res?.code === '10000') {
        this.dataObj = res.data;
        this.briefPieList = [
          { value: this.dataObj.successCount, name: '成功', itemStyle: { color: '#3c6af6' } },
          { value: this.dataObj.failCount, name: '失败', itemStyle: { color: '#e83425' } },
          { value: this.dataObj.unknownCount, name: '未知', itemStyle: { color: '#f7d047' } },
        ];
      }
    },
    async getTendency(params) {
      const [res] = await api.queryTendency(params);
      if (res?.code === '10000') {
        this.trendObj = {
          time: res.data?.map((x) => x.time),
          tendencyArr: [
            { name: '发送数', data: res.data?.map((x) => x.sendCount) },
            { name: '失败数', data: res.data?.map((x) => x.failCount) },
            { name: '成功数', data: res.data?.map((x) => x.successCount) },
            { name: '未知数', data: res.data?.map((x) => x.unknownCount) },
          ],
        };
      }
    },
    async getBusiness(params) {
      const [res] = await api.queryBusiness(params);
      if (res?.code === '10000') {
        this.businessPieList = res.data?.map((x) => {
          return {
            ...x,
            value: x.count,
            itemStyle: {
              borderRadius: '3%',
            },
          };
        });
      }
    },
    async getSign(params) {
      const [res] = await api.querySign(params);
      if (res?.code === '10000') {
        this.signPieList = res.data?.map((x) => {
          return {
            ...x,
            value: x.count,
            itemStyle: {
              borderRadius: '3%',
            },
          };
        });
      }
    },
    async getAccount(params) {
      const [res] = await api.queryAccount(params);
      if (res?.code === '10000') {
        this.accountPieList = res.data?.map((x) => {
          return {
            ...x,
            value: x.count,
            itemStyle: {
              borderRadius: '3%',
            },
          };
        });
      }
    },
    loadData() {
      const params = { ...this.params };
      this.handleTimeRange(params);
      this.getData(params);
      this.getTendency(params);
      this.getBusiness(params);
      this.getSign(params);
      this.getAccount(params);
    },
    handleReset() {
      this.params = initParams(this.config);
      this.loadData();
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: 'timeRange',
          title: '时间',
          startFieldName: 'startTime',
          endFieldName: 'endTime',
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field]) && params[x.field].length > 0) {
          params[x.startFieldName] = params[x.field][0] + ' 00:00:00';
          params[x.endFieldName] = params[x.field][1] + ' 23:59:59';
          delete params[x.field];
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.card-title {
  display: flex;
  align-items: center;
  &::before {
    margin-right: 5px;
    display: inline-block;
    content: '';
    width: 4px;
    height: 18px;
    background: #1b58f4;
  }
}
.card-content {
  display: flex;
  .card-left {
    flex: 2;
    display: grid;
    grid-template-columns: auto auto auto;
    row-gap: 20px;
    &-item {
      font-size: 18px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .text {
        font-size: 48px;
        color: rgba(39, 98, 248, 1);
        margin-right: 6px;
      }
      .text-orange {
        color: rgba(253, 188, 6, 1);
      }
    }
  }
  .card-right {
    flex: 1;
  }
}
.two-cards-box {
  margin: 16px 16px 16px 0;
  display: grid;
  grid-template-columns: 50% 50%;
  row-gap: 16px;
  column-gap: 16px;
}
</style>
