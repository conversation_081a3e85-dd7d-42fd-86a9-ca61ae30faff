<template>
  <div class="sms-send-container">
    <!-- 顶部导航 -->
    <a-tabs v-model="sendType" class="nav-tabs">
      <a-tab-pane key="SAME" tab="相同内容">
        <!-- 主体布局 -->
        <a-row :gutter="24">
          <!-- 左侧表单区 -->
          <a-col :span="16" style="padding: 24px 0">
            <DynamicForm ref="baseForm" :config="formConfig" :params="params">
              <template #mobile>
                <a-textarea
                  v-model="params.mobile"
                  :rows="5"
                  placeholder="请输入手机号码，多个号码之间用英文逗号分隔"
                ></a-textarea>
              </template>
              <template #import>
                <UploadExcel
                  sendType="SAME"
                  @uploadSuccess="handleUploadSuccess"
                  @remove="handleFileRemove"
                ></UploadExcel>
              </template>
              <template #smsContent>
                <a-textarea
                  :rows="5"
                  v-model="params.smsContent"
                  placeholder="请输入短信内容，单条短信长度最大70个字，超过时将分条发送。"
                ></a-textarea>
                <p style="color: red; float: right" v-if="params.smsContent?.length > 70">
                  已输入{{ params.smsContent?.length }}个字，短信内容超出70字，计费{{
                    msgSendCount(params.smsContent)
                  }}条，请注意！
                </p>
                <p style="color: red; float: right" v-if="contentVerifyMsg">
                  {{ contentVerifyMsg }}
                </p>
              </template>
            </DynamicForm>
            <div class="action-buttons">
              <a-button @click="handleReset"> 重置 </a-button>
              <a-button type="primary" @click="submitSend(params)"> 发送 </a-button>
            </div>
          </a-col>

          <!-- 右侧预览区 -->
          <a-col :span="8">
            <div class="preview-container">
              <h3>效果预览</h3>
              <div class="phone-mockup">
                <div class="sms-preview">{{ params.smsContent }}</div>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-tab-pane>
      <a-tab-pane key="BATCH" tab="个性化群发">
        <a-form-model
          ref="batchForm"
          :model="batchParams"
          layout="horizontal"
          :labelCol="{ span: 4 }"
          :wrapperCol="{ span: 20 }"
        >
          <a-form-model-item
            prop="batchList"
            label="在线编辑发送内容"
            :rules="[{ required: true, message: '请填写表格' }]"
          >
            <EditTable :columns="batchColumns" v-model="batchParams.batchList" ref="batchTable" showAddBtn>
              <template #operation="{ row, $rowIndex }">
                <a-button type="link" @click="removeBatchRow($rowIndex)">删除</a-button>
              </template>
            </EditTable>
          </a-form-model-item>
        </a-form-model>
        <div class="action-buttons">
          <a-button @click="handleReset">重置</a-button>
          <a-button type="primary" @click="submitBatchSend(batchParams.batchList)">发送</a-button>
        </div>
      </a-tab-pane>
      <!-- 下发结果弹窗 -->
      <SendModal
        :resultVisible="resultVisible"
        :sendLoading="sendLoading"
        :callbackObj="callbackObj"
        @cancel="handleResultCancel"
        :isTimeout="isTimeout"
        @resend="handleResend"
      ></SendModal>
    </a-tabs>
  </div>
</template>

<script>
import { initParams } from '@/utils/buse';
import SendModal from '../components/sendModal.vue';
import UploadExcel from '../smsTemplate/components/uploadExcel.vue';
import api from '@/api/sms/smsSend.js';
import EditTable from '@/components/EditTable/index.vue';
export default {
  components: {
    UploadExcel,
    SendModal,
    EditTable,
  },
  data() {
    return {
      params: {},
      batchMobile: undefined,
      isTimeout: false,
      sendLoading: false,
      sendBatch: '',
      sendParams: {},
      sendType: 'SAME',
      taskIds: [],
      resultVisible: false,
      callbackObj: {
        // successCount: 0,
        // failedCount: 1,
        // failedReason: '原因原因原因',
      },
      // 新增个性化群发相关数据
      batchParams: {
        batchList: [{ phone: '', msg: '' }],
      },
      contentVerifyMsg: '',
    };
  },
  computed: {
    batchColumns() {
      return [
        {
          title: '手机号码',
          field: 'phone',
          isEdit: true,
          width: '20%',
          props: { placeholder: '请输入手机号' },
          rules: [
            { pattern: /^1[3-9]\d{9}$/, message: '手机号格式错误', trigger: 'change' },
            { required: true, message: '请输入手机号码', trigger: 'change' },
          ],
        },
        {
          title: '短信内容',
          field: 'msg',
          isEdit: true,
          element: 'a-textarea',
          width: '70%',
          props: {
            placeholder: '请输入短信内容，签名写在【】里面，如【新电途】。单条短信长度最大70个字，超过时将分条发送。',
            rows: 2,
          },
          verifyMsgName: 'verifyMsg',
          rules: [
            { required: true, message: '请输入短信内容', trigger: 'change' },
            { validator: this.validateContent, trigger: 'blur' },
          ],
        },
        {
          title: '操作',
          slots: { default: 'operation' },
          width: '10%',
        },
      ];
    },
    formConfig() {
      return [
        {
          field: 'mobile',
          element: 'slot',
          slotName: 'mobile',
          title: '手机号码',
          props: {
            placeholder: '请输入手机号码，多个号码之间用英文逗号分隔',
          },
          attrs: { rows: 5 },
          rules: [{ validator: this.validateMobile }, { required: true, message: '请输入手机号码', trigger: 'change' }],
        },
        {
          field: 'import',
          element: 'slot',
          slotName: 'import',
          title: '手机号导入',
        },
        {
          field: 'smsContent',
          element: 'slot',
          slotName: 'smsContent',
          title: '短信内容',
          props: {
            placeholder: '请输入短信内容，签名写在【】里面，如【新电途】。单条短信长度最大70个字，超过时将分条发送。',
          },
          rules: [
            { required: true, message: '请输入短信内容', trigger: 'change' },
            { validator: this.validateContent, trigger: 'blur' },
          ],
        },
      ];
    },
  },
  created() {
    this.params = initParams(this.formConfig);
  },
  methods: {
    // 新增个性化群发方法
    addBatchRow() {
      //   if (!this.validateTempInputs()) return;
      this.batchParams.batchList.push({
        phone: '',
        msg: '',
      });
    },
    removeBatchRow(rowIndex) {
      this.batchParams.batchList.splice(rowIndex, 1);
    },
    async submitBatchSend(params, apiName = 'submitBatch') {
      this.sendType = 'BATCH';
      this.$refs.batchForm.validate(async (valid) => {
        if (!valid) return;
        const tableValid = await this.$refs.batchTable.validate();

        // const tableValid = await this.validateBatchTable();
        if (!tableValid) return false;
        const [res] = await api[apiName](params);
        // this.resultVisible = true;
        if (res?.code === '10000' && Object.keys(res.data).length > 0) {
          this.sendBatch = res.data?.sendBatch;
          this.taskIds = res.data.taskId;
          this.$message.success('操作成功');
          this.batchParams = {
            batchList: [{ phone: '', msg: '' }],
          };
          // this.intervalApiFunc({ taskIds: this.taskIds });
        } else {
          this.isTimeout = true;
          this.$message.error('操作失败');
        }
      });
    },
    //点击下发并查询发送结果
    async submitSend(row, apiName = 'send') {
      this.$refs.baseForm.validate(async (valid) => {
        if (!valid) return;
        this.sendType = 'SAME';
        const params = {
          ...row,
          sendType: 'SAME',
          sendValue: row.sendValue,
          sign: row.sign,
        };
        this.sendParams = { ...params };
        const [res] = await api[apiName](params);
        // this.resultVisible = true;
        if (res?.code === '10000' && Object.keys(res.data).length > 0) {
          this.sendBatch = res.data?.sendBatch;
          this.$message.success('操作成功');
          this.params = initParams(this.formConfig);
          // this.intervalApiFunc({ taskIds: res.data?.taskId });
        } else {
          this.isTimeout = true;
          this.$message.error('操作失败');
        }
      });
    },
    handleReset() {
      if (this.sendType === 'BATCH') {
        this.batchParams = {
          batchList: [{ phone: '', msg: '' }],
        };
      } else {
        this.params = initParams(this.formConfig);
        this.contentVerifyMsg = '';
      }
    },
    msgSendCount(text) {
      const len = text?.length || 0;
      return len <= 70 ? 1 : Math.ceil(len / 70);
    },
    //下发-文件上传成功
    handleUploadSuccess(row) {
      this.params.mobile = this.params.mobile + row?.mobile;
    },
    //下发-删除上传文件
    handleFileRemove() {
      //   this.batchMobile = undefined;
    },

    // 手机号码校验 如果是导入则不校验规则
    validateMobile(rule, value, callback) {
      console.log('value', rule, value, callback);
      if (value) {
        // 1. 必填校验（处理空输入或纯逗号）
        if (!value || value.trim().replace(/,/g, '') === '') {
          return callback(new Error('请输入手机号'));
        }

        // 2. 全局空格检查（最高优先级）
        if (/\s/.test(value)) {
          return callback(new Error('出现空格'));
        }

        // 3. 中文逗号检查
        if (value.includes('，')) {
          return callback(new Error('请使用英文逗号'));
        }

        const arr = value.split(',');

        // 4. 空手机号检测（如连续逗号或首尾逗号）
        if (arr.some((num) => num === '')) {
          return callback(new Error('手机号码不能为空'));
        }

        // 5. 数量限制校验
        if (arr.length > 1000) {
          return callback(new Error('手机号最多1000个'));
        }

        // 6. 格式校验
        const myreg = /^1[3-9]\d{9}$/;
        for (const num of arr) {
          if (!myreg.test(num)) {
            return callback(new Error(`手机号格式错误: ${num}`));
          }
        }

        // 7. 重复性校验
        const numMap = arr.reduce((acc, num) => {
          acc[num] = (acc[num] || 0) + 1;
          return acc;
        }, {});
        const duplicates = Object.entries(numMap).filter(([_, count]) => count > 1);
        if (duplicates.length > 0) {
          return callback(new Error(`重复手机号: ${duplicates.map((d) => d[0]).join(', ')}`));
        }

        callback(); // 全部通过
      }
      callback();
    },
    // 调接口校验短信内容
    async validateContent(rule, value, callback, item, row, $rowIndex) {
      if (value) {
        if (this.sendType === 'SAME') {
          // 短信内容检验描述(当verifyResult等于1时，提示errorMsg，仍旧可以发送。 如果是0则不允许发送，并提示msg)
          const [res] = await api.validateContent({ msgContent: value });
          const { errorMsg, verifyResult, sendValue, sign } = res.data;
          if (verifyResult == 1) {
            this.contentVerifyMsg = errorMsg;
            this.params.sendValue = sendValue;
            this.params.sign = sign;
            callback();
          } else {
            callback(new Error(errorMsg));
          }
        } else {
          // 短信内容检验描述(当verifyResult等于1时，提示errorMsg，仍旧可以发送。 如果是0则不允许发送，并提示msg)
          const [res] = await api.validateContent({ msgContent: value });
          const { errorMsg, verifyResult, sendValue, sign } = res.data;
          if (verifyResult == 1) {
            this.$set(row, 'verifyMsg', errorMsg);
            this.params.sendValue = sendValue;
            this.params.sign = sign;
            callback();
          } else {
            callback(new Error(errorMsg));
          }
        }
      }
    },
    //下发后重新发送失败的
    handleResend() {
      if (this.sendType === 'SAME') {
        this.submitSend(
          { ...this.sendParams, sendBatch: this.sendBatch, mobile: this.callbackObj?.failedTel },
          'resendSame'
        );
      } else {
        this.submitBatchSend({ taskId: this.taskIds[0] }, 'resend');
      }
    },
    handleResultCancel() {
      this.resultVisible = false;
    },
    intervalApiFunc(params) {
      let attempts = 0;
      const maxAttempts = 10; // 最大尝试次数
      const interval = 3000; // 每次查询的时间间隔（毫秒）
      this.sendLoading = true;

      const intervalId = setInterval(async () => {
        attempts++;
        // 发送请求到后端接口
        const [res] = await api.querySendResult(params);
        if (res?.code === '10000') {
          const { failedCount, successCount, totalCount } = res.data;
          if (failedCount + successCount === totalCount) {
            this.callbackObj = res.data;
            clearInterval(intervalId); // 成功获取数据后清除定时器
            this.sendLoading = false;
          }
        }
        // 如果达到最大尝试次数，停止查询并提示用户
        if (attempts >= maxAttempts) {
          clearInterval(intervalId);
          this.isTimeout = true;
          this.sendLoading = false;
        }
        console.log(attempts, 'attempts');
      }, interval);
    },
  },
};
</script>

<style scoped>
.sms-send-container {
  padding: 24px;
  background: #fff;
}

.nav-tabs {
  margin-bottom: 24px;
}

.nav-tabs::v-deep .ant-tabs-nav-wrap {
  border-bottom: 2px solid #f0f0f0;
}

.nav-tabs::v-deep .ant-tabs-tab-active {
  color: #1890ff;
}

.error-text {
  color: #ff4d4f;
  margin-top: 8px;
}

.upload-section {
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.file-info {
  margin-top: 8px;
  color: #666;
}

.action-buttons {
  margin-top: 24px;
  text-align: center;
  display: flex;
  justify-content: center;
  gap: 16px;
}

.preview-container {
  background: #fff;
  padding: 16px;
  text-align: center;
}

.phone-mockup {
  background: url('@/assets/images/mobile.png') no-repeat center/contain;
  border-radius: 12px;
  padding: 16px;
  width: 375px; /* 根据实际图片宽度设置 */
  height: 667px; /* 根据实际图片高度设置 */
  position: relative;
  margin: 0 auto;
}
.sms-preview {
  text-align: left;
  padding: 10px;
  background: #f2f2f2;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: absolute;
  top: 14%; /* 根据图片实际显示区域调整 */
  left: 20%;
  right: 20%;
  overflow: auto;
}
.word-count {
  color: #666;
  font-size: 12px;
  margin-top: 8px;
}
</style>
