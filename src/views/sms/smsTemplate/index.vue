//短信模版
<template>
  <div>
    <h2 style="margin: 0 24px">短信模板</h2>
    <BuseCrud
      ref="crud"
      title=""
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalCancel="modalCancelHandler"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @loadData="loadData"
      @handleReset="handleReset"
      @rowEdit="handleEdit"
    >
      <template #defaultHeader>
        <a-button @click="rowAdd" type="primary" style="margin-right: 16px" v-hasPermi="['sms:template:add']"
          >新增</a-button
        >
        <a-button @click="handleBatchSend" type="primary" v-hasPermi="['sms:template:batchSend']">个性化群发</a-button>
      </template>
      <template #smsSign="{ item, params }">
        <a-select
          v-model="params.smsSign"
          placeholder="请输入或选择短信签名"
          showSearch
          :options="signOptions"
        ></a-select>
        <p>
          短信签名需符合规范要求，否则会导致短信发送失败。<a-tooltip>
            <template slot="title"
              ><p>
                1、作为短信发送者属性的一种标识，签名必须用于标识公司、产品或业务。建议设置为短信发送方的真实应用名称、网站名称或公司名称。不支持含义模糊的中性签名，如“客服通知”、“客户您好”
              </p>
              <p>2、签名不能含有违法违规或者损害到相关他人权益的内容。</p>
              <p>3、长度限制为2~12个字。由中文、英文或数字组成，不支持符号（含空格）。</p>
              <p>4、请直接填写签名，无需添加【】、()、[]等符号。发送短信时，会自动为您的签名增加括号，例如【邦道】。</p>
            </template>
            <a> 查看规范要求 </a>
          </a-tooltip>
        </p>
      </template>
      <template slot="status" slot-scope="{ row }">
        <a-switch
          :checked="row.status === 0"
          @change="statusChange(row)"
          class="mySwitch"
          :disabled="!isHasPermi(['sms:template:changeStatus'])"
        />
      </template>
      <template #smsContent="{ item, params }">
        <a-textarea
          :rows="5"
          v-model="params.smsContent"
          placeholder="请输入短信内容，单条短信长度最大70个字，超过时将分条发送。"
        ></a-textarea>
        <p style="color: red; float: right" v-if="params.smsContent?.length > 70">
          已输入{{ params.smsContent?.length }}个字，短信内容超出70字，计费{{
            msgSendCount(params.smsContent)
          }}条，请注意！
        </p>
      </template>
      <template #import="{ item, params }">
        <UploadExcel sendType="SAME" @uploadSuccess="handleUploadSuccess" @remove="handleFileRemove"></UploadExcel>
      </template>
    </BuseCrud>
    <a-drawer
      title="操作日志"
      placement="right"
      :visible="logVisible"
      :closable="false"
      @close="logVisible = false"
      width="50%"
    >
      <TimeLine :list="logList"></TimeLine>
    </a-drawer>
    <!-- 个性化群发弹窗 -->
    <a-modal
      width="600px"
      title="个性化群发短信"
      :visible="modalVisible"
      :destroyOnClose="true"
      cancelText="取消"
      okText="确定下发"
      @cancel="modalVisible = false"
      @ok="submitBatchSend(batchMobile)"
    >
      <UploadExcel sendType="MULTI" @uploadSuccess="handleBatchUploadSuccess" @remove="handleFileRemove"></UploadExcel>
    </a-modal>
    <!-- 下发结果弹窗 -->
    <SendModal
      :resultVisible="resultVisible"
      :sendLoading="sendLoading"
      :callbackObj="callbackObj"
      @cancel="handleResultCancel"
      :isTimeout="isTimeout"
      @resend="handleResend"
    ></SendModal>
    <!-- 停用/启用确认弹窗 -->
    <a-modal
      width="600px"
      title="提示信息"
      :visible="statusVisible"
      :destroyOnClose="true"
      cancelText="取消"
      okText="确定"
      @cancel="handleStatusCancel"
      @ok="submitStatusChange"
      centered
    >
      <div class="text-tip">确定{{ templateStatus == 1 ? '停用' : '启用' }}该模版吗？</div>
      <a-form-model
        ref="remarkForm"
        :model="remarkForm"
        layout="horizontal"
        :labelCol="{ span: 4 }"
        :wrapperCol="{ span: 20 }"
      >
        <a-form-model-item prop="remark" label="备注">
          <a-textarea
            v-model="remarkForm.remark"
            :rows="5"
            placeholder="500个字符以内，非必填"
            maxlength="500"
          ></a-textarea>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import api from '@/api/sms/smsTemplate.js';
import { initParams } from '@/utils/buse.js';
import { DictCodeAPI } from '@/api/system/dict.js';
import { defaultMsg, isHasPermi, setCustomTree } from '@/utils/system/common';
import TimeLine from '@/components/Timeline/index.vue';
import UploadExcel from './components/uploadExcel.vue';
import SendModal from '../components/sendModal.vue';
import { message } from 'ant-design-vue';
export default {
  components: { TimeLine, UploadExcel, SendModal },
  data() {
    return {
      tableProps: {
        align: 'center',
      },
      remarkForm: {
        remark: '',
      },
      templateStatus: 1,
      statusVisible: false,
      callbackObj: {
        // successCount: 0,
        // failedCount: 1,
        // failedReason: '原因原因原因',
      },
      resultVisible: false,
      modalVisible: false,
      logList: [
        { operateType: '操作类型', operatorName: '操作人', createTime: '2021-10-21', operateDetail: '备注备注' },
        { operateType: '操作类型', operatorName: '操作人', createTime: '2021-10-21', operateDetail: '备注备注' },
        { operateType: '操作类型', operatorName: '操作人', createTime: '2021-10-21', operateDetail: '备注备注' },
      ],
      logVisible: false,
      operationType: 'add',
      params: {},
      tableData: [],
      tableColumn: [
        { field: 'templateName', title: '模版名称' },
        {
          field: 'businessType',
          title: '业务类型',
        },
        {
          field: 'smsSign',
          title: '短信签名',
        },
        {
          field: 'smsContent',
          title: '短信内容',
          width: 200,
          showOverflow: 'tooltip',
        },
        {
          field: 'createTime',
          title: '创建时间',
        },
        {
          field: 'createName',
          title: '创建人',
        },
        {
          field: 'status',
          title: '状态',
          slots: {
            default: 'status',
          },
        },
      ],
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      businessOptions: [],
      signOptions: [],
      signSource: [],
      businessSource: [],
      isFileUploaded: false,
      batchMobile: undefined,
      isTimeout: false,
      sendLoading: false,
      templateId: '',
      sendBatch: '',
      sendParams: {},
      sendType: 'SAME',
      taskIds: [],
    };
  },
  computed: {
    filterOptions() {
      //筛选类配置
      return {
        config: [
          {
            field: 'templateName',
            title: '模版名称',
          },
          {
            field: 'businessType',
            title: '业务类型',
            element: 'a-select',
            props: {
              options: this.businessOptions,
              showSearch: true,
            },
          },
          {
            field: 'createTime',
            title: '创建时间',
            element: 'a-range-picker',
            props: {
              valueFormat: 'YYYY-MM-DD',
            },
          },
          {
            field: 'createName',
            title: '创建人',
          },
          {
            field: 'status',
            title: '状态',
            element: 'a-select',
            props: {
              options: [
                { value: '0', label: '启用' },
                { value: '1', label: '禁用' },
              ],
              showSearch: true,
            },
          },
          {
            field: 'smsSign',
            title: '短信签名',
            element: 'a-select',
            props: {
              options: this.signOptions,
              showSearch: true,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      const form = {
        //下发
        send: [
          {
            field: 'mobile',
            element: 'a-textarea',
            title: '手机号码',
            props: {
              placeholder: '请输入手机号码，多个号码之间用英文逗号分隔',
              disabled: this.isFileUploaded,
            },
            attrs: { rows: 5 },
            rules: [
              { validator: this.validateMobile },
              { required: true, message: '请输入手机号码', trigger: 'change' },
            ],
          },
          {
            field: 'import',
            element: 'slot',
            slotName: 'import',
            title: '手机号导入',
          },
        ],
        //新增
        add: [
          {
            field: 'templateName',
            element: 'a-input',
            title: '模版名称',
            rules: [{ required: true, message: '请输入模版名称' }],
          },
          {
            field: 'businessType',
            element: 'a-auto-complete',
            title: '业务类型',
            props: {
              dataSource: this.businessSource,
            },
            on: {
              search: this.onBusinessSearch,
            },
            rules: [{ required: true, message: '请输入业务类型' }],
          },
          {
            field: 'smsSign',
            element: 'slot',
            slotName: 'smsSign',
            title: '短信签名',
            rules: [
              { required: true, message: '请选择短信签名' },
              // { pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{2,12}$/, message: '请输入符合规范的短信签名', trigger: 'change' },
            ],
          },
          {
            field: 'smsContent',
            element: 'slot',
            slotName: 'smsContent',
            title: '短信内容',
            props: {
              placeholder: '请输入短信内容，单条短信长度最大70个字，超过时将分条发送。',
            },
            rules: [{ required: true, message: '请输入短信内容' }],
          },
        ],
      };
      //弹窗类配置
      return {
        formConfig: form[this.operationType] || [],
        customOperationTypes: [
          {
            title: '下发',
            typeName: 'send',
            event: (row) => {
              return new Promise((resolve) => {
                this.handleSend(row);
                resolve();
              });
            },
            condition: (row) => {
              return row.status == 0 && isHasPermi(['sms:template:send']);
            },
          },
          {
            title: '批次记录',
            typeName: 'batchRecord',
            event: (row) => {
              return new Promise((resolve) => {
                this.handleJumpBatchRecord(row);
                resolve();
              });
            },
            condition: (row) => {
              return isHasPermi(['sms:template:batchRecord']);
            },
          },
          {
            title: '日志',
            typeName: 'log',
            event: (row) => {
              return new Promise((resolve) => {
                this.handleLogDrawer(row);
                resolve();
              });
            },
            condition: (row) => {
              return isHasPermi(['sms:template:log']);
            },
          },
          {
            title: '下发记录',
            typeName: 'sendRecord',
            event: (row) => {
              return new Promise((resolve) => {
                this.handleJumpSendRecord(row);
                resolve();
              });
            },
            condition: (row) => {
              return isHasPermi(['sms:template:sendRecord']);
            },
          },
          {
            title: '复制',
            typeName: 'copy',
            event: (row) => {
              return new Promise((resolve) => {
                this.handleCopy(row);
                resolve();
              });
            },
            condition: (row) => {
              return isHasPermi(['sms:template:copy']);
            },
          },
        ],
        menuTitle: '操作',
        addBtn: false,
        addTitle: '新增',
        delBtn: isHasPermi(['sms:template:delete']),
        viewBtn: false,
        editBtn: isHasPermi(['sms:template:update']),
        editTitle: '编辑',
      };
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
    // api.querySignName({}).then(([res]) => {
    //   this.signOptions = res.data?.map((x) => {
    //     return { value: x, label: x };
    //   });
    // });
    DictCodeAPI('sms_sign').then((res) => {
      this.signOptions = res;
    });
    DictCodeAPI('sms_business_type').then((res) => {
      this.businessOptions = res;
    });

    // api.queryBusinessName({}).then(([res]) => {
    //   this.businessOptions = res.data?.map((x) => {
    //     return { value: x, label: x };
    //   });
    // });
    this.loadData();
  },
  methods: {
    isHasPermi,
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: 'createTime',
          title: '创建时间',
          startFieldName: 'createStartTime',
          endFieldName: 'createEndTime',
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field]) && params[x.field].length > 0) {
          params[x.startFieldName] = params[x.field][0] + ' 00:00:00';
          params[x.endFieldName] = params[x.field][1] + ' 23:59:59';
          delete params[x.field];
        }
      });
    },
    // 手机号码校验 如果是导入则不校验规则
    validateMobile(rule, value, callback) {
      if (value) {
        const myreg = /^[1][3-9][0-9]{9}$/;
        const arr = value.split(',');
        if (arr.length > 1000) {
          callback(new Error('手机号码最多1000个'));
        }
        const lastMobile = arr[arr.length - 1];
        if (lastMobile.length === 11) {
          const num2 = arr.reduce((acc, item) => {
            acc[item] ? acc[item]++ : (acc[item] = 1);
            return acc;
          }, {});
          for (const i in arr) {
            if (!myreg.test(arr[i])) {
              callback(new Error('手机号码格式错误'));
              break;
            }
            if (num2[arr[i]] > 1) {
              callback(new Error('存在重复手机号'));
            }
          }
        } else {
          if (!myreg.test(lastMobile) && lastMobile.length === 11) {
            callback(new Error('手机号码格式错误'));
          } else if (lastMobile.length < 11) {
            callback(new Error('请输入完整的手机号'));
          } else {
            callback(new Error('手机号位数超限'));
          }
        }
      }
      callback();
    },
    async onSignSearch(searchText) {
      const [res] = await api.querySignName({ name: searchText });
      this.signSource = res.data;
    },
    async onBusinessSearch(searchText) {
      const [res] = await api.queryBusinessName({ name: searchText });
      this.businessSource = res.data;
    },

    //打开日志抽屉
    async handleLogDrawer(row) {
      const [res] = await api.getLogList({ templateId: row.templateId });
      this.logList = res.data || [];
      this.logVisible = true;
    },
    //跳转至下发记录
    handleJumpSendRecord(row) {
      this.$router.push({ name: 'sendRecord', params: { templateId: row.templateId } });
    },
    //跳转至批次记录
    handleJumpBatchRecord(row) {
      this.$router.push({ name: 'batchRecord', params: { templateId: row.templateId } });
    },
    handleStatusCancel() {
      this.statusVisible = false;
      this.$refs.remarkForm.resetFields();
    },
    //停用/启用
    async submitStatusChange() {
      const param = {
        templateId: this.templateId,
        status: this.templateStatus,
        ...this.remarkForm,
      };
      const [result] = await api.switchTakerEnable(param);
      defaultMsg(result, this.templateStatus == 1 ? '停用' : '启用');
      this.statusVisible = false;
      this.handleStatusCancel();
      this.loadData();
    },
    //点击状态切换 打开确认弹窗
    statusChange(row) {
      this.statusVisible = true;
      this.templateId = row.templateId;
      console.log(row.status, 'status');
      this.templateStatus = row.status == 1 ? 0 : 1;
    },
    msgSendCount(text) {
      const len = text?.length || 0;
      return len <= 70 ? 1 : Math.ceil(len / 70);
    },
    handleSend(row) {
      this.isFileUploaded = false;
      this.operationType = 'send';
      this.$refs.crud.switchModalView(true, 'send', { ...initParams(this.modalConfig.formConfig), ...row });
    },
    handleEdit(row) {
      this.operationType = 'add';
      this.$refs.crud.switchModalView(true, 'UPDATE', { ...initParams(this.modalConfig.formConfig), ...row });
    },
    rowAdd() {
      this.onSignSearch();
      this.onBusinessSearch();
      this.operationType = 'add';
      this.$refs.crud.switchModalView(true, 'ADD', initParams(this.modalConfig.formConfig));
    },
    handleBatchSend() {
      this.batchMobile = undefined;
      this.modalVisible = true;
    },
    //下发-删除上传文件
    handleFileRemove() {
      this.batchMobile = undefined;
      this.isFileUploaded = false;
      this.$refs.crud.setFormFields({ mobile: '' });
    },
    //下发-文件上传成功
    handleUploadSuccess(row) {
      this.isFileUploaded = true;
      this.$refs.crud.setFormFields({ mobile: row?.mobile });
    },
    //个性群发-文件上传成功
    handleBatchUploadSuccess(formData) {
      this.batchMobile = formData;
    },

    intervalApiFunc(params) {
      let attempts = 0;
      const maxAttempts = 10; // 最大尝试次数
      const interval = 3000; // 每次查询的时间间隔（毫秒）
      this.sendLoading = true;

      const intervalId = setInterval(async () => {
        attempts++;
        // 发送请求到后端接口
        const [res] = await api.querySendResult(params);
        if (res?.code === '10000') {
          const { failedCount, successCount, totalCount } = res.data;
          if (failedCount + successCount === totalCount) {
            this.callbackObj = res.data;
            clearInterval(intervalId); // 成功获取数据后清除定时器
            this.sendLoading = false;
          }
        }
        // 如果达到最大尝试次数，停止查询并提示用户
        if (attempts >= maxAttempts) {
          clearInterval(intervalId);
          this.isTimeout = true;
          this.sendLoading = false;
        }
        console.log(attempts, 'attempts');
      }, interval);
    },
    //点击群发
    async submitBatchSend(params, apiName = 'submitBatch') {
      this.sendType = 'MULTI';
      if (params) {
        const [res] = await api[apiName](params);
        this.modalVisible = false;
        // this.resultVisible = true;
        if (res?.code === '10000' && Object.keys(res.data).length > 0) {
          this.sendBatch = res.data?.sendBatch;
          this.taskIds = res.data.taskId;
          // this.intervalApiFunc({ taskIds: this.taskIds });
          this.$message.success('操作成功');
        } else {
          this.isTimeout = true;
          this.$message.error('操作失败');
        }
      } else {
        this.$message.error('文件不能为空或文件内容有误！');
      }
    },
    //下发后重新发送失败的
    handleResend() {
      if (this.sendType === 'SAME') {
        this.submitSend(
          { ...this.sendParams, sendBatch: this.sendBatch, mobile: this.callbackObj?.failedTel },
          'resendSame'
        );
      } else {
        this.submitBatchSend({ taskId: this.taskIds[0] }, 'resend');
      }
    },
    //点击下发并查询发送结果
    async submitSend(row, apiName = 'send') {
      this.sendType = 'SAME';
      const params = {
        ...row,
        sendType: 'SAME',
        sign: row.smsSign,
        sendValue: row.smsContent,
      };
      this.sendParams = { ...params };
      const [res] = await api[apiName](params);
      this.$refs.crud.switchModalView(false);
      // this.resultVisible = true;
      if (res?.code === '10000' && Object.keys(res.data).length > 0) {
        this.sendBatch = res.data?.sendBatch;
        // this.intervalApiFunc({ taskIds: res.data?.taskId });
        this.$message.success('操作成功');
      } else {
        this.isTimeout = true;
        this.$message.error('操作失败');
      }
    },
    handleResultCancel() {
      this.resultVisible = false;
    },
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      console.log(crudOperationType, 'crud');
      let params = { ...formParams };
      if (crudOperationType === 'send') {
        this.submitSend(formParams);
        return false;
      }
      // crudOperationType:add/update
      const [res] = await api[crudOperationType](params);
      if (res?.code === '10000') {
        this.$message.success('操作成功');
        this.loadData();
      } else {
        this.$message.error('操作失败');
        return false;
      }
    },
    async loadData() {
      let params = { ...this.params };
      this.handleTimeRange(params);
      let page = { pageSize: this.tablePage.pageSize, pageNum: this.tablePage.currentPage };
      this.loading = true;
      let [result] = await api.getTableData({
        ...page,
        ...params,
      });
      this.loading = false;
      this.tableData = result.data;
      this.tablePage.total = Number(result.count);
    },
    handleReset() {
      this.tablePage.currentPage = 1;
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    modalCancelHandler() {
      this.$refs.crud.switchModalView(false);
    },
    deleteRowHandler(row) {
      const params = { templateId: row.templateId };
      this.$confirm({
        title: '确定删除该模版吗？',
        // content: 'When clicked the OK button, this dialog will be closed after 1 second',
        onOk: async () => {
          const [result] = await api.delete(params);
          defaultMsg(result, '删除');
          this.loadData();
        },
      });
    },
    handleCopy(row) {
      const params = { templateId: row.templateId };
      this.$confirm({
        title: '确定复制该模版吗？',
        // content: 'When clicked the OK button, this dialog will be closed after 1 second',
        onOk: async () => {
          const [result] = await api.copy(params);
          defaultMsg(result, '复制');
          this.loadData();
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.text-blue {
  color: rgb(39, 98, 248);
}
.text-tip {
  text-align: center;
  font-size: 18px;
  margin-bottom: 10px;
}
</style>
