<template>
  <div>
    <a-row>
      <a-upload
        accept=".xlsx"
        name="file"
        :fileList="fileList"
        :remove="handleRemove"
        :beforeUpload="beforeUpload"
        :custom-request="uploadFile"
      >
        <div v-if="fileList.length < 1">
          <a-button type="primary"> <a-icon style="line-height: 1.499" type="upload" />上传文件 </a-button>
        </div>
      </a-upload>
      <a-col>
        支持xls、xlsx、csv。请上传未加密的文件！
        <a type="link">
          <a @click="downloadExcel">下载模板</a>
        </a>
      </a-col>
      <p style="color: red; float: right" v-if="validate">
        {{ validateText }}
      </p>
    </a-row>
    <!-- <a-row v-if="sendType === 'MULTI'">
      <a-col>
        <label>号码数量：{{ getMultiNum() }}</label>
      </a-col>
    </a-row> -->
  </div>
</template>

<script>
import api from '@/api/sms/smsTemplate.js';

export default {
  props: {
    sendType: {
      type: String,
      default: 'SAME',
    },
  },
  data() {
    return {
      validate: false,
      validateText: '',
      fileList: [],
    };
  },
  methods: {
    // 下载一对一模板
    downloadExcel() {
      const link = document.createElement('a');
      // 设置文件的URL（可以是本地文件路径或远程文件URL）
      link.href =
        this.sendType === 'SAME'
          ? '/work-order-system/static/相同内容群发excel样例文件.xlsx'
          : '/work-order-system/static/个性化内容群发excel样例文件.xlsx'; // 替换为你的文件路径
      // 设置下载文件的名字
      link.download = this.sendType === 'SAME' ? '相同内容群发excel样例文件.xlsx' : '个性化内容群发excel样例文件.xlsx'; // 用户下载后文件的名字
      // 触发点击事件
      link.click();
      document.body.removeChild(link);
      //   window.location.href = '/work-order-system/static/个性化内容群发excel样例文件.xlsx';
    },
    // 删除选中文件
    handleRemove(file) {
      const index = this.fileList.indexOf(file);
      const newFileList = this.fileList.slice();
      newFileList.splice(index, 1);
      this.fileList = newFileList;
      this.validate = false;
      this.$emit('remove');
      //   this.num = 0;
      //   this.mobileRequest.required = true;
      //   this.mobileRequest.disabled = false;
      //   this.mobileRequest.sendType = 'SAME';
      //   this.getCreateSendBatch();
      //   this.form.setFieldsValue({
      //     mobile: '',
      //     num: 0,
      //   });
    },
    // 导入前文件格式检验
    beforeUpload(file) {
      const type = file.name.toLowerCase().split('.').pop();
      if (!['xls', 'xlsx', 'csv'].includes(type)) {
        this.$message.error('只能上传 excel 文件!');
        return false;
      }
    },
    // 文件上传
    uploadFile(options) {
      const formData = new FormData();
      formData.append('file', options.file);
      formData.append('sendSame', this.sendType === 'SAME');

      api
        .excelAdd(formData)
        .then((response) => {
          const [res] = response;
          this.fileList = [...this.fileList, options.file];
          if (res.data?.verifyMessage) {
            this.validate = true;
            this.validateText = res.data.verifyMessage;
            //校验不通过时输入框也改成禁用状态
            this.$emit('uploadSuccess');
            return false;
          }
          this.$message.success('上传成功');
          if (this.sendType === 'MULTI') {
            this.$emit('uploadSuccess', formData);
          } else {
            this.$emit('uploadSuccess', { fileList: this.fileList, ...res.data });
          }
        })
        .catch((err) => {
          console.log('失败了');
          this.$message.error(err.rtnMsg);
        });
    },
  },
};
</script>

<style></style>
