//查询下发结果弹窗
<template>
  <a-modal
    width="600px"
    title="下发结果"
    :visible="resultVisible"
    :destroyOnClose="true"
    centered
    :bodyStyle="{ display: 'flex', justifyContent: 'center' }"
    @cancel="handleCancel"
    :zIndex="1001"
    :maskClosable="false"
  >
    <a-spin tip="短信发送中..." :spinning="sendLoading"></a-spin>
    <div v-if="!sendLoading">
      <!-- 全部成功 -->
      <a-result status="info" v-if="callbackObj.failedCount === 0">
        <template slot="icon"><a-icon type="check-circle" theme="filled" /></template>
        <template slot="title">
          <span class="text-blue"> {{ callbackObj.successCount }} </span>条已全部成功下发！</template
        >
      </a-result>
      <!-- 部分失败 -->
      <a-result status="error" v-else-if="callbackObj.failedCount && callbackObj.failedCount > 0">
        <template slot="icon"><a-icon type="exclamation-circle" theme="filled" /></template>
        <template slot="title">
          成功下发<span class="text-blue"> {{ callbackObj.successCount }} </span>条，失败<span class="text-blue">
            {{ callbackObj.failedCount }} </span
          >条！</template
        >
        <template slot="subTitle"
          ><div class="reason">{{ callbackObj.failedReason }}</div></template
        >
      </a-result>
      <!-- 超时 -->
      <a-result title="红豆短信回调结果延迟，请稍后在批次列表或下发记录中刷新查看结果！" v-else-if="isTimeout">
      </a-result>
    </div>
    <!-- 尾部按钮插槽 -->
    <template slot="footer">
      <div v-if="!sendLoading">
        <a-button @click="handleCancel" v-if="callbackObj.failedCount === 0 || isTimeout">我知道了</a-button>
        <div v-else-if="callbackObj.failedCount && callbackObj.failedCount > 0">
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="submitBatchSend">失败部分重新下发</a-button>
        </div>
      </div>
      <div v-else></div>
    </template>
  </a-modal>
</template>

<script>
export default {
  props: {
    resultVisible: { type: Boolean, default: false },
    sendLoading: { type: Boolean, default: false },
    callbackObj: { type: Object, default: () => undefined },
    isTimeout: { type: Boolean, default: false },
  },
  data() {
    return {};
  },
  methods: {
    handleCancel() {
      this.$emit('cancel');
    },
    submitBatchSend() {
      this.$emit('resend');
    },
  },
};
</script>

<style>
.reason {
  margin-top: 10px;
  max-height: 150px;
  overflow: auto;
}
</style>
