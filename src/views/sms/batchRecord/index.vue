//批次记录
<template>
  <div>
    <h2 style="margin: 0 24px">短信批次记录</h2>
    <BuseCrud
      ref="crud"
      title=""
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalCancel="modalCancelHandler"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @handleReset="handleReset"
    >
      <template #defaultHeader>
        <a-button @click="handleExport" style="margin-right: 16px" v-hasPermi="['sms:batchRecord:export']"
          >导出</a-button
        >
        <!-- <a-button @click="handleBatchSend" type="primary">重新发送</a-button> -->
      </template>
      <template #import="{ item, params }"></template>
    </BuseCrud>
    <a-drawer
      title="操作日志"
      placement="right"
      :visible="logVisible"
      :closable="false"
      @close="logVisible = false"
      width="50%"
    >
      <TimeLine :list="logList"></TimeLine>
    </a-drawer>
    <FailDetailDrawer ref="failDetailDrawer" @close="loadData"></FailDetailDrawer>
    <!-- 下发结果弹窗 -->
    <SendModal
      :resultVisible="resultVisible"
      :sendLoading="sendLoading"
      :callbackObj="callbackObj"
      @cancel="handleResultCancel"
      :isTimeout="isTimeout"
      @resend="handleResend"
    ></SendModal>
  </div>
</template>

<script>
import api from '@/api/sms/batchRecord.js';
import { initParams } from '@/utils/buse.js';
import { DictCodeAPI } from '@/api/system/dict.js';
import { defaultMsg, isHasPermi, setCustomTree } from '@/utils/system/common';
import TimeLine from '@/components/Timeline/index.vue';
import FailDetailDrawer from './components/failDetailDrawer.vue';
import { downLoadXlsx } from '@/utils/system/common';
import SendModal from '../components/sendModal.vue';

export default {
  components: { TimeLine, FailDetailDrawer, SendModal },
  data() {
    return {
      tableProps: {
        align: 'center',
        showOverflow: 'tooltip',
      },
      isTimeout: false,
      sendLoading: false,
      callbackObj: {},
      resultVisible: false,
      sendBatch: '',
      logList: [],
      logVisible: false,
      operationType: 'add',
      params: {},
      tableData: [],
      tableColumn: [
        // { type: 'checkbox' },
        { field: 'batchType', title: '批次类型', width: 120 },
        {
          field: 'batchCode',
          title: '批次号',
          width: 180,
          slots: {
            default: ({ row }) => {
              return [
                <a
                  on={{
                    click: () => this.handleJumpSendRecord(row),
                  }}
                >
                  {row.batchCode}
                </a>,
              ];
            },
          },
        },
        {
          field: 'businessType',
          title: '业务类型',
          width: 100,
        },
        {
          field: 'smsContent',
          title: '批次内容',
          width: 200,
          slots: {
            default: ({ row }) => {
              const vnode =
                row.batchType === '个性化群发'
                  ? [
                      <a
                        on={{
                          click: () => this.handleJumpSendRecord(row),
                        }}
                      >
                        详情
                      </a>,
                    ]
                  : [<span>{row.smsContent}</span>];
              return vnode;
            },
          },
        },
        {
          field: 'wordAndSplitCount',
          title: '字数｜拆分条数',
          width: 100,
          slots: {
            default: ({ row }) => {
              const vnode =
                row.batchType === '个性化群发'
                  ? [
                      <a
                        on={{
                          click: () => this.handleJumpSendRecord(row),
                        }}
                      >
                        明细
                      </a>,
                    ]
                  : [<span>{row.wordAndSplitCount}</span>];
              return vnode;
            },
          },
        },
        {
          field: 'sendStatusName',
          title: '发送状态',
          width: 100,
          titlePrefix: {
            content: `请刷新页面查看最新的回调结果`,
            icon: 'vxe-icon-question-circle-fill',
          },
          slots: {
            default: ({ row }) => {
              return [<span style={{ color: row.sendStatus == '1' ? '#1b58f4' : 'red' }}>{row.sendStatusName}</span>];
            },
          },
        },
        {
          field: 'sendCount',
          title: '发送数',
          width: 100,
        },
        {
          field: 'successCount',
          title: '成功数',
          width: 100,
        },
        {
          field: 'failCount',
          title: '失败数',
          width: 100,
          slots: {
            default: ({ row }) => {
              return [
                row.failCount >= 0 ? (
                  <a
                    on={{
                      click: () => this.handleFailDrawer(row),
                    }}
                  >
                    {row.failCount}
                  </a>
                ) : (
                  <span>{row.failCount}</span>
                ),
              ];
            },
          },
        },
        {
          field: 'unknownCount',
          title: '未知数',
          width: 100,
        },
        {
          field: 'successRate',
          title: '成功率',
          width: 100,
        },
        {
          field: 'sendUserName',
          title: '发送人',
          width: 100,
        },
        {
          field: 'operateTime',
          title: '操作时间',
          width: 180,
        },
        {
          field: 'remark',
          title: '备注',
          width: 150,
        },
      ],
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      businessOptions: [],
      batchTypeOptions: [
        { value: '相同内容群发', label: '相同内容群发' },
        { value: '个性化群发', label: '个性化群发' },
      ],
    };
  },
  computed: {
    filterOptions() {
      //筛选类配置
      return {
        config: [
          {
            field: 'batchCode',
            title: '批次号',
          },
          {
            field: 'batchType',
            title: '批次类型',
            element: 'a-select',
            props: {
              options: this.batchTypeOptions,
              showSearch: true,
            },
          },
          {
            field: 'createTime',
            title: '批次时间',
            element: 'a-range-picker',
            props: {
              valueFormat: 'YYYY-MM-DD',
            },
          },
          {
            field: 'sendUserName',
            title: '发送人',
          },
          {
            field: 'sendStatus',
            title: '发送状态',
            element: 'a-select',
            props: {
              options: [
                { value: '1', label: '全部成功' },
                { value: '2', label: '部分成功' },
                { value: '3', label: '全部失败' },
              ],
              showSearch: true,
            },
          },
          {
            field: 'operateTime',
            title: '操作时间',
            element: 'a-range-picker',
            props: {
              valueFormat: 'YYYY-MM-DD',
            },
          },
          {
            field: 'businessType',
            title: '业务类型',
            element: 'a-select',
            props: {
              options: this.businessOptions,
              showSearch: true,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      const form = {
        //备注
        remark: [
          {
            field: 'remark',
            element: 'a-textarea',
            title: '备注',
            props: {
              placeholder: '500个字符以内',
              maxLength: 500,
            },
            attrs: { rows: 5 },
          },
        ],
      };
      //弹窗类配置
      return {
        formConfig: form[this.operationType] || [],
        customOperationTypes: [
          {
            title: '重发',
            typeName: 'send',
            event: (row) => {
              if (row.templateStatus == 1) {
                this.$message.error('当前模版已停用，请开启后再进行重发！');
                return;
              }
              this.handleSend({ batchCode: row.batchCode });
            },
            condition: (row) => {
              return [2, 3].includes(row.sendStatus) && isHasPermi(['sms:batchRecord:resend']);
            },
          },
          {
            title: '备注',
            typeName: 'remark',
            event: (row) => {
              return new Promise((resolve) => {
                this.handleRemark(row);
                resolve();
              });
            },
            condition: (row) => {
              return isHasPermi(['sms:batchRecord:remark']);
            },
          },
          {
            title: '日志',
            typeName: 'log',
            event: (row) => {
              return new Promise((resolve) => {
                this.handleLogDrawer(row);
                resolve();
              });
            },
            condition: (row) => {
              return isHasPermi(['sms:batchRecord:log']);
            },
          },
        ],
        menuTitle: '操作',
        addBtn: false,
        delBtn: false,
        viewBtn: false,
        editBtn: false,
        menuFixed: 'right',
      };
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
    if (this.$route.params) {
      this.params = { ...this.params, ...this.$route.params };
    }
    DictCodeAPI('sms_business_type').then((res) => {
      this.businessOptions = res;
    });
    // api.queryBusinessName({}).then(([res]) => {
    //   this.businessOptions = res.data?.map((x) => {
    //     return { value: x, label: x };
    //   });
    // });
    this.loadData();
  },
  methods: {
    intervalApiFunc(params) {
      let attempts = 0;
      const maxAttempts = 10; // 最大尝试次数
      const interval = 3000; // 每次查询的时间间隔（毫秒）
      this.sendLoading = true;

      const intervalId = setInterval(async () => {
        attempts++;
        // 发送请求到后端接口
        const [res] = await api.querySendResult(params);
        if (res?.code === '10000') {
          const { failedCount, successCount, totalCount } = res.data;
          if (failedCount + successCount === totalCount) {
            this.callbackObj = res.data;
            clearInterval(intervalId); // 成功获取数据后清除定时器
            this.sendLoading = false;
          }
        }
        // 如果达到最大尝试次数，停止查询并提示用户
        if (attempts >= maxAttempts) {
          clearInterval(intervalId);
          this.isTimeout = true;
          this.sendLoading = false;
        }
        console.log(attempts, 'attempts');
      }, interval);
    },

    handleResultCancel() {
      this.resultVisible = false;
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: 'createTime',
          title: '批次时间',
          startFieldName: 'createStartTime',
          endFieldName: 'createEndTime',
        },
        {
          field: 'operateTime',
          title: '操作时间',
          startFieldName: 'operateStartTime',
          endFieldName: 'operateEndTime',
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field]) && params[x.field].length > 0) {
          params[x.startFieldName] = params[x.field][0] + ' 00:00:00';
          params[x.endFieldName] = params[x.field][1] + ' 23:59:59';
          delete params[x.field];
        }
      });
    },
    async handleExport() {
      let params = { ...this.params };
      this.handleTimeRange(params);
      let page = { pageSize: this.tablePage.pageSize, pageNum: this.tablePage.currentPage };
      // const sendRecordIds = this.$refs.crud.getCheckboxRecords()?.map((x) => x.sendRecordId);
      // console.log('sendRecordIds', sendRecordIds);
      let [result] = await api.export({
        ...page,
        ...params,
      });
      downLoadXlsx(new Blob([result]), '批次记录导出.xlsx');
    },
    //失败明细
    handleFailDrawer(row) {
      this.$refs.failDetailDrawer.open(row);
    },
    //打开日志抽屉
    async handleLogDrawer(row) {
      const [res] = await api.getLogList({ batchRecordId: row.batchRecordId });
      this.logList = res.data || [];
      this.logVisible = true;
    },
    //跳转至下发记录
    handleJumpSendRecord(row) {
      this.$router.push({ name: 'sendRecord', params: { batchCode: row.batchCode } });
    },
    //备注
    handleRemark(row) {
      this.operationType = 'remark';
      this.$refs.crud.switchModalView(true, 'remark', { batchRecordId: row.batchRecordId, remark: row.remark });
    },
    //列表重发按钮
    async handleSend(params) {
      const [res] = await api.resend(params);
      this.resultVisible = true;
      if (res?.code === '10000' && Object.keys(res.data).length > 0) {
        this.sendBatch = res.data?.sendBatch;
        this.intervalApiFunc({ taskIds: res.data?.taskId });
      } else {
        this.isTimeout = true;
      }
    },
    //重发后重新发送失败的
    handleResend() {
      this.handleSend({ batchCode: this.sendBatch });
    },
    //批量重发
    // handleBatchSend() {},
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      // crudOperationType:remark
      const [res] = await api[crudOperationType](params);
      if (res?.code === '10000') {
        this.$message.success('提交成功');
        this.loadData();
      } else {
        return false;
      }
    },
    async loadData() {
      let params = { ...this.params };
      this.handleTimeRange(params);
      let page = { pageSize: this.tablePage.pageSize, pageNum: this.tablePage.currentPage };
      this.loading = true;
      let [result] = await api.getTableData({
        ...page,
        ...params,
      });
      this.loading = false;
      this.tableData = result.data;
      this.tablePage.total = Number(result.count);
    },
    handleReset() {
      this.tablePage.currentPage = 1;
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    modalCancelHandler() {
      console.log('取消按钮');
    },
  },
};
</script>

<style></style>
