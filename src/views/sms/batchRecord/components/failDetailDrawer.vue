<template>
  <a-drawer
    title="失败明细"
    placement="right"
    :visible="visible"
    :closable="false"
    @close="handleClose"
    width="50%"
    destroyOnClose
  >
    <div>
      <a-button @click="handleBatchSend" type="primary" v-if="tableData && tableData.length > 0">重新发送</a-button>
    </div>
    <BuseCrud
      ref="crud"
      title=""
      :loading="loading"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :modalConfig="modalConfig"
      @loadData="loadData"
    ></BuseCrud>
    <!-- 下发结果弹窗 -->
    <SendModal
      :resultVisible="resultVisible"
      :sendLoading="sendLoading"
      :callbackObj="callbackObj"
      @cancel="handleResultCancel"
      :isTimeout="isTimeout"
      @resend="handleResend"
    ></SendModal>
  </a-drawer>
</template>

<script>
import api from '@/api/sms/batchRecord.js';
import SendModal from '../../components/sendModal.vue';

export default {
  components: {
    SendModal,
  },
  data() {
    return {
      templateStatus: 0,
      isTimeout: false,
      sendLoading: false,
      callbackObj: {},
      resultVisible: false,
      sendBatch: '',
      visible: false,
      tableColumn: [
        { type: 'checkbox', width: 50 },
        { field: 'telPhone', title: '失败号码' },
        { field: 'failReason', title: '失败原因' },
        { field: 'failCount', title: '失败次数' },
        { field: 'successTime', title: '最新失败时间' },
        {
          field: 'sendStatus',
          title: '最新下发状态',
          slots: {
            default: ({ row }) => {
              const dict = [
                { label: '待发送', value: 0 },
                { label: '提交成功', value: 1 },
                { label: '发送成功', value: 2 },
                { label: '发送失败', value: 3 },
                { label: '未知', value: 4 },
              ];
              const statusName = dict.find((x) => x.value == row.sendStatus)?.label;
              return [
                <span style={{ color: ['3', '4'].includes(row.sendStatus) ? '#165dff' : 'red' }}>{statusName}</span>,
              ];
            },
          },
        },
      ],
      tableData: [],
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      batchCode: '',
    };
  },
  computed: {
    modalConfig() {
      return {
        addBtn: false,
        delBtn: false,
        viewBtn: false,
        editBtn: false,
        menuWidth: 100,
        customOperationTypes: [
          {
            title: '重发',
            typeName: 'send',
            event: (row) => {
              return new Promise((resolve) => {
                this.handleSend({ batchCode: row.batchCode });
                resolve();
              });
            },
            condition: (row) => {
              return [3, 4].includes(row.sendStatus);
            },
          },
        ],
      };
    },
  },
  methods: {
    handleClose() {
      this.visible = false;
      this.$emit('close');
    },
    intervalApiFunc(params) {
      let attempts = 0;
      const maxAttempts = 10; // 最大尝试次数
      const interval = 3000; // 每次查询的时间间隔（毫秒）
      this.sendLoading = true;

      const intervalId = setInterval(async () => {
        attempts++;
        // 发送请求到后端接口
        const [res] = await api.querySendResult(params);
        if (res?.code === '10000' && Object.keys(res.data).length > 0) {
          const { failedCount, successCount, totalCount } = res.data;
          if (failedCount + successCount === totalCount) {
            this.callbackObj = res.data;
            clearInterval(intervalId); // 成功获取数据后清除定时器
            this.sendLoading = false;
            this.loadData();
          }
        } else {
          clearInterval(intervalId);
          this.isTimeout = true;
          this.sendLoading = false;
        }
        // 如果达到最大尝试次数，停止查询并提示用户
        if (attempts >= maxAttempts) {
          clearInterval(intervalId);
          this.isTimeout = true;
          this.sendLoading = false;
        }
      }, interval);
    },

    handleResultCancel() {
      this.resultVisible = false;
    },
    handleBatchSend() {
      const checkedArr = this.$refs.crud.getCheckboxRecords();
      this.handleSend({
        taskId: checkedArr[0]?.taskId,
        batchCode: this.batchCode,
        mobiles: checkedArr?.map((x) => x.telPhone),
      });
    },
    async loadData() {
      let page = { pageSize: this.tablePage.pageSize, pageNum: this.tablePage.currentPage };
      this.loading = true;
      let [result] = await api.getFailDetail({
        ...page,
        batchCode: this.batchCode,
      });
      this.loading = false;
      this.tableData = result.data;
      this.tablePage.total = Number(result.count);
    },
    open(row) {
      this.tablePage = { total: 0, currentPage: 1, pageSize: 10 };
      this.batchCode = row.batchCode;
      this.templateStatus = row.templateStatus;
      this.visible = true;
      this.loadData();
    },
    //列表重发按钮
    async handleSend(params) {
      if (this.templateStatus == 1) {
        this.$message.error('当前模版已停用，请开启后再进行重发！');
        return;
      }
      const [res] = await api.resend(params);
      this.resultVisible = true;
      if (res?.code === '10000' && Object.keys(res.data).length > 0) {
        this.sendBatch = res.data?.sendBatch;
        this.intervalApiFunc({ taskIds: res.data?.taskId });
      } else {
        this.isTimeout = true;
      }
    },
    //重发后重新发送失败的
    handleResend() {
      this.handleSend({ batchCode: this.sendBatch });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .bd3001-page-wrapper-container {
  margin: 16px 0;
}
/deep/ .bd3001-page-wrapper-container .bd3001-content {
  padding: 0;
}
</style>
