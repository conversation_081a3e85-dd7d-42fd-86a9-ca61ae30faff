//下发记录
<template>
  <div>
    <h2 style="margin: 0 24px">短信下发记录</h2>
    <BuseCrud
      ref="crud"
      title=""
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalCancel="modalCancelHandler"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @handleReset="handleReset"
    >
      <template #defaultHeader>
        <a-button @click="handleExport" v-hasPermi="['sms:sendRecord:export']">导出</a-button>
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import api from '@/api/sms/sendRecord.js';
import { initParams } from '@/utils/buse.js';
import { DictCodeAPI } from '@/api/system/dict.js';
import { defaultMsg, isHasPermi, setCustomTree } from '@/utils/system/common';
import { message } from 'ant-design-vue';
import { downLoadXlsx } from '@/utils/system/common';

export default {
  data() {
    return {
      tableProps: {
        align: 'center',
        rowConfig: {
          keyField: 'groupId',
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      operationType: 'add',
      params: {},
      tableData: [],
      tableColumn: [
        // { type: 'checkbox', width: 60 },
        { field: 'telPhone', title: '手机号', width: 150 },
        { field: 'sendStatusName', title: '发送状态', width: 100 },
        { field: 'failReason', title: '失败原因', width: 120 },
        { field: 'batchCode', title: '发送批次', width: 200 },
        { field: 'businessType', title: '业务类型', width: 120 },
        {
          field: 'smsSign',
          title: '短信签名',
          width: 150,
        },
        {
          field: 'smsContent',
          title: '短信内容',
          width: 200,
          showOverflow: 'tooltip',
        },
        {
          field: 'wordAndSplitCount',
          title: '字数｜拆分条数',
          width: 100,
        },
        {
          field: 'createTime',
          title: '提交时间',
          width: 180,
        },
        {
          field: 'createName',
          title: '提交人',
          width: 100,
        },
        {
          field: 'successTime',
          title: '发送成功时间',
          width: 180,
        },
        {
          field: 'accountName',
          title: '短信发送账号名称',
          width: 150,
        },
      ],
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      businessOptions: [],
      failReasonOptions: [],
      sendStatusOptions: [],
      sendBatchOptions: [],
      signOptions: [],
      currentPage: 1,
      isLoading: false,
      batchCodeName: '',
    };
  },
  computed: {
    filterOptions() {
      //筛选类配置
      return {
        config: [
          {
            field: 'telPhone',
            title: '手机号',
          },
          {
            field: 'sendStatus',
            title: '发送状态',
            element: 'a-select',
            props: {
              options: [
                { value: 0, label: '待发送' },
                { value: 1, label: '提交成功' },
                { value: 2, label: '发送成功' },
                { value: 3, label: '发送失败' },
                { value: 4, label: '未知' },
              ],
              showSearch: true,
            },
          },
          {
            field: 'createTime',
            title: '提交时间',
            element: 'a-range-picker',
            props: {
              valueFormat: 'YYYY-MM-DD',
            },
          },
          {
            field: 'createName',
            title: '提交人',
          },
          {
            field: 'businessType',
            title: '业务类型',
            element: 'a-select',
            props: {
              options: this.businessOptions,
              showSearch: true,
            },
          },
          {
            field: 'successSendTime',
            title: '成功时间',
            element: 'a-range-picker',
            props: {
              valueFormat: 'YYYY-MM-DD',
            },
          },
          {
            field: 'failReason',
            title: '失败原因',
            element: 'a-select',
            props: {
              options: this.failReasonOptions,
              showSearch: true,
            },
          },
          {
            field: 'batchCode',
            title: '发送批次',
            element: 'a-select',
            props: {
              options: this.sendBatchOptions,
              showSearch: true,
              filterOption: false, //这个一定要加
            },
            on: {
              popupScroll: (event) => {
                const { scrollTop, offsetHeight, scrollHeight } = event.target;
                if (scrollTop + 2 + offsetHeight >= scrollHeight) {
                  // 检测到滚动到底部
                  this.getBatchCodeOptions();
                }
              },
              //输入内容回调 调接口搜索
              search: async (val) => {
                this.batchCodeName = val;
                this.sendBatchOptions = [];
                this.currentPage = 1;
                await this.getBatchCodeOptions();
              },
            },
          },
          {
            field: 'smsSign',
            title: '短信签名',
            element: 'a-select',
            props: {
              options: this.signOptions,
              showSearch: true,
            },
          },
          {
            field: 'accountName',
            title: '发送账号',
            element: 'a-select',
            props: {
              options: [
                { value: '邦道【能源维保通】', label: '邦道【能源维保通】' },
                { value: '邦道【行短】', label: '邦道【行短】' },
              ],
              showSearch: true,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      //弹窗类配置
      return {
        formConfig: [],
        customOperationTypes: [],
        menu: false,
        menuTitle: '操作',
        addBtn: false,
        delBtn: true,
        viewBtn: false,
        editBtn: true,
      };
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
    if (this.$route.params) {
      this.params = { ...this.params, ...this.$route.params };
    }
    // DictCodeAPI('order_tag').then((res) => {
    //   this.sendStatusOptions = res;
    // });
    // api.queryBusinessName({}).then(([res]) => {
    //   this.businessOptions = res.data?.map((x) => {
    //     return { value: x, label: x };
    //   });
    // });
    DictCodeAPI('sms_business_type').then((res) => {
      this.businessOptions = res;
    });
    DictCodeAPI('sms_fail_reason').then((res) => {
      this.failReasonOptions = res;
    });
    DictCodeAPI('sms_sign').then((res) => {
      this.signOptions = res;
    });
    // api.queryFailReasonName({}).then(([res]) => {
    //   this.failReasonOptions = res.data?.map((x) => {
    //     return { value: x, label: x };
    //   });
    // });
    // api.querySignName({}).then(([res]) => {
    //   this.signOptions = res.data?.map((x) => {
    //     return { value: x, label: x };
    //   });
    // });
    this.getBatchCodeOptions();
    this.loadData();
  },
  methods: {
    async getBatchCodeOptions() {
      // api.queryBatchCodeName({ batchCode: 'SMS', }).then(([res]) => {
      //   this.sendBatchOptions = res.data?.map((x) => {
      //     return { value: x, label: x };
      //   });
      // });
      if (this.isLoading) {
        return;
      }
      this.isLoading = true;
      const params = { pageNum: this.currentPage, pageSize: 10, batchCode: this.batchCodeName };
      const [res] = await api.queryBatchCodeName(params);
      const newOptions = res?.data?.map((x) => {
        return { value: x, label: x };
      });
      if (newOptions.length > 0) {
        this.sendBatchOptions = this.sendBatchOptions.concat(newOptions);
        this.currentPage++;
      }
      this.isLoading = false;
    },
    async handleExport() {
      let params = { ...this.params };
      this.handleTimeRange(params);
      let page = { pageSize: this.tablePage.pageSize, pageNum: this.tablePage.currentPage };
      // const sendRecordIds = this.$refs.crud.getCheckboxRecords()?.map((x) => x.sendRecordId);
      // console.log('sendRecordIds', sendRecordIds);
      let [result] = await api.export({
        ...page,
        ...params,
      });
      downLoadXlsx(new Blob([result]), '下发记录导出.xlsx');
    },

    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      // crudOperationType:transfer/withdraw
      const [res] = await api[crudOperationType](params);
      if (res?.code === '10000') {
        this.$message.success('提交成功');
        this.loadData();
      } else {
        return false;
      }
    },
    async loadData() {
      console.log('params筛选参数', this.params);
      let params = { ...this.params };
      this.handleTimeRange(params);
      let page = { pageSize: this.tablePage.pageSize, pageNum: this.tablePage.currentPage };
      this.loading = true;
      let [result] = await api.getTableData({
        ...page,
        ...params,
      });
      this.loading = false;
      this.tableData = result.data;
      this.tablePage.total = Number(result.count);
    },
    handleReset() {
      this.tablePage.currentPage = 1;
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    modalCancelHandler() {
      console.log('取消按钮');
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: 'createTime',
          title: '提交时间',
          startFieldName: 'createStartTime',
          endFieldName: 'createEndTime',
        },
        {
          field: 'successSendTime',
          title: '成功时间',
          startFieldName: 'successStartTime',
          endFieldName: 'successEndTime',
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field]) && params[x.field].length > 0) {
          params[x.startFieldName] = params[x.field][0] + ' 00:00:00';
          params[x.endFieldName] = params[x.field][1] + ' 23:59:59';
          delete params[x.field];
        }
      });
    },
  },
};
</script>

<style></style>
