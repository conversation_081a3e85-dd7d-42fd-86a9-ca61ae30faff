<template>
  <div class="login-container">
    <LoginForm
      class="login-box"
      @success="afterLogin"
    ></LoginForm>
    <div
      v-if="copyright"
      class="copyright"
    >
      {{ copyright }}
    </div>
  </div>
</template>

<script>
import LoginForm from '@/components/Login/LoginForm.vue';
import { initRoute } from '@/router/guards';

export default {
  name: 'LoginPage',
  components: { LoginForm },
  computed: {
    copyright () {
      return process.env.VUE_APP_COPY_RIGHT;
    },
  },
  methods: {
    // 登录之后操作
    async afterLogin () {
      // $(window).unbind('beforeunload');
      window.onbeforeunload = null;
      try {
        console.log('afterLogin');
        await this.$store.dispatch('base/GetInfo');
        const redirect = this.$route.query.redirect || '';
        console.log('redirect', redirect);
        // this.$store.dispatch('base/setLoginFlag', true);
        if (redirect && redirect.indexOf('http') > -1) {
          window.location.href = redirect;
        } else {
          // 查询用户信息并初始化动态路由
          await initRoute(this.$store);
          const hasFirstMenu = this.$store.state.setting.sysFirstMenuPath;
          console.log('.split', hasFirstMenu);
          console.log(window.location.href.split('#'));
          if (window.location.href.split('#')[0]) {
            window.location.href = window.location.href.split('#')[0];
          } else {
            this.$router.replace(hasFirstMenu || '/401');
          }
        }
      } catch (error) {
        console.log(error);
        this.$router.replace('/');
      }
    },
  },
};
</script>

<style lang="less" scoped>
.login-container {
  display: flex;
  align-items: center;
  height: 100vh;
  .login-box {
    transform: translateY(-10%);
  }
  .copyright {
    position: fixed;
    bottom: 16px;
    width: 100%;
    text-align: center;
  }
}
</style>
