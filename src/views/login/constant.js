//表单规则
export const getRules = () => {
  return {
    username: [
      {
        required: true,
        message: '请输入用户名',
      },
      {
        max: 30,
        message: '账户名不得超过30个字符',
      },
    ],
    code: [
      {
        required: true,
        message: '请输入验证码',
        trigger: 'change',
      },
    ],
    password: [
      {
        required: true,
        message: '请输入用户密码',
      },
    ],
  };
};
// 初始化表单
export const initForm = () => {
  return {
    username: '',
    password: '',
    code: '',
  };
};
// 重置表单校验
export const getResetRules = () => {
  return {
    userName: [
      { required: true, message: '请输入账户名', trigger: 'change' },
      { max: 30, message: '账户名不得超过30个字符' },
    ],
    originalPassword: [{ required: true, message: '请输入原始密码', trigger: 'change' }],
    password: [
      {
        required: true,
        message: '请输入密码',
        trigger: 'change',
        whitespace: true,
      },
      {
        validator: (rule, value, callback) => {
          try {
            let reg = /(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[\W_]).{8,}/;
            if (reg.test(value)) {
              callback();
            } else {
              callback(new Error('数据格式错误'));
            }
          } catch (err) {
            callback(new Error('数据格式错误'));
          }
        },
      },
    ],
    passwordSecond: [
      {
        required: true,
        message: '请输入密码',
        trigger: 'change',
        whitespace: true,
      },
      {
        validator: (rule, value, callback) => {
          try {
            let reg = /(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[\W_]).{8,}/;
            if (reg.test(value)) {
              const form = this.form;
              if (value && value !== form.password) {
                callback(new Error('两次密码不同!'));
              } else {
                callback();
              }
            } else {
              callback(new Error('数据格式错误'));
            }
          } catch (err) {
            callback(new Error('数据格式错误'));
          }
        },
      },
    ],
  };
};
// 初始化表单
export const initResetForm = () => {
  return {
    userName: '', //用户名称
    password: '', //新密码
    originalPassword: '', //原始密码
    passwordSecond: '', //重复密码
  };
};
