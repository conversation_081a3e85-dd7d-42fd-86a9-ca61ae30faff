export const queryList = ({ limit, pageNum }) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const list = [
        {
          id: 10001,
          name: '张三',
          sex: '1',
          age: 22,
        },
        {
          id: 10002,
          name: 'cxk',
          sex: '2',
          age: 22,
        },
      ];
      resolve([
        {
          total: list.length,
          data: list.slice((pageNum - 1) * limit, pageNum * limit),
        },
        undefined,
      ]);
    }, 100);
  });
};
