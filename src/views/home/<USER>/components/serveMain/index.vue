<template>
  <div>
    <div class="line-b-title">质检项</div>

    <div class="select" v-hasPermi="['system:telDetail:templateListAll']">
      <a-select
        v-if="nowMod.templateKey"
        placeholder="质检模板选择"
        style="width: 300px"
        :disabled="checkStatus !== undefined"
        v-model="nowMod.templateKey"
      >
        <a-select-option
          v-for="(item, index) in allList"
          :key="index"
          :value="item.templateKey"
          @click="changeSelect(index)"
        >
          {{ item.templateName }}
        </a-select-option></a-select
      >
    </div>

    <div class="score">
      合计得分 <span class="big-font">{{ sum }}</span>
    </div>
    <div class="rank-box">
      <a-descriptions bordered class="box-distance">
        <a-descriptions-item
          v-for="(item, index) in nowMod.project"
          :key="index"
          :label="`*${item.projectName}（${item.projectScore}）`"
          :span="8"
        >
          <input
            class="area-none"
            :rows="4"
            v-model="score[index]"
            @input="handleInput(index)"
            :disabled="checkStatus !== undefined"
          />
        </a-descriptions-item>
      </a-descriptions>
    </div>

    <!-- serveTest部分 -->
    <div>
      <div class="line-b-title">质检评语</div>
      <div style="margin-bottom: 8px">
        <a-textarea
          :rows="4"
          style="font-size: 16px"
          :disabled="checkStatus !== undefined"
          v-model="checkComment"
          placeholder="输入质检评语"
        />
      </div>

      <div class="flex-box" style="margin-top: 16px" v-if="checkStatus == undefined">
        <a-button @click="submit" type="primary" v-hasPermi="['system:telDetail:communicateCheck']">确定</a-button>
        <a-button @click="goBack">返回</a-button>
      </div>
      <div>
        <a-alert style="margin-top: 8px" v-if="showWarn" message="需要填入所有分数才能提交" type="warning" />
      </div>
    </div>

    <!-- serveAppeal部分 -->
    <div v-if="checkStatus > 0">
      <div class="line-b-title">申诉详情</div>
      <div style="margin-bottom: 8px">
        <a-textarea :rows="4" style="font-size: 16px" v-model="appealContent" :disabled="checkStatus !== '1'" />
      </div>
      <div class="flex-box" style="margin-top: 16px" v-if="checkStatus == '1'">
        <a-button type="primary" @click="appeal" v-hasPermi="['system:telDetail:communicateCheckAppeal']"
          >申诉</a-button
        >
        <a-button @click="goBack">返回</a-button>
      </div>
    </div>

    <!-- serveAppealTest部分 -->
    <div v-if="checkStatus > 1">
      <div class="line-b-title">申诉评语</div>
      <div style="margin-bottom: 8px">
        <a-textarea :rows="4" style="font-size: 16px" v-model="appealDesc" :disabled="checkStatus !== '2'" />
      </div>
      <div class="flex-box" style="margin-top: 16px" v-if="checkStatus == '2'">
        <a-button type="primary" @click="pass" v-hasPermi="['system:telDetail:communicateCheckAppeal']">通过</a-button>
        <a-button @click="reject" v-hasPermi="['system:telDetail:communicateCheckAppeal']">驳回</a-button>
      </div>
    </div>
    <!-- <serveAppealTest :listForAppealTest="listForAppealTest" v-if="path == 'serveappealtest'" :appealtest="appealtest" /> -->
  </div>
</template>

<script>
import { checkAPI, checkAppealAPI, checkDetailAPI } from '@/api/system/telManager';

import { listAllAPI } from '@/api/system/telManager';
export default {
  props: ['checkStatus', 'checkId'],
  components: {},
  data() {
    return {
      //serveTest转移
      showWarn: false,
      appealContent: '', //申诉详情
      appealDesc: '', //申诉评语

      communicateId: '',
      allList: [{ project: '' }], //整个收到的对象数组
      modChoice: 0, //但前选中
      checkTemplateId: [],
      templateName: [],
      projectName: [],
      projectScore: [],
      score: [],
      path: '',

      listForAppealTest: '', //appealTest的数据
    };
  },
  methods: {
    //质检下拉模板选择
    // async listAllFn() {
    //   let [result] = await listAllAPI();
    //   console.log('质检模板下拉列表', result.data);
    // },

    //模板选择，在这里传name和id
    choiceMod(value, name) {
      this.transList.templateName = name;
      this.transList.templateKey = value;
    },
    //serveTest
    async submit() {
      if (this.fullfill == true) {
        this.transList.checkDesc = this.checkComment;
        this.transList.communicateId = this.$store.state.base.cumId;
        // if (this.checkStatus !== undefined) {
        this.transList.checkRecordId = this.checkId;
        // }
        console.log('看看参数', this.transList);
        let [result] = await checkAPI(this.transList);
        console.log(result);
        this.$message.info('质检提交成功');
        this.showWarn = false;
      } else {
        this.showWarn = true;
      }
    },
    async appeal() {
      let [result] = await checkAppealAPI({
        checkRecordId: this.checkId,
        appealContent: this.appealContent,
        status: '2',
      });
      console.log(result);
      if (result.code == '10000') {
        this.$notification.open({
          message: '成功',
          description: '申诉成功',
          icon: <a-icon type="smile" style="color: #108ee9" />,
        });
      }
    },
    async pass() {
      let [result] = await checkAppealAPI({
        checkRecordId: this.checkId,
        appealDesc: this.appealDesc,
        status: '3',
      });
      console.log(result);
      if (result.code == '10000') {
        this.$notification.open({
          message: '成功',
          description: '内容通过成功',
          icon: <a-icon type="smile" style="color: #108ee9" />,
        });
      }
    },
    async reject() {
      let [result] = await checkAppealAPI({
        checkRecordId: this.checkId,
        appealDesc: this.appealDesc,
        status: '4',
      });
      console.log(result);
      if (result.code == '10000') {
        this.$notification.open({
          message: '成功',
          description: '驳回成功',
          icon: <a-icon type="smile" style="color: #108ee9" />,
        });
      }
    },
    handleInput(index) {
      console.log(this.nowMod.project[index].projectScore);
      if (this.score[index] === '' || isNaN(this.score[index])) {
        this.score[index] = 0; // 对于非规范值，清空输入值
      } else {
        this.score[index] = Math.min(Math.max(Number(this.score[index]), 0), this.nowMod.project[index].projectScore); // 将值限制在0到分数限制之间
      }
      console.log(this.fullfill);
      console.log(this.transList);
    },

    //获取模板列表，也是第一次进入时刻
    async getList() {
      let [result] = await listAllAPI();
      console.log('不处理result看看', result);
      if (result.code === '10000') {
        // console.log(result.data.project);
        // let result = result.data;
        this.allList = result.data;
        console.log('现在获取到了allList', this.allList);
        for (const item in this.allList) {
          this.checkTemplateId.push(item.checkTemplateId);
          this.templateName.push(item.templateName);
        }

        //因为默认选择第一个，所以要处理一下。后续会默认按照value和Name选
        if (this.checkStatus == undefined) {
          this.changeSelect(0);
        }
      }
    },
    changeSelect(value) {
      console.log('切换' + value);
      this.modChoice = value;

      console.log('选择之后的nowMod', this.nowMod);
      this.score = [];
      this.choiceMod(this.nowMod.templateKey, this.nowMod.templateName);
      console.log('transList改变了吗', this.transList);
    },

    //在已经质检的情况下，获取值
    async getDefaultValue() {
      let [result] = await checkDetailAPI(this.$store.state.base.cumId);
      console.log('质检结果', result.data);
      if (result.data !== undefined) {
        this.score = [];
        this.checkComment = '';

        this.modChoice = this.allList.findIndex((item) => item.templateKey == result.data.checkRecord.templateKey);
        console.log('findIndex', this.modChoice);
        this.changeSelect(this.modChoice);
        this.nowMod.templateKey = result.data.checkRecord.templateKey || 'Test'; //质检回显模板

        for (const item of result.data.project) {
          this.score.push(item.projectScore);
        }
        this.checkComment = result.data.checkRecord.checkDesc;

        try {
          this.appealContent = result.data.checkRecord.appealContent;
          this.appealDesc = result.data.checkRecord.appealDesc;
        } catch (err) {
          console.log('本来就没');
        }

        console.log('这是质检了才会出现的');
      } else {
        console.warn('这个时候应该是未质检');
      }
    },
    goBack() {
      this.$router.go(-1);
    },
  },
  computed: {
    //serveTest相关
    // subList() {
    //   return this.transList;
    // },
    sum() {
      return Number(this.score.reduce((total, num) => total + num, 0));
    },
    //给子组件传值
    transList() {
      let list = JSON.parse(JSON.stringify(this.nowMod));
      // list.communicateId = this.communicateId;
      list.communicateId = this.$store.state.base.cumId;
      for (let i = 0; i < list.project.length; i++) {
        list.project[i].projectScore = Number(this.score[i]);
      }
      return list;
    },
    nowMod() {
      console.log('计算中的allList', this.modChoice);
      return this.allList[this.modChoice];
    },
    fullfill() {
      //要求全部数字填写，然后返回true
      return this.transList.project.every((item) => {
        return item.projectScore > 0 || item.projectScore == 0;
      });
    },
  },
  created() {
    console.log('Detail详细数据', this.checkDetail);
    this.getList();
  },
  beforeMount() {
    this.getDefaultValue();
    console.log('nowMod的allList', this.allList);
    console.log('nowMod', this.nowMod);
  },
};
</script>
<style lang="less" scoped>
.flex-box {
  display: flex;
  justify-content: space-evenly;
}
.select {
  margin: 8px auto;
  width: 3vw;
  flex: 1 1 auto;
  margin-left: 16px;
}
.big-font {
  font-size: 24px;
  color: #00cc00;
  line-height: 32px;
  height: 32px;
  flex: 1 1 auto;
}
.score {
  margin-left: 16px;
}
//去除文本边框
.area-none {
  border: none;
  resize: none;
  outline: none;
  font-size: 18px;
  color: #00cc00;
  width: 36px;
  margin: 0 auto;
}
.rank-box {
  width: 300px;
  margin: 16px;
}
.line-b-title {
  border-left: 4px solid #1b58f4;
  padding-left: 10px; /* 可选，用于控制文字与边框的间距 */
  text-align: left;
  color: color rgb(51, 51, 51);
  font-weight: bold;
  font-size: 16px;
  line-height: 24px;
  height: 24px;
  margin-bottom: 8px;
}
</style>
