<template>
  <div>
    <div class="line-b-title">申诉详情</div>
    <div>
      <a-textarea :rows="4" style="font-size: 16px" :disabled="!appeal" />
    </div>
    <div class="flex-box" style="margin-top: 16px" v-if="appeal">
      <a-button>申诉</a-button>
      <a-button>返回</a-button>
    </div>
  </div>
</template>

<script>
export default {
  props: ['appeal'],
  components: {},
  data() {
    return {};
  },
  methods: {},
};
</script>
<style lang="less" scoped>
.line-b-title {
  border-left: 4px solid #1b58f4;
  padding-left: 10px; /* 可选，用于控制文字与边框的间距 */
  text-align: left;
  color: color rgb(51, 51, 51);
  font-weight: bold;
  font-size: 16px;
  line-height: 24px;
  height: 24px;
  margin-bottom: 8px;
  margin-top: 8px;
}
.flex-box {
  display: flex;
  justify-content: space-evenly;
}
</style>
