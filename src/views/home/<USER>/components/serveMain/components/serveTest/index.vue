<template>
  <div>
    <div class="line-b-title">质检评语</div>
    <div>
      <a-textarea
        :rows="4"
        style="font-size: 16px"
        :disabled="!test"
        v-model="checkComment"
        placeholder="输入质检评语"
      />
    </div>

    <div class="flex-box" style="margin-top: 16px" v-if="test">
      <a-button @click="submit">确定</a-button>
      <a-button>返回</a-button>
    </div>
    <div>
      <a-alert style="margin-top: 8px" v-if="showWarn" message="需要填入所有分数才能提交" type="warning" />
    </div>
  </div>
</template>

<script>
export default {
  props: ['test', 'fullfill', 'transList'],
  components: {},
  data() {
    return {
      showWarn: false,
      checkComment: '',
    };
  },
  methods: {
    submit() {
      if (this.fullfill == true) {
        this.subList.checkComment = this.checkComment;
        console.log(this.subList);
        console.log('提交成功');
        this.showWarn = false;
      } else {
        this.showWarn = true;
      }
    },
  },
  computed: {
    subList() {
      return this.transList;
    },
  },
};
</script>
<style lang="less" scoped>
.line-b-title {
  border-left: 4px solid #1b58f4;
  padding-left: 10px; /* 可选，用于控制文字与边框的间距 */
  text-align: left;
  color: color rgb(51, 51, 51);
  font-weight: bold;
  font-size: 16px;
  line-height: 24px;
  height: 24px;
  margin-bottom: 8px;
}
.flex-box {
  display: flex;
  justify-content: space-evenly;
}
</style>
