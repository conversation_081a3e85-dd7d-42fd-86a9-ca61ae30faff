<template>
  <a-modal
    width="600px"
    title="表单"
    :visible="visible"
    :destroyOnClose="true"
    cancelText="取消"
    :footer="null"
    @cancel="handleCancel()"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <DynamicForm ref="ruleForm" :config="formConfig" :params="formValue">
        <template #imgUpload>
          <Upload
            list-type="picture-card"
            :limitSize="2"
            fileType="IMAGE"
            :file-list.sync="formValue.imgUrl"
            :data="{
              fileType: 'IMAGE',
            }"
          />
        </template>
        <template #xmlUpload>
          <Upload
            list-type="text"
            :limitSize="4"
            fileType="XML"
            :file-list.sync="formValue.xmlUrl"
            :data="{
              fileType: 'IMAGE',
            }"
          />
        </template>
      </DynamicForm>
      <a-row type="flex" justify="center">
        <a-button type="primary" style="margin-right: 16px" @click="onClickSubmit">提交</a-button>
        <a-button @click="onClickReset">清空校验报错</a-button></a-row
      >
    </a-spin>
  </a-modal>
</template>

<script>
import Upload from '@/components/Upload';
import { formConfig, initFormValue } from '../constant';
export default {
  props: ['visible', 'detail', 'isLook'],
  components: { Upload },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          if (!this.detail) return;
          this.formValue = {
            ...initFormValue(),
            ...this.detail,
          };
        } else {
          this.formValue = initFormValue();
        }
      },
    },
  },
  data() {
    return {
      loading: false,
      formValue: initFormValue(),
      formConfig,
    };
  },
  methods: {
    onClickSubmit() {
      // 表单校验
      this.$refs.ruleForm.validate((valid) => {
        console.log(valid);
        if (valid) {
          console.log(this.formValue);
        }
      });
    },
    onClickReset() {
      // 清空校验报错
      this.$refs.ruleForm.clearValidate();
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
  },
};
</script>
