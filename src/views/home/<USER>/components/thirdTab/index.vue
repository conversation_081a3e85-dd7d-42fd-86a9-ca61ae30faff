<template>
  <BuseCrud
    ref="crud"
    title=""
    :loading="loading"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @loadData="loadData"
    @modalCancel="modalCancelHandler"
    @modalSubmit="modalSubmit"
    @modalConfirm="modalConfirmHandler"
    @rowDel="deleteRowHandler"
    @handleCreate="rowAdd"
  >
  </BuseCrud>
</template>
<script>
import { callListAPI } from '@/api/system/callBench';

export default {
  name: 'DumiDocVueIndex',
  props: ['customerNumber', 'mainUniqueId', 'visitorSecPhone'],
  data() {
    return {
      menuShow: false,
      tableData: [], //接收数据
      tableColumn: [
        //列名，列数字转文字配置
        {
          field: 'customerNumber',
          title: '访客电话',
          minWidth: 120,
        },
        {
          field: 'bridgeDuration',
          title: '通话时长',
          minWidth: 100,
        },
        {
          field: 'bridgeTime',
          title: '接通时间',
          minWidth: 160,
        },
        {
          field: 'nickName',
          title: '处理员工',
          minWidth: 130,
        },
      ],
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
    };
  },
  computed: {
    modalConfig() {
      //弹窗类配置
      return {
        menu: true,
        addBtn: false,
        delBtn: false,
        viewBtn: false,
        editBtn: false,
        formConfig: [
          {
            field: 'id',
            title: '工单号',
          },
          {
            field: 'time',
            title: '通话时间',
          },
          {
            field: 'conductor',
            title: '人员',
          },

          {
            field: 'call',
            title: '主叫号码',
          },
          {
            field: 'called',
            title: '被叫号码',
          },
          {
            field: 'testDate',
            title: '质检日期',
          },
          {
            field: 'grade',
            title: '质检分数',
          },
        ],
        customOperationTypes: [
          {
            title: '查看',
            typeName: 'goView',
            event: (row) => {
              return new Promise((resolve) => {
                this.$store.dispatch('base/cumIdFn', row.communicateId);
                this.$store.dispatch('base/mainIdFn', row.mainUniqueId);
                this.$store.dispatch('base/numberFn', row.customerNumber);
                console.log('row是哪些', row);
                this.$router.push('/telmanagerdetail');
                resolve();
              });
            },
          },
        ],
      };
    },
  },
  beforeMount() {
    this.loadData();
  },
  methods: {
    async loadData() {
      // this.$message.info(`查询参数为：${JSON.stringify(this.params)}`);
      this.loading = true;
      let page = {
        customerNumber: this.customerNumber,
        customerSecPhone: this.visitorSecPhone,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        flag: 4,
      };
      // let start = ''
      // let end = ''
      // if (isMonth) {
      //   let date = moment().format()
      //   start = moment(date).startOf('month').format("YYYY-MM-DD");
      //   end = moment(date).endOf('month').format("YYYY-MM-DD");
      //   console.log('现在时间？', date);
      //   console.log('获取这个月第一天', start);
      //   console.log('获取这个月最后一天', end);
      // }
      let [result] = await callListAPI(page);

      console.log('历史通话内容', result.data);

      this.tablePage.total = result.count;
      this.loading = false;
      this.tableData = result.data;
    },
    modalConfirmHandler(tableList) {
      if (tableList.crudOperationType == 'update') {
        console.log('编辑成功');
      } else if (tableList.crudOperationType == 'add') {
        console.log(tableList);
        console.log('添加成功');
      }
    },
    modalSubmit() {
      console.log('提交按钮');
    },
    modalCancelHandler() {
      console.log('取消按钮');
    },
    deleteRowHandler() {},
    rowAdd() {
      this.$refs.crud.switchModalView(true, 'ADD');
    },
    listAdd() {
      this.$refs.crud.switchModalView(true, 'ADD');
    },
    guestTagHandler() {
      console.log('增加客户标签');
    },
    callback(key) {
      console.log(key);
    }, //标签页切换
    callbackContent(key) {
      console.log(key);
    }, //标签页切换
    onSearch() {
      console.log('搜索');
    },

    toTelManager(row) {
      // this.$router.push('/telmanager');
      // this.$router.push('/telmanagerdetail');
      // this.$store.dispatch('base/cumIdFn', this.cu);
      // console.log('组件传值了嘛');

      console.log('看row', row);
    },
    // async getListFn(num) {
    //   let [result] = await callListAPI({ customerNumber: this.customerNumber, pageNum: num, pageSize: 10, flag: 4 });
    //   console.log(result.data);
    //   this.tableData = result.data;
    // },
  },
  watch: {
    mainUniqueId() {
      this.loadData();
    },
  },
};
</script>
<style lang="less" scoped>
//关注按钮
.blue-button {
  display: inline-block;
  margin-right: 10px;
  color: #165dff;
  cursor: pointer;
  padding: 0;
  min-width: 38px;
  height: 30px;
  line-height: 30px;
}
</style>
