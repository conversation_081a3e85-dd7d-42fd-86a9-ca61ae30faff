<template>
  <a-row type="flex" style="padding-left: 20px">
    <a-col v-for="item in myData" :key="item.id" :span="4" class="colorItem">
      <a-card :style="{ backgroundColor: item.color }" :bordered="false" class="aCard">
        <h4>{{ item.text }}</h4>
        <span>{{ item.nums }}</span>
      </a-card>
    </a-col>
  </a-row>
</template>

<script setup>
import { ref, watchEffect } from 'vue';
const props = defineProps({
  colors: Array,
  cardData: Object,
});
let cardData = ref(props.cardData);
const texts = ['升级完结工单数', '新建工单数', '完结工单数', '升级工单数'];
const myColors = props.colors ? props.colors : ['blue', 'yellow', 'green', 'red'];
let myData = ref([]);
function initData() {
  if (cardData.value) {
    let newData = [];
    for (let i = 0; i < texts.length; i++) {
      const num = Object.values(cardData.value)[i];
      newData.push({
        text: texts[i],
        nums: num,
        color: myColors[i],
      });
    }
    const res = newData[0];
    newData.splice(0, 1);
    newData.splice(3, 0, res);
    myData.value = newData;
  }
}
watchEffect(() => {
  cardData.value = props.cardData;
  initData();
});
</script>

<style lang="less" scoped>
.colorItem {
  margin: 0 42px;
}
.aCard {
  border-radius: 5px;
  text-align: center;
  h4 {
    color: white;
  }
  span {
    color: white;
    font-size: 24px;
    font-weight: 600;
  }
}
</style>
