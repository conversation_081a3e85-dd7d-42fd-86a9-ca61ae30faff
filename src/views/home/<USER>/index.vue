<template>
  <BuseCrud
    ref="crud"
    title=""
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @modalCancel="modalCancelHandler"
    @modalSubmit="modalSubmit"
    @modalConfirm="modalConfirmHandler"
    @handleReset="handleReset"
    @rowDel="deleteRowHandler"
    @rowEdit="rowEdit"
    @loadData="loadData"
    @handleCreate="rowAdd"
  >
    <template #defaultHeader>
      <span class="button-right" @click="getSelectEvent">
        <a-button @click="exportList" v-hasPermi="['system:guestList:visitorExport']">导出</a-button>
      </span>
    </template>

    <template #datePick>
      <a-range-picker v-model="params.createTime" @change="changeTime(params.createTime)" />
    </template>

    <template slot="visitorName" slot-scope="{ params }">
      <a-input
        :defaultValue="params.visitorName"
        v-model="params.visitorName"
        placeholder="请输入访客名称"
        @input="nameFn(params.visitorName)"
      ></a-input>
      <div class="tag-place">
        <a-tag color="blue" class="tag" v-if="visitorSex == 1">先生</a-tag>
        <a-tag color="blue" class="tag" v-if="visitorSex == 2">女士</a-tag>
      </div>
      <a-button class="name-button" @click="spell(params.visitorName, 1)"> 先生 </a-button>
      <a-button class="name-button" @click="spell(params.visitorName, 2)"> 女士 </a-button>
    </template>

    <template slot="address" slot-scope="{ params }">
      <a-row>
        <a-col :span="12">
          <a-cascader
            :defaultValue="address"
            v-model="params.address"
            :options="provinceList"
            placeholder="请选择所在地区"
            @change="addressChange(params)"
          />
        </a-col>
        <a-col :span="1"></a-col>
        <a-col :span="11">
          <a-input placeholder="请输入详细地址" v-model="params.fullAddress" @blur="fullAddressFn(params)"></a-input>
        </a-col>
      </a-row>
    </template>

    <template slot="guestTag">
      <div class="guest-tag">
        <a-tag class="tags" color="pink" v-for="(item, index) in orderName" :key="index">{{ item }}</a-tag>
        <a-button
          v-show="!showTags"
          type="primary"
          @click="
            guestTagHandler;
            showTags = !showTags;
          "
          ><span><a-icon type="plus-circle" class="icon-plus" /></span>添加访客标签</a-button
        >

        <Tags :orderTags="orderTags" :showTags="showTags" @custom-event="transTags" @custom-showAdd="changeAddTags" />
      </div>
    </template>
  </BuseCrud>
</template>

<script>
// import mock from './mock';
import { listAPI, addAPI, detailAPI, exportAPI } from '@/api/system/guestList';
import Tags from '@/views/home/<USER>';
import moment from 'moment';
import province from '@/views/home/<USER>/province';
import { downLoadXlsx, isHasPermi, setCustomTree } from '@/utils/system/common';
import { DictCodeAPI } from '@/api/system/dict';
export default {
  components: { Tags },
  name: 'DumiDocVueIndex',
  data() {
    return {
      man: false, //先生
      lady: false, //女士
      gen: 0, //这个是给重复输入避免丢失先生/女士准备的
      visitorSex: 0, //后端传入用于判断性别
      startTime: '', //日期
      endTime: '', //结束日期
      showTags: false, //是否展示Tags的select框;同时控制按钮
      orderTags: [], //tag数组，要求拼接，那就拼接吧，不传数组了
      orderName: [], //没想到值和名字是分开的
      visitorInfoId: '', //某一个访客的id
      provinceList: [], //省份表
      visitorName: '', //访客名称，用来给新增和编辑用的
      address: [], //地址
      fullAddress: '', //详细地址，不要忘了提交置空以及取消置空
      menuShow: true,
      tableData: [],
      tableColumn: [
        { type: 'checkbox' },
        //列名，列数字转文字配置
        {
          field: 'visitorName',
          title: '访客名称',
          minWidth: 100,
        },
        {
          field: 'realName',
          title: '真实姓名',
          minWidth: 100,
        },
        {
          field: 'visitorPhone',
          title: '访客电话-1',
          minWidth: 115,
        },
        {
          field: 'visitorSecPhone',
          title: '访客电话-2',
          minWidth: 115,
        },
        {
          field: 'createByName',
          title: '创建人',
          minWidth: 100,
        },
        {
          field: 'visitorFrom',
          title: '客户来源',
          minWidth: 100,
          formatter({ cellValue }) {
            if (cellValue == 'east') {
              return '东土大唐';
            } else {
              return cellValue;
            }
          },
        },
        {
          field: 'createTime',
          title: '创建时间',
          minWidth: 160,
        },
        {
          field: 'visitorGrade',
          title: '客户等级',
          minWidth: 110,
        },
      ],
      //筛选以及获取每页的数据
      params: {
        visitorOrRealName: '',
        visitorFrom: '',
        startTime: '',
        endTime: '',
        visitorGrade: '',
        createBy: '',
        visitorPhone: '',
      }, //筛选参数

      loading: false,
      tablePage: { total: 20, currentPage: 1, pageSize: 10 },

      //字典部分
      visitorGradeSelect: [],
      visitorFromSelect: [],
      createBySelect: [],
    };
  },
  computed: {
    filterOptions() {
      //筛选类配置
      return {
        config: [
          {
            field: 'visitorOrRealName',
            title: '名称或姓名',
          },
          {
            field: 'visitorFrom',
            title: '访客来源',
            element: 'a-select',
            props: {
              options: this.visitorFromSelect,
            },
          },
          {
            field: 'createTime',
            title: '创建时间',
            element: 'slot',
            slotName: 'datePick',
          },
          {
            field: 'visitorGrade',
            title: '客户等级',
            element: 'a-select',
            props: {
              options: this.visitorGradeSelect,
            },
          },
          {
            field: 'createBy',
            title: '创建人',
            element: 'a-tree-select',
            props: {
              treeData: this.createBySelect,
            },
          },
          {
            field: 'visitorPhone',
            title: '访客电话',
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      //弹窗类配置
      return {
        formConfig: [
          {
            field: 'visitorName',
            title: '访客名称',
            element: 'slot',
            slotName: 'visitorName',
            rules: [{ required: true, message: '请填写访客名称' }],
          },
          {
            field: 'realName',
            title: '真实姓名',
          },
          {
            field: 'visitorPhone',
            title: '访客电话',
            rules: [{ required: true, message: '请输入正确的电话号码', pattern: /^\d+$/ }],
          },
          {
            field: 'visitorSecPhone',
            title: '访客电话-2',
          },
          {
            field: 'address',
            title: '访客地址',
            element: 'slot',
            slotName: 'address',
          },
          {
            field: 'wechatId',
            title: '微信号',
          },
          {
            field: 'mail',
            title: '邮箱',
            rules: [{ pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '请输入正确的邮箱' }],
          },
          {
            field: 'visitorGrade',
            title: '客户等级',
            element: 'a-select',
            props: { options: this.visitorGradeSelect },
          },
          {
            field: 'visitorFrom',
            title: '访客来源',
            element: 'a-select',
            props: { options: this.visitorFromSelect },
          },
          {
            field: 'visitorTag',
            title: '访客标签',
            colProps: {
              // span: 12,
            },
            itemProps: {},
            element: 'slot',
            slotName: 'guestTag',
          },
          {
            field: 'remark',
            title: '备注',
            element: 'a-textarea',
          },
        ],
        menuTitle: '操作',
        addBtn: isHasPermi(['system:guestList:visitorAdd']),
        delBtn: false,
        viewBtn: false,
        editBtn: isHasPermi(['system:guestList:visitorDetail']),
        //自定义操作
        // customOperationTypes: [
        //   {
        //     //定义操作类型名称
        //     typeName: 'apply',
        //     title: '自定义操作',
        //     //该操作下弹窗对应内容插槽
        //     slotName: 'apply',
        //     event: () => {
        //       console.log('自定义');
        //       return Promise.resolve('你好');
        //     },
        //   },
        // ],
      };
    },
  },
  beforeMount() {
    DictCodeAPI('visitor_from').then((res) => {
      this.visitorFromSelect = res;
    });
    DictCodeAPI('visitor_grade').then((res) => {
      this.visitorGradeSelect = res;
    });
    this.$store.dispatch('base/GetUserList', { nickName: '' }).then((res) => {
      let test = [];
      this.createBySelect = setCustomTree(res);
      Object.entries(this.createBySelect).map(([, arr]) => {
        let child = [];
        if (arr.children && arr.children.length > 0) {
          child = arr.children.map(({ value, valueName }) => ({
            value,
            label: valueName,
          }));
        }
        test.push({
          value: arr.value,
          label: arr.valueName,
          children: child,
        });
      });
      this.createBySelect = test;
    });
  },
  mounted() {
    this.loadData();
    this.getProvince();
  },
  methods: {
    async loadData() {
      this.loading = true;
      let pageDetail = { pageNum: Number(this.tablePage.currentPage), pageNumSize: Number(this.tablePage.pageSize) };
      this.params.startTime = this.startTime;
      this.params.endTime = this.endTime;
      let [test] = await listAPI({ ...this.params, ...pageDetail });
      console.log(test.data);
      // this.$message.info(`查询参数为：${JSON.stringify(this.params)}`);

      // const result = await new Promise((resolve) => {
      //   setTimeout(() => {
      //     resolve(mock);
      //   }, 500);
      // });

      this.loading = false;
      test.data = test.data.map((item) => {
        return {
          ...item,
          visitorInfoId: item.visitorInfoId,
          createTime: moment(item.createTime).format('YYYY-MM-DD HH:mm:ss'),
        };
      });
      this.tableData = test.data;
      this.tablePage.total = test.count;
    },
    async exportList() {
      let [result] = await exportAPI(this.params);
      downLoadXlsx(new Blob([result]), '访客列表导出.xlsx');
    },
    getProvince() {
      // let str = province.toString();
      // let obj = Object.assign({}, str);
      let obj = JSON.parse(JSON.stringify(province));
      console.log(obj);
      console.log(Object.entries(obj)); //测试
      const result = Object.entries(obj).map(([province, cities]) => ({
        province: province,
        cities: Object.entries(cities).map(([city, districts]) => ({
          city,
          districts,
        })),
      }));
      console.log(result);
      this.provinceList = this.transformData(result);
      console.log(this.provinceList);
    },
    transformData(data) {
      //把数据转换成级联可识别样式
      return data.map((province) => {
        return {
          label: province.province,
          value: province.province,
          children: province.cities.map((city) => {
            return {
              label: city.city,
              value: city.city,
              children: city.districts.map((district) => {
                return {
                  label: district,
                  value: district,
                };
              }),
            };
          }),
        };
      });
    },
    // 获取访客列表，要求传入参数pageNum和pageSize
    async getListFn() {
      listAPI;
    },
    changeTime(value) {
      const [startDate, endDate] = value;
      this.startTime = moment(startDate).format('YYYY-MM-DD HH:mm:ss');
      this.endTime = moment(endDate).format('YYYY-MM-DD HH:mm:ss');
    },
    //重置按钮，用于消除template的时间
    // handleReset(params) {
    //   params = '';
    //   this.startTime = '';
    //   this.endTime = '';
    // },
    async modalConfirmHandler(tableList) {
      //provinceCode
      //visitorInfoId
      //cityCode

      delete tableList.createByName;
      try {
        console.log('adress地址', this.address);
        tableList.address = this.address.join(',');
      } catch (err) {
        console.log('这里没有改变地址尝试保存');
      }
      console.log('全地址', this.fullAddress);
      tableList.fullAddress = this.fullAddress;
      try {
        tableList.visitorTag = this.orderTags.join(',');
      } catch (err) {
        console.log('这里没有改变改变标签尝试保存');
      }
      tableList.visitorName = this.visitorName;
      tableList.visitorSex = this.visitorSex;
      console.log(tableList);
      let result = await addAPI(tableList);
      console.log(result);
      console.log(tableList.visitorTag);

      //重置
      this.orderTags = [];
      this.visitorSex = 0;
      this.visitorName = '';
      this.address = [];
      this.fullAddress = '';

      this.loadData(); //刷新
    },
    modalSubmit() {
      console.log('提交按钮');
    },
    modalCancelHandler() {
      this.orderTags = [];
      this.visitorSex = 0;
      this.visitorName = '';
      this.address = [];
      this.fullAddress = '';
      console.log('取消按钮');
    },
    deleteRowHandler() {},
    rowAdd() {
      this.$refs.crud.switchModalView(true, 'ADD');
    },
    async rowEdit(row) {
      //改成了id
      let id = { visitorInfoId: row.visitorInfoId };
      let [result] = await detailAPI(id);
      console.log('结果有问题？', result);
      if (result.data.visitorSex == 0) {
        row.visitorName = result.data.visitorName;
        console.log('执行一句');
      } else if (result.data.visitorSex != 0) {
        console.log('执行二具');
        row.visitorName = result.data.visitorName.replace(/先生|女士/g, '');
      }
      row.mail = result.data.mail;
      row.realName = result.data.realName;

      row.visitorFrom = result.data.visitorFrom;
      row.visitorGrade = result.data.visitorGrade;
      row.wechatId = result.data.wechatId;
      row.remark = result.data.remark;

      row.fullAddress = result.data.fullAddress;
      this.fullAddress = result.data.fullAddress; //初始值
      try {
        row.address = result.data.address.split(',');
        console.log('看看地址数组', row.address);
        this.address = result.data.address.split(','); //初始值

        this.orderTags = result.data.visitorTag.split(',');
        console.log(this.orderTags);
      } catch (err) {
        console.log(err);
      }

      this.$refs.crud.switchModalView(true, 'UPDATE', row);
      this.visitorName = result.data.visitorName;
      this.visitorSex = result.data.visitorSex;

      console.log(row);
    },
    getSelectEvent() {
      console.log('导出');
      // let selectRecords = this.$refs.crud.getCheckboxRecords();
      // console.log(selectRecords);
    },
    guestTagHandler() {
      console.log('增加客户标签');
    },
    spell(name, gen) {
      if (gen == 1) {
        //点击先生时候
        if (!this.man) {
          //初次点击，以及切换
          this.visitorName = name + '先生';
          this.lady = false;
          this.man = !this.man;
          this.visitorSex = 1;
        } else if (this.man) {
          //重复点击同一性别，也就是取消
          this.visitorName = name;
          this.man = !this.man;
          this.visitorSex = 0;
        }
      } else if (gen == 2) {
        if (!this.lady) {
          this.visitorName = name + '女士';
          this.man = false;
          this.lady = !this.lady;
          this.visitorSex = 2;
        } else if (this.lady) {
          this.visitorName = name;
          this.lady = !this.lady;
          this.visitorSex = 0;
        }
      } else if (gen == 3) {
        //gen == 3代表不选，我就点着玩
        if (this.lady) {
          this.visitorName = name + '女士';
          this.visitorSex = 2;
        } else if (this.man) {
          this.visitorName = name + '先生';
          this.visitorSex = 1;
        }
      }
      console.log('现在visitorSex' + this.visitorSex);
      console.log(this.visitorName);
    },
    nameFn(name) {
      this.visitorName = name;
      if (this.man || this.lady) {
        // 在有性别选择了的时候才会执行
        this.spell(name, 3);
      }
      console.log(this.visitorName);
    },
    //地址变动
    addressChange(value) {
      console.log(value);
      this.address = value.address;
    },
    //详细地址变动
    fullAddressFn(value) {
      // this.fullAddress = value;
      console.log(value);
      this.fullAddress = value.fullAddress;
    },

    //tag子组件
    async transTags(value) {
      this.orderName = [];
      this.orderTags = [];
      this.orderTags = value;
      console.log(value);
      let result = await DictCodeAPI('visitor_tag');
      console.log(result);
      try {
        this.orderName = value.map((value) => {
          const item = result.find((obj) => obj.value == value);
          return item ? item.label : null;
        });
        console.log(this.orderName);
      } catch (err) {
        console.log('看起来你没输入tag');
      }
    },
    changeAddTags(value) {
      this.showTags = !value;
    },
    //筛选重置按钮
    handleReset() {
      this.startTime = '';
      this.endTime = '';
      this.tablePage.currentPage = 1;
      this.params = {
        visitorOrRealName: '',
        visitorFrom: '',
        startTime: '',
        endTime: '',
        visitorGrade: '',
        createBy: '',
      };
      this.loadData();
    },
  },
};
</script>

<style lang="less" scoped>
.button-right {
  margin-right: 16px;
}
.tag-place {
  display: flex;
  justify-content: flex-end;
  top: -36px;
  position: relative;
  .tag {
    position: absolute;
    height: 32px;
    line-height: 32px;
  }
}
.name-button {
  margin-right: 16px;
}
.guest-tag {
  text-align: left;
  .tags {
    white-space: nowrap;
    line-height: 32px;
    height: 32px;
  }
}
.icon-plus {
  margin-right: 4px;
}
</style>
