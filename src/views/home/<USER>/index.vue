<template>
  <div style="display: inline-block">
    <a-button type="primary" @click="listAdd()">创建工单</a-button>
    <div>
      <a-modal title="提示" :visible="modalSeen" @ok="modalOK" @cancel="modalCancel">
        选择该优先级会触发SLA目标，请确认
      </a-modal>
    </div>
    <BuseCrud
      v-show="false"
      ref="crud"
      :modalConfig="modalConfig"
      @modalCancel="modalCancelHandler"
      @modalConfirm="modalConfirmHandler"
      @modalSubmit="modalSubmit"
      @handleCreate="rowAdd"
    >
      <template slot="orderHandler" slot-scope="{ params }">
        <a-tree-select
          @focus="loadUserList"
          placeholder="请选择受理人员"
          v-model="params.orderHandler"
          :treeData="filterHandler"
          show-search
          :disabled="autoDisable || editFlag"
          labelInValue
          :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
          :getPopupContainer="(triggerNode) => triggerNode.parentNode"
        >
          <template slot="myTitle" slot-scope="item">
            <StatuNode :newNodes="item" />
          </template>
        </a-tree-select>
      </template>
      <template slot="orContent" slot-scope="{ params }">
        <div class="orderContent">
          <AddReply :orderSort="nowCategoryID" @selectedReply="handleSelect" />
          <Editor
            :value="contentInput"
            :addFileList="addFileList"
            v-model="params.orderContent"
            @input="giveParam(params.orderContent)"
            @addFile="addFile"
          />
        </div>
      </template>

      <template slot="selectOr" slot-scope="{ params }">
        <a-select
          :getPopupContainer="(triggerNode) => triggerNode.parentNode"
          placeholder="请在选择工单分类之后选择工单模板"
          @select="handleChange"
          v-model="modChoice"
          :disabled="editFlag"
        >
          <a-select-option v-for="(item, index) in exList || []" :key="index" :value="index">
            {{ item.templateName }}
          </a-select-option>
        </a-select>

        <div class="templateFields">
          <a-button
            type="primary"
            v-if="showMod"
            @click="
              showMod = false;
              modChoice = undefined;
            "
            ><a-icon type="minus-circle" /> 取消模板选择</a-button
          >
          <a-form-model ref="templateForm" :model="tempForm" :rules="tempRules">
            <template v-for="(item, index) in choiceExList.fields || []">
              <a-form-model-item
                v-if="item !== undefined && showMod && item.fieldType == '1'"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 17 }"
                :label="item.fieldName"
                :key="index"
                :prop="item.templateFieldId"
                ><a-input
                  :defaultValue="item.value ? item.value : item.defaultValue"
                  v-model="tempForm[item.templateFieldId]"
                ></a-input>
              </a-form-model-item>

              <a-form-model-item
                v-if="item !== undefined && showMod && item.fieldType == '2'"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 17 }"
                :label="item.fieldName"
                :key="index"
                :prop="item.templateFieldId"
              >
                <a-select
                  @focus="getModSelect(item.fieldDataKey)"
                  :defaultValue="item.value ? item.value : item.defaultValue"
                  v-model="tempForm[item.templateFieldId]"
                >
                  <a-select-option v-for="node in item.options" :key="node.value">
                    {{ node.label }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>

              <a-form-model-item
                v-if="item !== undefined && showMod && item.fieldType == '3'"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 17 }"
                :label="item.fieldName"
                :key="index"
                :prop="item.templateFieldId"
                ><a-date-picker
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                  style="width: 100%"
                  :defaultValue="item.value ? item.value : item.defaultValue"
                  v-model="tempForm[item.templateFieldId]"
                ></a-date-picker>
              </a-form-model-item>

              <a-form-model-item
                v-if="item !== undefined && showMod && item.fieldType == '4'"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 17 }"
                :label="item.fieldName"
                :key="index"
                :prop="item.templateFieldId"
                ><Editor
                  :defaultValue="item.value ? item.value : item.defaultValue"
                  v-model="tempForm[item.templateFieldId]"
                  :uploadVisible="false"
                />
              </a-form-model-item>

              <a-form-model-item
                v-if="item !== undefined && showMod && item.fieldType == '5'"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 17 }"
                :label="item.fieldName"
                :key="index"
                :prop="item.templateFieldId"
              >
                <a-select
                  mode="multiple"
                  style="width: 100%"
                  placeholder="请输入"
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                  @focus="getModSecSelect(item.fieldDataKey, tempForm[item.templateFieldId])"
                  v-model="tempForm[item.templateFieldId]"
                  :defaultValue="item.value ? item.value : item.defaultValue"
                >
                  <a-select-option v-for="node in item.options" :key="node.value">
                    {{ node.label }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>

              <a-form-model-item
                v-if="item !== undefined && showMod && item.fieldType == '6'"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 17 }"
                :label="item.fieldName"
                :key="index"
                :prop="item.templateFieldId"
              >
                <a-input-number
                  :defaultValue="item.value ? item.value : item.defaultValue"
                  v-model="tempForm[item.templateFieldId]"
                />
              </a-form-model-item>

              <a-form-model-item
                v-if="item !== undefined && showMod && item.fieldType == '7'"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 17 }"
                :label="item.fieldName"
                :key="index"
                :prop="item.templateFieldId"
              >
                <a-input-number
                  :defaultValue="item.value ? item.value : item.defaultValue"
                  v-model="tempForm[item.templateFieldId]"
                  :formatter="(value) => `${value}%`"
                  :parser="(value) => value.replace('%', '')"
                />
              </a-form-model-item>
            </template>
          </a-form-model>
        </div>
      </template>
      <template slot="newGuest" slot-scope="{ params }">
        <a-button type="primary" @click="showGuestfn(params)"
          ><span>
            <a-icon v-if="showGuest == false" type="plus-circle" class="icon-plus" />
            <a-icon v-if="showGuest == true" type="minus-circle" class="icon-plus" /> </span
          ><span v-if="showGuest == true">取消</span>创建新访客</a-button
        >
        <a-form-model ref="visitorForm" :model="params" v-show="showGuest" :rules="visitorRules" class="visitorForm">
          <a-form-model-item
            label="访客名称"
            prop="visitorName"
            key="visitorName"
            :label-col="{ span: 5 }"
            :wrapper-col="{ span: 17 }"
          >
            <a-input
              v-model="params.visitorName"
              placeholder="请输入访客名称"
              @blur="nameFn(params.visitorName)"
              @change="visitorName = params.visitorName"
            />
            <div class="tag-place">
              <a-tag color="blue" class="tag" v-if="man">先生</a-tag>
              <a-tag color="blue" class="tag" v-if="lady">女士</a-tag>
            </div>
            <a-button class="name-button" size="small" @click="spell(params.visitorName, 1)"> 先生 </a-button>
            <a-button class="name-button" size="small" @click="spell(params.visitorName, 2)"> 女士 </a-button>
          </a-form-model-item>
          <a-form-model-item
            label="访客电话"
            prop="visitorPhone"
            key="visitorPhone"
            :label-col="{ span: 5 }"
            :wrapper-col="{ span: 17 }"
          >
            <a-input v-model="params.visitorPhone" @change="visitorPhone = params.visitorPhone" />
          </a-form-model-item>
        </a-form-model>
      </template>

      <template slot="flowType">
        <a-radio-group v-model="flowRadio" @change="choiceManFlow" :disabled="editFlag">
          <a-radio value="1"> 人工流转 </a-radio>
          <a-radio value="2" v-if="enabledFlag == '1'" :disabled="autoFlag"> 自动分配 </a-radio>
          <a-radio value="3" v-if="enabledFlag == '1'"> 工单池流转 </a-radio>
        </a-radio-group>
      </template>

      <!-- <template slot="guestName" slot-scope="{ params }">
      </template> -->

      <template slot="guestTag">
        <div class="guest-tag">
          <a-tag class="tags" color="pink" v-for="(item, index) in orderName" :key="index">{{ item }}</a-tag>
          <a-button v-show="!showTags" type="primary" @click="showTags = !showTags"
            ><span><a-icon type="plus-circle" class="icon-plus" /></span>添加工单标签</a-button
          >

          <Tags :showTags="showTags" @custom-event="transTags" @custom-showAdd="changeAddTags" />
        </div>
      </template>
      <template slot="phoneHistory" slot-scope="{ params }">
        <div>
          <a-input
            v-if="!showHistoryCall && showCall"
            v-model="params.phoneNumber"
            placeholder="请输入手机号"
            :class="[showCall ? 'callInput' : '']"
          />
          <a-select
            v-else
            placeholder="从通话记录中选择手机号"
            @search="searchCall"
            @change="selectCall"
            show-search
            class="callInput"
            v-model="params.phoneNumber"
            option-label-prop="label"
            :allow-clear="true"
            :filter-option="false"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
          >
            <a-select-option
              v-for="(item, index) in filterCallList"
              :value="item.mainUniqueId"
              :key="item.mainUniqueId"
              class="optionItem"
              :aria-label="item.customerNumber"
            >
              <div class="selectNode">
                <span>{{ item.clientName }}</span>
                <span>{{ item.customerNumber }}</span>
                <span>{{ item.startTime }}</span>
              </div>
            </a-select-option>
          </a-select>
          <a v-if="showCall" @click="getTodayCall">{{ showHistoryCall ? '取消绑定' : '绑定通话记录' }}</a>
        </div>
      </template>
      <template slot="vehicle" slot-scope="{ params }">
        <a-row>
          <a-col :span="8">
            <a-input v-model="params.vehicleBrand" placeholder="请输入品牌"></a-input>
          </a-col>
          <a-col :span="8">
            <a-input v-model="params.vehicleModel" placeholder="请输入车型"></a-input>
          </a-col>
          <a-col :span="8">
            <!-- <a-input v-model="params.vehicleYear" placeholder="请选择年份"></a-input> -->
            <BuseRangePicker
              type="year"
              v-model="params.vehicleYear"
              format="YYYY"
              value-format="YYYY"
              placeholder="请选择年份"
              :needShowSecondPicker="() => false"
            ></BuseRangePicker>
          </a-col>
        </a-row>
      </template>
      <!-- <template slot="stationId" slot-scope="{ params }">
        <a-select
          v-model="params.stationId"
          :filter-option="
            (input, option) => {
              return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            }
          "
          show-search
          allowClear
          @popupScroll="handlePopupScroll"
        >
          <a-select-option v-for="item in stationOptions" :key="item.value" :value="item.value">{{
            item.label
          }}</a-select-option>
        </a-select>
      </template> -->
    </BuseCrud>
    <a-modal :visible="showFailModal" @ok="resendOrder" @cancel="showFailModal = false" title="提示信息">
      <template slot="footer">
        <a-button @click="showFailModal = false"> 我知道了 </a-button>
        <a-button type="primary" :loading="modalLoading" @click="resendOrder"> 重试 </a-button>
      </template>
      <div class="fail-modal">
        <div><a-icon type="exclamation-circle" class="fail-modal-icon" /></div>
        <div>
          <h3>{{ failMsg }}</h3>
          <p>已生成客服工单，但能源维保通{{ failMsg }}，请点击重试或稍后在详情中重新提交。</p>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script src="./defineComponent.vue.js"></script>

<style lang="less" scoped>
.addBtn {
  position: absolute;
  top: 120px;
  left: 108px;
}

//tab大盒子
.content-tab-box {
  margin-top: 48px;
}
.tab1-box {
  padding-left: 80px;
  padding-right: 80px;
  //访客详情大标题
  .line-b-title {
    border-left: 4px solid #1b58f4;
    padding-left: 10px; /* 可选，用于控制文字与边框的间距 */
    text-align: left;
    color: color rgb(51, 51, 51);
    font-weight: bold;
  }
  //访客详情内容
  .line-b {
    height: 48px;
    line-height: 48px;
    text-align: left;
    color: rgb(170, 170, 170);
    span {
      color: rgb(51, 51, 51);
    }
  }
}
//紧急字体
.exgency {
  color: red;
}
//访客名称
.tag-place {
  display: flex;
  justify-content: flex-end;
  top: -36px;
  position: relative;
  .tag {
    position: absolute;
    height: 32px;
    line-height: 32px;
  }
}
.name-button {
  margin-right: 16px;
}
.guest-tag {
  //标签样式
  // display: flex;
  // justify-content: space-around;
  .tags {
    white-space: nowrap;
    line-height: 32px;
    height: 32px;
  }
}

.icon-plus {
  margin-right: 4px;
}
.button-flex {
  margin-top: 16px;
  display: flex;
  justify-content: space-around;
}
.optionItem span,
.selectNode span {
  margin: 0 4px;
}
.callInput {
  width: 400px;
  margin-right: 16px;
}
/deep/ .quillWrapper .ql-snow.ql-toolbar {
  display: flex;
}
.templateFields {
  /deep/ .ant-form-item {
    margin-bottom: 4px;
    display: flex;
  }
  /deep/ .ant-form-item label {
    white-space: break-spaces;
    text-align: justify;
    display: inline-flex;
    font-size: 12px;
    color: #999;
  }
  /deep/ .quillWrapper .ql-toolbar.ql-snow .ql-formats {
    transform: scale(0.7);
    display: inline-flex;
    margin: 0 -6px;
  }
  /deep/ .quillWrapper .ql-toolbar.ql-snow .ql-formats:nth-child(1) {
    margin: 0 -20px;
  }
  /deep/ .quillWrapper .ql-toolbar.ql-snow .ql-formats:nth-child(2) {
    margin: 0 -20px;
  }
  /deep/ .quillWrapper .ql-toolbar.ql-snow .ql-formats:nth-child(3) {
    margin: 0 -14px;
  }
}
/deep/ .ant-form-item-label {
  text-align: left;
  width: fit-content;
  display: inline-block;
}
.visitorForm {
  /deep/ .ant-form-item-label > label {
    color: #999;
    font-size: 12px;
  }
}
.templateFields /deep/ .ant-form-item label {
  display: inline-block;
}
/deep/ .ant-form-item-control {
  text-align: left;
}
.replyMark {
  margin: 24px 0 8px 0;
}
.orderContent {
  padding-top: 24px;
  /deep/ .replyLine {
    margin-bottom: 4px;
  }
  /deep/ .quillWrapper .ql-toolbar.ql-snow .ql-formats {
    transform: scale(0.9);
    display: inline-flex;
    margin: 0 -1px;
  }
}
.fail-modal {
  display: flex;
  &-icon {
    font-size: 20px;
    color: red;
    margin-right: 10px;
  }
}
</style>
