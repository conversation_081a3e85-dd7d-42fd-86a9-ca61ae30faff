<template>
  <div class="border" v-show="showTags">
    <a-select
      mode="multiple"
      style="width: 100%"
      placeholder="请选择标签"
      :defaultValue="orderTags"
      @change="handleChange"
      @blur="showAdd(true)"
      @focus="showAdd(false)"
    >
      <a-select-option v-for="(item, index) in list" :key="index" :value="item.value">
        {{ item.label }}
      </a-select-option>
    </a-select>
    <a-button type="primary" @click="showAdd(true)">隐藏标签选择框</a-button>
  </div>
</template>
<script>
import { DictCodeAPI } from '@/api/system/dict';
export default {
  props: ['showTags', 'orderTags'],
  data() {
    return {
      list: [],
    };
  },
  methods: {
    handleChange(value) {
      this.$emit('custom-event', value); // 触发自定义事件，并将值作为参数
    },
    showAdd(value) {
      this.$emit('custom-showAdd', value);
    },
    async getTags() {
      if (this.$route.path == '/guestlist') {
        let result = await DictCodeAPI('visitor_tag');
        this.list = result;
        console.log('tag', result);
      } else {
        let result = await DictCodeAPI('order_tag');
        this.list = result;
        console.log('tag', result);
      }
    },
  },
  beforeMount() {
    this.getTags();
    this.handleChange(this.orderTags);
    console.log('查明路由', this.$route.path);
  },
  mounted() {},
  computed: {},
};
</script>
<style lang="less" scoped>
.border {
  margin-left: 8px;
  margin-right: 8px;
}
</style>
