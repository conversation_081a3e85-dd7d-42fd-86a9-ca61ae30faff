<template>
  <a-card class="fa-box">
    <a-row>
      <a-col :span="10" class="big-box">
        <div>
          <div class="line-b-title"><a-icon type="idcard" /> 通话详情</div>
          <div>
            <a-descriptions bordered class="box-distance labelLeft">
              <a-descriptions-item label="通话类型" :span="4">
                <div>{{ mainIdObj.callType + ' ' + mainIdObj.customerNumber }}</div>
              </a-descriptions-item>
              <a-descriptions-item label="归属地" :span="4">
                <div>{{ mainIdObj.customerProvince }} {{ mainIdObj.customerCity }}</div>
              </a-descriptions-item>
              <a-descriptions-item label="总通话时长" :span="4">
                <div>{{ mainIdObj.bridgeDurationStr }}</div>
              </a-descriptions-item>
              <a-descriptions-item label="挂断方" :span="4">
                <div>{{ mainIdObj.endReason }}</div>
              </a-descriptions-item>
              <a-descriptions-item label="通话录音" :span="4">
                <div>
                  <audio
                    style="width: 250px"
                    ref="audioPlayer"
                    :src="voice"
                    controls="controls"
                    @click="playAudio"
                  ></audio>
                </div>
              </a-descriptions-item>
            </a-descriptions>
          </div>

          <div>
            <div class="title-flex">
              <div v-if="orderIds">
                <div class="line-b-title"><a-icon type="snippets" />关联工单：</div>
                <div class="order-id">
                  <a class="order-son" v-for="(item, index) in orderIds" :key="index" @click="goDetail(item)"
                    >{{ item }}<span>{{ arrTime[index] }}</span
                    ><a-tag :color="transformStatu(labelStatus[index])">{{ labelStatus[index] }}</a-tag></a
                  >
                </div>
                <a-drawer
                  width="80vw"
                  title="工单详情"
                  placement="right"
                  :closable="true"
                  :visible="visible"
                  @close="onClose"
                >
                  <WorkDetail :orderId="orderId" />
                </a-drawer>
              </div>
            </div>
          </div>

          <div>
            <div class="line-b-title">通话日志</div>
            <div>
              <template>
                <a-timeline style="margin-top: 24px">
                  <a-timeline-item>接听状态： {{ mainIdObj.status }}</a-timeline-item>
                  <a-timeline-item
                    ><span class="point">访客</span> 进入IVR {{ mainIdObj.routerStartTime }}</a-timeline-item
                  >
                  <a-timeline-item>进队列（呼叫技能组） {{ mainIdObj.joinQueueTime }}</a-timeline-item>
                  <a-timeline-item>分配到坐席（呼叫技能组） {{ mainIdObj.startTime }}</a-timeline-item>
                  <a-timeline-item
                    >坐席 <span class="point"> {{ mainIdObj.clientName }} </span> 响铃
                    {{ mainIdObj.clientRingingTime }}</a-timeline-item
                  >
                  <a-timeline-item
                    >坐席 <span class="point"> {{ mainIdObj.clientName }} </span> 通话建立
                    {{ mainIdObj.clientOffhookTime }}</a-timeline-item
                  >
                  <a-timeline-item
                    >坐席 <span class="point"> {{ mainIdObj.clientName }} </span> 通话结束
                    {{ mainIdObj.endTime }}</a-timeline-item
                  >
                  <a-timeline-item v-if="mainIdObj.investigationTime">
                    发起满意度流程 {{ mainIdObj.investigationTime }}
                  </a-timeline-item>
                  <a-timeline-item v-if="!mainIdObj.investigationTime"> 未发起满意度调查 </a-timeline-item>

                  <a-timeline-item v-if="mainIdObj.investigationTime && mainIdObj.investigationKey">
                    满意度调查结果：{{ satisfaction }}
                  </a-timeline-item>
                  <a-timeline-item v-if="mainIdObj.investigationTime && !mainIdObj.investigationKey">
                    未进行满意度评价
                  </a-timeline-item>

                  <!-- <a-timeline-item v-for="(item, index) in flowList" :key="index">{{ item }}</a-timeline-item> -->
                </a-timeline>
              </template>
            </div>
          </div>
        </div>
      </a-col>
      <a-col :span="14" class="big-box">
        <div>
          <div>
            <a-tabs default-active-key="1" @change="callback">
              <a-tab-pane key="1" tab="客户详情">
                <div class="line-b">
                  <div class="line">
                    访客姓名<span>{{ detail.visitorName }}</span>
                  </div>
                  <div class="line">
                    真实姓名<span>{{ detail.realName }}</span>
                  </div>
                  <div class="line">
                    访客电话<span>{{ detail.visitorPhone }}</span>
                  </div>
                  <div class="line">
                    访客电话-2<span>{{ detail.visitorSecPhone }}</span>
                  </div>
                  <div class="line">
                    微信<span>{{ detail.wechatId }}</span>
                  </div>
                  <div class="line">
                    邮箱<span>{{ detail.mail }}</span>
                  </div>
                  <div class="line">
                    地址<span>{{ detail.address }} {{ detail.fullAddress }}</span>
                  </div>
                  <div class="line">
                    备注
                    <div style="margin-top: 8px">
                      <a-textarea v-model="detail.remark" :rows="4" disabled />
                    </div>
                  </div>
                  <div class="line" style="margin-top: 24px">
                    客户标签<a-tag color="pink" v-for="(item, index) in tagName" :key="index">{{ item }}</a-tag>
                  </div>
                </div>
              </a-tab-pane>
              <a-tab-pane key="2" tab="服务质检" force-render>
                <ServeMain :checkStatus="checkStatus" :checkId="checkId" />
              </a-tab-pane>
            </a-tabs>
          </div>
        </div>
      </a-col>
    </a-row>
  </a-card>
</template>

<script>
import ServeMain from './components/serveMain';
import { phoneDetail, detailAPI, checkDetailAPI, voiceFn } from '@/api/system/telManager';
import { valueFindFn } from '@/utils/system/common';
import { DictCodeAPI } from '@/api/system/dict';
import WorkDetail from '@/views/workorder/detail';
export default {
  components: { ServeMain, WorkDetail },
  data() {
    return {
      mainIdObj: {},
      satisfaction: '',
      flowList: [],
      detail: {},
      tagName: [],
      checkStatus: '', //质检状态
      visible: false, //弹窗显示
      orderIds: [],
      arrStatus: [],
      labelStatus: [], //字典找汉字
      arrTime: [], //store的时间们
      orderId: '', //选中id
      checkId: '', //复检id
      voice: '',
      manName: '', //满意度调查结果
    };
  },
  methods: {
    callback(key) {
      console.log(key);
    },
    async getId() {
      console.log('看看数据传过来没有id', this.$store.state.base.cumId);
      console.log('看看数据传过来没有id', this.$store.state.base.mainUniqueId);
      console.log('看看数据传过来没有电话', this.$store.state.base.customerNumber);
      console.log('检查orderIDS', this.$store.state.base.orderId);
      console.log('状态', this.$store.state.base.arrStatus);
      let mainId = this.$store.state.base.mainUniqueId;
      let phone = this.$store.state.base.customerNumber;
      this.orderIds = this.$store.state.base.orderId;
      this.arrStatus = this.$store.state.base.arrStatus;
      this.arrTime = this.$store.state.base.arrTime;

      let dict = await DictCodeAPI('order_status');
      console.log('==========dict', dict);
      let arrStatus = this.arrStatus.join(',');
      this.labelStatus = valueFindFn(dict, arrStatus).split(',');
      console.log('---labelStatus---', this.labelStatus);

      let [result] = await phoneDetail(mainId);
      console.log('mainId结果', result);
      this.mainIdObj = result.data;

      let satisList = await DictCodeAPI('investigation_result');
      let stringKey = result.data.investigationKey.toString();
      this.satisfaction = valueFindFn(satisList, stringKey);
      console.log('=======满意度', this.satisfaction);
      try {
        this.flowList = result.data.remark.split(';');
        this.flowList.pop(); //去掉最后一个多余的空

        // let result = await DictCodeAPI('')满意度
      } catch (err) {
        console.log('流程发过来的有问题');
      }

      let [detailResult] = await detailAPI({ visitorPhone: phone });
      this.detail = detailResult.data;
      console.log('看看访客详情', this.detail);

      try {
        let tags = await DictCodeAPI('visitor_tag'); //标签字典部分
        console.log('tags', tags);
        this.tagName = valueFindFn(tags, detailResult.data.visitorTag).split(',');
        console.log(this.tagName);
      } catch (err) {
        console.log('访客详情没有标签');
      }
    },
    async getStatus() {
      let [result] = await checkDetailAPI(this.$store.state.base.cumId);
      try {
        this.checkStatus = result.data.checkRecord.status;
        this.checkId = result.data.checkRecord.checkRecordId;
      } catch (err) {
        this.checkStatus = undefined;
      }
    },
    //下面两个是弹窗的显示
    goDetail(item) {
      this.visible = true;
      this.orderId = item;
    },
    onClose() {
      this.visible = false;
    },
    playAudio() {
      this.$refs.audioPlayer.play();
    },
    async getVoice() {
      let [result] = await voiceFn(this.$store.state.base.mainUniqueId);
      console.log('-------------录音--------', result.data);
      // this.voice = result.data;
      this.voice = result.data.replace('http://', 'https://');
      console.log('this.voice', this.voice);
    },
    transformStatu(orderStatu) {
      let color = '';
      if (orderStatu === '催办中' || orderStatu === '9') {
        color = 'red';
      } else if (orderStatu === '无需处理' || orderStatu === '已处理' || orderStatu === '已完结') {
        color = 'green';
      } else {
        color = 'blue';
      }
      return color;
    },
  },
  beforeMount() {
    this.getId();
    this.getStatus();
  },
  mounted() {
    this.getVoice();
  },
};
</script>
<style lang="less" scoped>
.big-box {
  padding: 36px;
}
//右侧访客列表
.line-b {
  .line {
    min-height: 48px;
    margin-top: 8px;
    text-align: left;
    color: rgb(170, 170, 170);
  }

  span {
    margin-left: 24px;
    color: rgb(51, 51, 51);
  }
}

//访客详情大标题
.line-b-title {
  border-left: 4px solid #1b58f4;
  padding-left: 10px; /* 可选，用于控制文字与边框的间距 */
  text-align: left;
  color: color rgb(51, 51, 51);
  font-weight: bold;
  font-size: 16px;
  line-height: 24px;
  height: 24px;
  margin-top: 28px;
  margin-bottom: 12px;
}
.title-flex {
  span {
    font-size: 14px;
    font-weight: normal;
  }
  a {
    font-size: 14px;
    font-weight: normal;
  }
}
.fa-box {
  margin: 16px 24px;
  background: #fff;
  border-radius: 2px;
}
//时间表的强调字体
.point {
  color: #165dff;
}
.labelLeft {
  /deep/ .ant-descriptions-item-colon {
    white-space: nowrap;
  }
}
//跳转工单详情
.order-id {
  display: block;
  a {
    margin-top: 8px;
    margin-bottom: 8px;
  }
  .order-son {
    display: flex;
    justify-content: space-between;
  }
}
</style>
