<template>
  <a-spin :spinning="spinning">
    <a-card style="margin: 16px 24px 60px">
      <a-row class="aRow">
        <a-col :span="2" :offset="1">
          <h3>工作台</h3>
        </a-col>
        <a-col :span="6" :offset="13">
          <TimeRangeSelect
            :startTime="params.startTime"
            :endTime="params.endTime"
            :ranges="ranges"
            :allowRangeDay="allowRangeDay"
            @change="initData"
          />
        </a-col>
      </a-row>
      <ColorCard :cardData="workspace" :colors="colors" />
      <a-row class="aRow orderCount">
        <a-col :span="2" :offset="1">
          <h4><a-icon type="line-chart" class="myIcon" />工单统计</h4>
        </a-col>
      </a-row>
      <SmoothLine :chartData="chartData" :weekDate="weekDate" ref="smoothLine" />
      <a-row class="aRow noMarBot">
        <a-col :span="2" :offset="1">
          <h4><a-icon type="alert" class="myIcon" />任务提醒</h4>
        </a-col>
      </a-row>
      <a-row class="aRow noMarTop">
        <a-list>
          <a-col :span="12" :offset="1" v-if="taskRemind.waitHandleCount > 0">
            <a class="goTo" @click="goTo('waiting')">
              <a-alert :message="waitTip" type="info" show-icon></a-alert>
            </a>
          </a-col>
          <a-col :span="12" :offset="1" v-if="taskRemind.remindHandleCount > 0">
            <a class="goTo" @click="goTo('urging')">
              <a-alert :message="urgeTip" type="warning" show-icon></a-alert>
            </a>
          </a-col>
          <a-col :span="12" :offset="1" v-if="taskRemind.expireHandleCount > 0">
            <a class="goTo" @click="goTo('overtime')">
              <a-alert :message="expireTip" type="error" show-icon></a-alert>
            </a>
          </a-col>
        </a-list>
      </a-row>
    </a-card>
  </a-spin>
</template>
<script setup>
import moment from 'moment';
import TimeRangeSelect from '@/components/TimePicker/TimeRangeSelect.vue';
import ColorCard from './components/ColorCard.vue';
import SmoothLine from '@/components/Charts/SmoothLine.vue';
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router/composables';
import { orderBoard } from '@/api/system/workorder';
let workspace = ref();
let chartData = ref();
let weekDate = ref();
let taskRemind = ref({});
let smoothLine = ref();
let spinning = ref(true);
const route = useRouter();
// 提示信息
const waitTip = computed(() => {
  return '你有' + taskRemind.value.waitHandleCount + '条待处理的工单';
});
const urgeTip = computed(() => {
  return '你有' + taskRemind.value.remindHandleCount + '条催办工单';
});
const expireTip = computed(() => {
  return '你有' + taskRemind.value.expireHandleCount + '条超时工单';
});
// 设置时间范围选择器
const allowRangeDay = 30;
const params = {
  startTime: moment().subtract(6, 'days').format('YYYY-MM-DD'),
  endTime: moment().subtract(0, 'days').format('YYYY-MM-DD'),
};
const ranges = {
  今天: [moment(), moment()],
  过去30天: [moment().subtract(30, 'days'), moment().subtract(1, 'days')],
};
// 色卡颜色
const colors = ['rgba(43, 158, 251, 1)', 'rgba(253, 200, 120, 1)', 'rgba(183, 224, 117, 1)', 'rgba(255, 95, 121, 1)'];
// 转至相应的workbench下的tabs
function goTo(label) {
  route.push({
    path: '/workorderbench',
    query: {
      handleStatu: label,
    },
  });
}
async function initData(rangeTime) {
  const params = {
    startDate: moment(rangeTime.startTime).format('YYYY-MM-DD'),
    endDate: moment(rangeTime.endTime).format('YYYY-MM-DD'),
  };
  try {
    const [result] = await orderBoard(params);
    new Promise((resolve) => {
      const { data } = result;
      workspace.value = data.workspace;
      const orderStat = data.orderStat;
      const newOrderList = orderStat.map((item) => {
        return item.newOrderCount;
      });
      const finishOrderList = orderStat.map((item) => {
        return item.finishOrderCount;
      });
      chartData.value = [
        {
          name: '新建工单',
          data: newOrderList,
        },
        {
          name: '完结工单',
          data: finishOrderList,
        },
      ];
      weekDate.value = orderStat.map((item) => {
        return item.date.substring(5, 10);
      });
      taskRemind.value = data.taskRemind;
      resolve();
    }).then(() => {
      smoothLine.value.initChart();
      spinning.value = false;
    });
  } catch {
    spinning.value = true;
  }
}
onMounted(() => {
  initData(params);
});
</script>

<style scoped lang="less">
.aRow {
  margin: 16px 0;
  .tipNum {
    color: blue;
  }
  .goTo {
    color: blue;
    text-decoration: none;
    display: block;
    margin: 2px 0 2px 24px;
    /deep/ .ant-alert {
      padding: 4px 15px 4px 37px;
    }
    /deep/ .ant-alert-icon {
      top: 8px;
    }
  }
}
.orderCount {
  margin: 24px 0 0 0;
}
.myIcon {
  margin-right: 4px;
}
.noMarBot {
  margin-bottom: 0;
}
.noMarTop {
  margin-top: 0;
}
</style>
