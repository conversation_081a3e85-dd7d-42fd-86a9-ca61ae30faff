<template>
  <div id="components-layout-demo-basic" class="mymodal">
    <a-layout>
      <a-layout-sider width="250px">
        <div class="sider-title">通话记录</div>
        <a-row>
          <a-col :span="17">
            <a-input-search
              placeholder="电话号码"
              v-model="inputTel"
              enter-button
              size="small"
              @search="leftChange(nowTab)"
            ></a-input-search>
          </a-col>
          <a-col :span="1"> </a-col>
          <a-col :span="2">
            <a-button
              size="small"
              v-hasPermi="['system:callbench:communicateList']"
              @click="
                leftChange(nowTab);
                $message.info('刷新成功');
              "
              type="primary"
              >刷新</a-button
            >
          </a-col>
        </a-row>

        <div>
          <a-tabs v-model="nowTab" @change="leftChange" style="min-height: 15vh">
            <a-tab-pane :key="1" tab="今日通话">
              <div class="call-box">
                <div
                  :key="item.mainUniqueId"
                  class="call"
                  :class="{ leftActive: leftChoice == item.mainUniqueId }"
                  v-for="(item, index) in phoneList"
                  @click="
                    choiceOne(item.mainUniqueId, item.customerNumber, item.customerProvince, item.customerCity, index)
                  "
                >
                  <div class="line-a">
                    <div style="color: rgb(0, 0, 0)">{{ item.customerNumber }}</div>
                    <div>{{ item.preTimes }}</div>

                    <a-tag class="head-icon" v-if="item.createOrderFlag == '0'" color="pink"> 未创建 </a-tag>
                    <a-tag class="head-icon" v-if="item.createOrderFlag == '1'" color="green"> 已提交 </a-tag>
                    <a-tag class="head-icon" v-if="item.createOrderFlag == '2'" color="orange"> 草稿中 </a-tag>
                  </div>
                  <div class="line-a">
                    <div>{{ item.bridgeTime }}</div>
                    <div>{{ item.customerProvince }} {{ item.customerCity }}</div>
                  </div>

                  <div class="line-a">
                    <div style="width: 113px; overflow: hidden; text-align: left">
                      <div v-if="item.bridgeTime">通话时长：{{ item.bridgeDuration }}</div>
                      <div v-if="!item.bridgeTime" style="color: #f5222d">未接通</div>
                    </div>
                    <div style="width: 96px; overflow: hidden; text-align: right">处理人：{{ item.nickName }}</div>
                  </div>

                  <div class="line-a">
                    <!-- <div>处理员工：{{ item.nickName }}</div> -->
                    <div>处理坐席：{{ item.clientName }}</div>
                  </div>
                </div>
              </div>
            </a-tab-pane>
            <a-tab-pane :key="2" tab="24小时">
              <div class="call-box">
                <div
                  :key="item.mainUniqueId"
                  class="call"
                  :class="{ leftActive: leftChoice == item.mainUniqueId }"
                  v-for="(item, index) in phoneList"
                  @click="
                    choiceOne(item.mainUniqueId, item.customerNumber, item.customerProvince, item.customerCity, index)
                  "
                >
                  <div class="line-a">
                    <div style="color: rgb(0, 0, 0)">{{ item.customerNumber }}</div>
                    <div>{{ item.preTimes }}</div>
                    <a-tag class="head-icon" v-if="item.createOrderFlag == '0'" color="pink"> 未创建 </a-tag>
                    <a-tag class="head-icon" v-if="item.createOrderFlag == '1'" color="green"> 已提交 </a-tag>
                    <a-tag class="head-icon" v-if="item.createOrderFlag == '2'" color="orange"> 草稿中 </a-tag>
                  </div>
                  <div class="line-a">
                    <div>{{ item.bridgeTime }}</div>
                    <div>{{ item.customerProvince }} {{ item.customerCity }}</div>
                  </div>

                  <div class="line-a">
                    <div style="width: 113px; overflow: hidden; text-align: left">
                      <div v-if="item.bridgeTime">通话时长：{{ item.bridgeDuration }}</div>
                      <div v-if="!item.bridgeTime" style="color: #f5222d">未接通</div>
                    </div>
                    <div style="width: 96px; overflow: hidden; text-align: right">处理人：{{ item.nickName }}</div>
                  </div>

                  <div class="line-a">
                    <!-- <div>处理员工：{{ item.nickName }}</div> -->
                    <div>处理坐席：{{ item.clientName }}</div>
                  </div>
                </div>
              </div>
            </a-tab-pane>

            <a-tab-pane :key="3" tab="一周">
              <div class="call-box">
                <div
                  :key="item.mainUniqueId"
                  class="call"
                  :class="{ leftActive: leftChoice == item.mainUniqueId }"
                  v-for="(item, index) in phoneList"
                  @click="
                    choiceOne(item.mainUniqueId, item.customerNumber, item.customerProvince, item.customerCity, index)
                  "
                >
                  <div class="line-a">
                    <div style="color: rgb(0, 0, 0)">{{ item.customerNumber }}</div>
                    <div>{{ item.preTimes }}</div>
                    <a-tag class="head-icon" v-if="item.createOrderFlag == '0'" color="pink"> 未创建 </a-tag>
                    <a-tag class="head-icon" v-if="item.createOrderFlag == '1'" color="green"> 已提交 </a-tag>
                    <a-tag class="head-icon" v-if="item.createOrderFlag == '2'" color="orange"> 草稿中 </a-tag>
                  </div>
                  <div class="line-a">
                    <div>{{ item.bridgeTime }}</div>
                    <div>{{ item.customerProvince }} {{ item.customerCity }}</div>
                  </div>
                  <div class="line-a">
                    <div style="width: 113x; overflow: hidden; text-align: left">
                      <div v-if="item.bridgeTime">通话时长：{{ item.bridgeDuration }}</div>
                      <div v-if="!item.bridgeTime" style="color: #f5222d">未接通</div>
                    </div>
                    <div style="width: 96px; overflow: hidden; text-align: right">处理人：{{ item.nickName }}</div>
                  </div>

                  <div class="line-a">
                    <!-- <div>处理员工：{{ item.nickName }}</div> -->
                    <div>处理坐席：{{ item.clientName }}</div>
                  </div>
                </div>
              </div>
            </a-tab-pane>
          </a-tabs>
          <a-pagination @change="leftPageChange" v-model="leftPage" :total="leftPageCount" :page-size.sync="leftSize" />
        </div>
        <div></div>
      </a-layout-sider>
      <a-layout>
        <a-layout-header>
          <div class="header">
            <a-button
              type="primary"
              style="margin-top: 14px"
              @click="listAdd"
              v-if="showRight"
              v-hasPermi="['system:callbench:workOrderAdd']"
              >创建工单</a-button
            >
            <NewOrder ref="addOrder" v-show="false" :showCall="false" :showOk="true" />
          </div>
        </a-layout-header>
        <a-layout-content v-show="showRight">
          <div>
            <a-modal title="提示" :visible="modalSeen" @ok="modalOK" @cancel="modalCancel">
              选择该优先级会触发SLA目标，请确认
            </a-modal>
          </div>
          <div class="refresh">
            <a-button
              @click="
                choiceOne(leftChoice, customerNumber, customerProvince, customerCity);
                $message.info('刷新成功');
              "
              type="primary"
              ><a-icon type="retweet" v-hasPermi="['system:callbench:communicateList']" />刷新</a-button
            >
          </div>
          <div class="a-box">
            <a-avatar :size="80" style="margin-right: 16px; font-size: 20px">
              {{ firstFont }}
              <a-icon type="phone" v-if="!firstFont" style="color: rgba(0, 0, 0, 0.65)" />
            </a-avatar>
            <span class="name">{{ detailRight.visitorName }}</span>
            <span class="area">{{ detailRight.lowAddress }}</span>
            <span class="tel" v-show="detailRight.visitorPhone"
              ><a-icon type="phone" /> {{ detailRight.visitorPhone }}</span
            >
          </div>

          <div>
            <a-tabs default-active-key="1" @change="callbackContent" class="content-tab-box">
              <a-tab-pane key="1" tab="访客详情">
                <div class="tab1-box">
                  <div class="line-b-title">
                    访客基本信息
                    <a-icon
                      type="form"
                      style="margin-left: 8px; font-size: 16px"
                      @click="rightWrite"
                      v-if="!isRightWrite"
                    />
                  </div>
                  <a-row class="line-b">
                    <a-col :span="8">
                      <a-row>
                        <a-col :span="6"> 访客姓名 </a-col>

                        <a-col v-if="!isRightWrite" :span="17">
                          <span>{{ detailRight.visitorName }}</span>
                        </a-col>

                        <a-col :span="17" v-if="isRightWrite">
                          <!-- <a-input v-model="detailRight.visitorName"></a-input> -->

                          <a-input-group compact>
                            <a-input style="width: 60%" v-model="backName" />
                            <a-select
                              style="width: 40%"
                              :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                              v-model="visitorSex"
                            >
                              <a-select-option v-if="visitorSex == 0" @click="visitorSex = 0" :key="0">
                                {{ ' ' }}
                              </a-select-option>
                              <a-select-option @click="visitorSex = 1" :key="'1'">
                                {{ '先生' }}
                              </a-select-option>
                              <a-select-option @click="visitorSex = 2" :key="'2'">
                                {{ '女士' }}
                              </a-select-option>
                            </a-select>
                          </a-input-group>
                        </a-col>
                      </a-row>
                    </a-col>

                    <a-col :span="6">
                      <a-row>
                        <a-col :span="6"> 真实姓名 </a-col>

                        <a-col v-if="!isRightWrite" :span="17">
                          <span> {{ detailRight.realName }}</span>
                        </a-col>

                        <a-col :span="17" v-if="isRightWrite">
                          <a-input v-model="detailRight.realName"></a-input>
                        </a-col>
                      </a-row>
                    </a-col>

                    <a-col :span="8">
                      <a-row>
                        <a-col :span="6"> 访客电话 </a-col>

                        <a-col v-if="!isRightWrite" :span="17">
                          <span>{{ detailRight.visitorPhone }}</span>
                        </a-col>

                        <a-col :span="17" v-if="isRightWrite">
                          <a-input v-model="detailRight.visitorPhone"></a-input>
                        </a-col>
                      </a-row>
                    </a-col>
                  </a-row>
                  <a-row class="line-b">
                    <a-col :span="8">
                      <a-row>
                        <a-col :span="7"> 访客电话-2 </a-col>

                        <a-col v-if="!isRightWrite" :span="17">
                          <span>{{ detailRight.visitorSecPhone }}</span>
                        </a-col>

                        <a-col :span="17" v-if="isRightWrite">
                          <a-input v-model="detailRight.visitorSecPhone"></a-input>
                        </a-col>
                      </a-row>
                    </a-col>
                    <a-col :span="8">
                      <a-row>
                        <a-col :span="6"> 微信号 </a-col>

                        <a-col v-if="!isRightWrite" :span="17">
                          <span>{{ detailRight.wechatId }}</span>
                        </a-col>

                        <a-col :span="17" v-if="isRightWrite">
                          <a-input v-model="detailRight.wechatId"></a-input>
                        </a-col>
                      </a-row>
                    </a-col>

                    <a-col :span="8">
                      <a-row>
                        <a-col :span="6"> 邮箱 </a-col>

                        <a-col v-if="!isRightWrite" :span="17">
                          <span>{{ detailRight.mail }}</span>
                        </a-col>

                        <a-col :span="17" v-if="isRightWrite">
                          <a-input v-model="detailRight.mail"></a-input>
                        </a-col>
                      </a-row>
                    </a-col>
                  </a-row>

                  <a-row class="line-b">
                    <a-col :span="8">
                      <a-row>
                        <a-col :span="6"> 访客等级 </a-col>
                        <a-col v-if="!isRightWrite" :span="17">
                          <span>{{ fromGrade }}</span>
                        </a-col>
                        <a-col :span="17" v-if="isRightWrite">
                          <a-select
                            style="width: 100%"
                            :defaultValue="fromGrade"
                            v-model="fromGrade"
                            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                            @change="selectChange"
                          >
                            <a-select-option
                              v-for="(item, index) in visitorGradeSelect"
                              :key="index"
                              :value="item.label"
                            >
                              {{ item.label }}
                            </a-select-option>
                          </a-select>
                        </a-col>
                      </a-row></a-col
                    >
                    <a-col :span="8"
                      ><a-row>
                        <a-col :span="6"> 访客来源 </a-col>

                        <a-col v-if="!isRightWrite" :span="17">
                          <span>{{ fromName }}</span>
                        </a-col>

                        <a-col :span="17" v-if="isRightWrite">
                          <a-select
                            style="width: 100%"
                            :defaultValue="fromName"
                            v-model="fromName"
                            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                          >
                            <a-select-option
                              v-for="(item, index) in visitorFromSelect"
                              :key="index"
                              :value="item.label"
                            >
                              {{ item.label }}
                            </a-select-option>
                          </a-select>
                        </a-col>
                      </a-row></a-col
                    >
                  </a-row>

                  <a-row class="line-b">
                    <a-col :span="2"> 访客地址 </a-col>

                    <a-col v-if="!isRightWrite" :span="16">
                      <span> {{ noDotAddress }} {{ detailRight.fullAddress }}</span>
                    </a-col>

                    <a-col :span="16" v-if="isRightWrite">
                      <a-row class="line-b">
                        <a-col :span="11">
                          <a-cascader
                            v-if="defaultAddress"
                            style="width: 100%"
                            :options="provinceList"
                            placeholder="请选择所在地区"
                            :defaultValue="defaultAddress.split(',')"
                            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                            @change="addressChange"
                          />
                          <a-cascader
                            v-if="!defaultAddress"
                            style="width: 100%"
                            :options="provinceList"
                            placeholder="请选择所在地区"
                            :defaultValue="[]"
                            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                            @change="addressChange"
                          />
                        </a-col>
                        <a-col :span="1"></a-col>
                        <a-col :span="11"> <a-input v-model="detailRight.fullAddress"></a-input></a-col>
                      </a-row>
                    </a-col>
                  </a-row>

                  <a-row class="line-b">
                    <a-col :span="2"> 访客标签 </a-col>

                    <a-col v-if="!isRightWrite" :span="16">
                      <span>
                        <a-tag color="pink" v-for="(item, index) in tagName" :key="index">{{ item }}</a-tag></span
                      >
                    </a-col>

                    <a-col :span="8" v-if="isRightWrite">
                      <a-select
                        mode="multiple"
                        style="width: 100%"
                        :defaultValue="tagName"
                        v-model="tagName"
                        :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                        @change="tagTest"
                      >
                        <a-select-option v-for="(item, index) in tagSelect" :key="index" :value="item.label">{{
                          item.label
                        }}</a-select-option>
                      </a-select>
                    </a-col>
                  </a-row>

                  <div class="line-b-title" style="margin-bottom: 16px">访客备注信息</div>
                  <div>
                    <div>
                      <a-textarea :rows="4" v-model="detailRight.remark" :disabled="!isRightWrite" />
                    </div>
                    <div class="button-flex">
                      <a-button
                        type="primary"
                        @click="
                          upLoadDetail();
                          backNameFirst = true;
                          visitorSex = 0;
                        "
                        v-if="isRightWrite"
                        >确定</a-button
                      >
                      <a-button
                        @click="
                          choiceOne(leftChoice, customerNumber, customerProvince, customerCity);
                          backNameFirst = true;
                          visitorSex = 0;
                        "
                        v-if="isRightWrite"
                        >取消</a-button
                      >
                    </div>
                  </div>
                </div>
              </a-tab-pane>
              <a-tab-pane key="2" tab="历史工单" force-render>
                <BuseCrud
                  ref="crud"
                  :loading="loading"
                  :tablePage="tablePage"
                  :tableColumn="tableColumn"
                  :tableData="tableData"
                  :modalConfig="modalConfig"
                  @modalCancel="modalCancelHandler"
                  @modalSubmit="modalSubmit"
                  @modalConfirm="modalConfirmHandler"
                  @rowDel="deleteRowHandler"
                  @loadData="loadData"
                  @handleCreate="rowAdd"
                  class="all-father"
                >
                  <!-- 上面是tableData表部分 -->
                  <!-- <template slot="menu">
                    <div class="attention" v-if="isAttention == 0" @click="isAttention = 1">关注</div>
                    <div class="attention" v-if="isAttention == 1" @click="isAttention = 0">取消关注</div>
                  </template> -->
                  <template slot="vehicle" slot-scope="{ params }">
                    <a-row>
                      <a-col :span="8">
                        <a-input v-model="params.vehicleBrand" placeholder="请输入品牌"></a-input>
                      </a-col>
                      <a-col :span="8">
                        <a-input v-model="params.vehicleModel" placeholder="请输入车型"></a-input>
                      </a-col>
                      <a-col :span="8">
                        <!-- <a-input v-model="params.vehicleYear" placeholder="请选择年份"></a-input> -->
                        <BuseRangePicker
                          type="year"
                          v-model="params.vehicleYear"
                          format="YYYY"
                          placeholder="请选择年份"
                          :needShowSecondPicker="() => false"
                        ></BuseRangePicker>
                      </a-col>
                    </a-row>
                  </template>
                  <template slot="orderHandler" slot-scope="{ params }">
                    <a-tree-select
                      placeholder="请选择受理人员"
                      :disabled="autoDisable"
                      v-model="params.orderHandler"
                      :treeData="handleList"
                      showSearch
                      labelInValue
                      :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                      @focus="getUserList()"
                    >
                      <template slot="myTitle" slot-scope="item">
                        <StatuNode :newNodes="item" />
                      </template>
                    </a-tree-select>
                  </template>

                  <template slot="urgency" slot-scope="{ row }">
                    <span :style="{ color: urgencyColor(row.urgency) }">
                      {{ row.urgency }}
                    </span>
                  </template>

                  <template slot="categoryName" slot-scope="{ row }">
                    <span>
                      {{ row.categoryName }}
                      <div class="tipTag">
                        <a-tag color="red" v-if="row.remindFlag === '1'">催</a-tag>
                        <a-tag color="orange" v-if="row.expireFlag === '1'">超</a-tag>
                      </div>
                    </span>
                  </template>

                  <template slot="orderStatus" slot-scope="{ row }">
                    <a-tag :color="transformStatu(row.orderStatus)">
                      {{ row.orderStatus }}
                    </a-tag>
                  </template>

                  <template slot="orderId" slot-scope="{ row }">
                    <a @click="toOrderDetail(row)">
                      {{ row.orderId }}
                    </a>
                  </template>

                  <template #defaultHeader>
                    <span>
                      <a-button v-if="!showButton" type="primary" style="margin-right: 16px" @click="loadData(false)"
                        >全部</a-button
                      >
                      <a-button v-if="showButton" style="margin-right: 16px" @click="loadData(false)">全部</a-button>

                      <a-button v-if="showButton" type="primary" @click="loadData(true)">本月</a-button>
                      <a-button v-if="!showButton" @click="loadData(true)">本月</a-button>
                    </span>
                  </template>

                  <!-- 下面是创建工单部分 -->
                  <!-- <template slot="handleGroup">
                    <a-select
                      v-model="handlegroups.value"
                      placeholder="请先选择工单分类"
                      @change="clearUserList"
                      :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                    >
                      <a-select-option v-if="flowRadio == '2'" :value="backupHandle.groupValue">
                        {{ backupHandle.grouplabel }}
                      </a-select-option>

                      <template v-if="flowRadio == '1' && handleList.length !== 0">
                        <a-select-option
                          @click="choiceHandleGroup(item.userList)"
                          v-for="(item, index) in handleList"
                          :key="index"
                          :value="item.valueId"
                        >
                          {{ item.valueName }}
                        </a-select-option>
                      </template>
                    </a-select>
                  </template>

                  <template slot="handleUser">
                    <a-select
                      v-model="handleusers.value"
                      placeholder="请先选择工单分类"
                      :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                    >
                      <a-select-option v-if="flowRadio == '2'" :value="backupHandle.userValue">
                        {{ backupHandle.userlabel }}
                      </a-select-option>

                      <template v-if="flowRadio == '1' && handleUserList.length !== 0">
                        <a-select-option v-for="(item, index) in handleUserList" :key="index" :value="item.valueId">
                          {{ item.valueName }}</a-select-option
                        >
                      </template>
                    </a-select>
                  </template> -->

                  <template slot="orContent" slot-scope="{ params }">
                    <div class="orderContent">
                      <AddReply :orderSort="nowCategoryID" @selectedReply="handleSelect" />
                      <Editor
                        :value="contentInput"
                        v-model="params.orderContent"
                        @blur="params.orderContent = contentInput"
                        @input="giveParam(params.orderContent)"
                      />
                    </div>
                  </template>

                  <template slot="selectOr" slot-scope="{ params }">
                    <a-select
                      placeholder="请在选择工单分类之后选择工单模板"
                      @change="handleChange"
                      v-model="choiceValue"
                      :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                    >
                      <a-select-option v-for="(item, index) in exList || []" :key="index">
                        {{ item.templateName }}
                      </a-select-option>
                    </a-select>

                    <div class="templateFields">
                      <a-button
                        type="primary"
                        v-if="showMod"
                        @click="
                          showMod = false;
                          choiceValue = undefined;
                        "
                        ><a-icon type="minus-circle" /> 取消模板选择</a-button
                      >
                      <!-- :rules="rules[fielditem.templateFieldId]" :rules="rules" -->
                      <a-form-model ref="templateForm" :model="params">
                        <template v-for="(fielditem, index) in choiceExList.fields">
                          <a-form-model-item
                            v-if="fielditem !== undefined && showMod && fielditem.fieldType == '1'"
                            :label-col="{ span: 10 }"
                            :wrapper-col="{ span: 14 }"
                            :label="fielditem.fieldName"
                            :key="index"
                            :rules="[{ required: fielditem.emptyFlag === '0', message: '请输入', trigger: 'blur' }]"
                            :prop="`exValues.${index}`"
                            ><a-input
                              @focus="tryIt(params)"
                              :defaultValue="fielditem.defaultValue"
                              @hook:mounted="params.exValues[index] = fielditem.defaultValue"
                              v-model="params.exValues[index]"
                            ></a-input>
                          </a-form-model-item>

                          <a-form-model-item
                            v-if="fielditem !== undefined && showMod && fielditem.fieldType == '2'"
                            :label-col="{ span: 10 }"
                            :wrapper-col="{ span: 14 }"
                            :label="fielditem.fieldName"
                            :key="index"
                            :rules="[{ required: fielditem.emptyFlag === '0', message: '请输入', trigger: 'blur' }]"
                            :prop="`exValues.${index}`"
                          >
                            <a-select
                              @focus="getModSelect(fielditem.fieldDataKey)"
                              @hook:mounted="params.exValues[index] = fielditem.defaultValue"
                              v-model="params.exValues[index]"
                            >
                              <a-select-option v-for="selectItem in selectOne" :key="selectItem.value">
                                {{ selectItem.label }}
                              </a-select-option>
                            </a-select>
                          </a-form-model-item>

                          <a-form-model-item
                            v-if="fielditem !== undefined && showMod && fielditem.fieldType == '3'"
                            :label-col="{ span: 10 }"
                            :wrapper-col="{ span: 14 }"
                            :label="fielditem.fieldName"
                            :key="index"
                            :rules="[{ required: fielditem.emptyFlag === '0', message: '请输入', trigger: 'blur' }]"
                            :prop="`exValues.${index}`"
                            ><a-date-picker
                              :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                              style="width: 100%"
                              :defaultValue="fielditem.defaultValue"
                              @hook:mounted="params.exValues[index] = fielditem.defaultValue"
                              v-model="params.exValues[index]"
                            ></a-date-picker>
                          </a-form-model-item>

                          <a-form-model-item
                            v-if="fielditem !== undefined && showMod && fielditem.fieldType == '4'"
                            :label-col="{ span: 10 }"
                            :wrapper-col="{ span: 14 }"
                            :label="fielditem.fieldName"
                            :key="index"
                            :rules="[{ required: fielditem.emptyFlag === '0', message: '请输入', trigger: 'blur' }]"
                            :prop="`exValues.${index}`"
                          >
                            <Editor
                              :value="secContentInput"
                              :defaultValue="fielditem.defaultValue"
                              @hook:mounted="params.exValues[index] = fielditem.defaultValue"
                              v-model="params.exValues[index]"
                            />
                          </a-form-model-item>

                          <a-form-model-item
                            v-if="fielditem !== undefined && showMod && fielditem.fieldType == '5'"
                            :label-col="{ span: 10 }"
                            :wrapper-col="{ span: 14 }"
                            :label="fielditem.fieldName"
                            :key="index"
                            :rules="[{ required: fielditem.emptyFlag === '0', message: '请输入', trigger: 'blur' }]"
                            :prop="`exValues.${index}`"
                          >
                            <a-select
                              mode="tags"
                              style="width: 100%"
                              placeholder="请输入"
                              @change="fieldSelect"
                              :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                              @focus="getModSecSelect(fielditem.fieldDataKey)"
                              @hook:mounted="params.exValues[index] = fielditem.defaultValue"
                              v-model="params.exValues[index]"
                            >
                              <a-select-option v-for="item in selectCom" :key="item.value">
                                {{ item.label }}
                              </a-select-option>
                            </a-select>
                          </a-form-model-item>

                          <a-form-model-item
                            v-if="fielditem !== undefined && showMod && fielditem.fieldType == '6'"
                            :label-col="{ span: 10 }"
                            :wrapper-col="{ span: 14 }"
                            :label="fielditem.fieldName"
                            :key="index"
                            :rules="[{ required: fielditem.emptyFlag === '0', message: '请输入', trigger: 'blur' }]"
                            :prop="`exValues.${index}`"
                          >
                            <a-input-number
                              :defaultValue="fielditem.defaultValue"
                              @hook:mounted="params.exValues[index] = fielditem.defaultValue"
                              v-model="params.exValues[index]"
                              @change="fieldCount"
                            />
                          </a-form-model-item>

                          <a-form-model-item
                            v-if="fielditem !== undefined && showMod && fielditem.fieldType == '7'"
                            :label-col="{ span: 10 }"
                            :wrapper-col="{ span: 14 }"
                            :label="fielditem.fieldName"
                            :key="index"
                            :rules="[{ required: fielditem.emptyFlag === '0', message: '请输入', trigger: 'blur' }]"
                            :prop="`exValues.${index}`"
                          >
                            <a-input-number
                              :defaultValue="fielditem.defaultValue"
                              @hook:mounted="params.exValues[index] = fielditem.defaultValue"
                              v-model="params.exValues[index]"
                              @change="fieldCount"
                              :formatter="(value) => `${value}%`"
                              :parser="(value) => value.replace('%', '')"
                            />
                          </a-form-model-item>
                        </template>
                      </a-form-model>
                    </div>
                  </template>
                  <template slot="newGuest" slot-scope="{ params }">
                    <a-button type="primary" @click="showGuestfn(params)"
                      ><span>
                        <a-icon v-if="showGuest == false" type="plus-circle" class="icon-plus" />
                        <a-icon v-if="showGuest == true" type="minus-circle" class="icon-plus" /> </span
                      ><span v-if="showGuest == true">取消</span>创建新访客</a-button
                    >
                    <a-form-model
                      ref="visitorForm"
                      :model="params"
                      v-show="showGuest"
                      :rules="visitorRules"
                      class="visitorForm"
                    >
                      <a-form-model-item
                        label="访客名称"
                        prop="visitorName"
                        key="visitorName"
                        :label-col="{ span: 5 }"
                        :wrapper-col="{ span: 17 }"
                      >
                        <a-input
                          v-model="params.visitorName"
                          placeholder="请输入访客名称"
                          @blur="nameFn(params.visitorName)"
                          @change="visitorName = params.visitorName"
                        />
                        <div class="tag-place">
                          <a-tag color="blue" class="tag" v-if="man">先生</a-tag>
                          <a-tag color="blue" class="tag" v-if="lady">女士</a-tag>
                        </div>
                        <a-button class="name-button" size="small" @click="spell(params.visitorName, 1)">
                          先生
                        </a-button>
                        <a-button class="name-button" size="small" @click="spell(params.visitorName, 2)">
                          女士
                        </a-button>
                      </a-form-model-item>
                      <a-form-model-item
                        label="访客电话"
                        prop="visitorPhone"
                        key="visitorPhone"
                        :label-col="{ span: 5 }"
                        :wrapper-col="{ span: 17 }"
                      >
                        <a-input v-model="params.visitorPhone" @change="visitorPhone = params.visitorPhone" />
                      </a-form-model-item>
                    </a-form-model>
                  </template>

                  <template slot="flowType">
                    <a-radio-group v-model="flowRadio" @change="choiceManFlow">
                      <a-radio value="1"> 人工流转 </a-radio>
                      <a-radio value="2" v-if="enabledFlag == '1'" :disabled="autoFlag"> 自动分配 </a-radio>
                      <a-radio value="3" v-if="enabledFlag == '1'"> 工单池流转 </a-radio>
                    </a-radio-group>
                  </template>

                  <template slot="guestTag">
                    <div class="guest-tag">
                      <a-tag class="tags" color="pink" v-for="(item, index) in orderName" :key="index">{{
                        item
                      }}</a-tag>
                      <a-button
                        v-show="!showTags"
                        type="primary"
                        @click="
                          guestTagHandler;
                          showTags = !showTags;
                        "
                        ><span><a-icon type="plus-circle" class="icon-plus" /></span>添加工单标签</a-button
                      >

                      <Tags :showTags="showTags" @custom-event="transTags" @custom-showAdd="changeAddTags" />
                    </div>
                  </template>
                </BuseCrud>
                <a-modal :visible="showFailModal" @ok="resendOrder" @cancel="showFailModal = false" title="提示信息">
                  <template slot="footer">
                    <a-button @click="showFailModal = false"> 我知道了 </a-button>
                    <a-button type="primary" :loading="modalLoading" @click="resendOrder"> 重试 </a-button>
                  </template>
                  <div class="fail-modal">
                    <div><a-icon type="exclamation-circle" class="fail-modal-icon" /></div>
                    <div>
                      <h3>{{ failMsg }}</h3>
                      <p>已生成客服工单，但能源维保通{{ failMsg }}，请点击重试或稍后在详情中重新提交。</p>
                    </div>
                  </div>
                </a-modal>
              </a-tab-pane>
              <a-tab-pane key="3" tab="历史通话">
                <ThirdTab
                  v-if="key == '3'"
                  :customerNumber="customerNumber"
                  :visitorSecPhone="visitorSecPhone"
                  :mainUniqueId="leftChoice"
                />
              </a-tab-pane>
            </a-tabs>
          </div>
        </a-layout-content>
      </a-layout>
    </a-layout>
  </div>
</template>

<script src="./defineComponent.vue.js"></script>

<style lang="less" scoped>
#components-layout-demo-basic {
  text-align: center;
}
#components-layout-demo-basic .ant-layout-header,
#components-layout-demo-basic .ant-layout-footer {
  background: #fff;
  color: rgba(0, 0, 0, 0.65);
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  background-color: #fafafa;
}
#components-layout-demo-basic .ant-layout-footer {
  line-height: 1.5;
}
#components-layout-demo-basic .ant-layout-sider {
  background: #fff;
  color: rgba(0, 0, 0, 0.65);
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  padding: 8px;
}
#components-layout-demo-basic .ant-layout-content {
  background: #fff;
  color: rgba(0, 0, 0, 0.65);
  min-height: 120px;
  line-height: 80px;
}
#components-layout-demo-basic > .ant-layout {
  margin-bottom: 48px;
}
#components-layout-demo-basic > .ant-layout:last-child {
  margin: 0;
}
.header {
  display: flex;
  justify-content: flex-end;
}

//头像以及名字等
.a-box {
  margin-top: 32px;
  display: inline-block;
  position: relative;
  min-width: 250px;
  .name {
    font-size: 35px;
    margin-left: 16px;
  }
  .area {
    font-size: 20px;
    margin-left: 16px;
  }
  .tel {
    font-size: 18px;
    display: block;
    position: absolute;
    top: 40px;
    left: 112px;
    white-space: nowrap;
  }
}
//后续加需求的右侧刷新按钮
.refresh {
  position: absolute;
  right: 16px;
  display: inline-block;
}
//侧边标题
.sider-title {
  font-size: 16px;
  text-align: left;
  color: #333333;
  line-height: 32px;
  height: 32px;
}
//左侧盒子
.call-box {
  .call {
    height: 102px;
    border-radius: 8px;
    background-color: #fafafa;
    margin-top: 12px;
    margin-bottom: 12px;
    padding: 10px;
    font-size: 12px;
    cursor: pointer; //悬浮时变手指
    .line-a {
      display: flex;
      justify-content: space-between;
      line-height: 20px;
      height: 20px;
    }
  }
  // 左侧选中时候的表现
  .leftActive {
    border: 2px solid #1b58f4;
    transform: translateY(-2px);
    animation-duration: 0.5s;
  }
}
//关注按钮
.attention {
  display: inline-block;
  margin-right: 10px;
  color: #165dff;
  cursor: pointer;
  padding: 0;
  min-width: 38px;
  height: 30px;
  line-height: 30px;
}
//tab大盒子
.content-tab-box {
  margin-top: 48px;
}
.tab1-box {
  padding-left: 80px;
  padding-right: 80px;
  //访客详情大标题
  .line-b-title {
    border-left: 4px solid #1b58f4;
    padding-left: 10px; /* 可选，用于控制文字与边框的间距 */
    text-align: left;
    color: color rgb(51, 51, 51);
    font-weight: bold;
    margin-top: 16px;
  }
  //访客详情内容
  .line-b {
    height: 38px;
    line-height: 38px;
    text-align: left;
    font-size: 13px;
    color: rgb(170, 170, 170);
    span {
      color: rgb(51, 51, 51);
    }
  }
}
//紧急字体
.exgency {
  color: red;
}
//访客名称
.tag-place {
  display: flex;
  justify-content: flex-end;
  top: -36px;
  position: relative;
  .tag {
    position: absolute;
    height: 32px;
    line-height: 32px;
  }
}
.name-button {
  margin-right: 4px;
}
.guest-tag {
  //标签样式
  // display: flex;
  // justify-content: space-around;
  .tags {
    white-space: nowrap;
    line-height: 32px;
    height: 32px;
  }
}

.icon-plus {
  margin-right: 4px;
}
.button-flex {
  margin-top: 16px;
  display: flex;
  justify-content: space-around;
}

.templateFields {
  // /deep/ .ant-form-item label {
  //   white-space: break-spaces;
  //   text-align: justify;
  //   display: inline-flex;
  // }

  /deep/ .ant-form-item {
    margin-bottom: 4px;
    display: flex;
  }
  /deep/ .ant-form-item label {
    white-space: break-spaces;
    text-align: justify;
    display: inline-flex;
    font-size: 12px;
    color: #999;
  }
}

.replyMark {
  margin-bottom: 8px;
}

//左侧角标
.head-icon {
  position: absolute;
  border-radius: 2px;
  transform: translate(168px, -20px) scale(0.8);
  min-width: 36px;
}

/deep/ .ant-form-item-label {
  text-align: left;
  width: fit-content;
  display: inline-block;
}
.templateFields /deep/ .ant-form-item label {
  display: inline-block;
  // white-space: nowrap;
}
/deep/ .ant-form-item-control {
  text-align: left;
}
.orderContent {
  padding-top: 24px;
  /deep/ .replyLine {
    margin-bottom: 4px;
  }
  /deep/ .quillWrapper .ql-toolbar.ql-snow .ql-formats {
    transform: scale(0.9);
    display: inline-flex;
    margin: 0 -1px;
  }
}

.visitorForm {
  /deep/ .ant-form-item-label > label {
    color: #999;
    font-size: 12px;
  }
}
</style>
