<template>
  <BuseCrud
    ref="crud"
    title=""
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @modalCancel="modalCancelHandler"
    @modalSubmit="modalSubmit"
    @modalConfirm="modalConfirmHandler"
    @rowDel="deleteRowHandler"
    @loadData="loadData"
    @handleCreate="rowAdd"
    @handleReset="handleReset"
    @rowEdit="rowEdit"
  >
    <template #defaultHeader>
      <span class="button-right" @click="getSelectEvent">
        <a-button @click="exportList">导出</a-button>
      </span>
    </template>

    <template #datePick>
      <a-range-picker
        style="width: 268px"
        show-time
        v-model="params.bridgeTime"
        @change="changeTime(params.bridgeTime)"
      />
    </template>

    <template #checkScore>
      <a-inputNumber
        @change="startScoreChange(params)"
        v-model="params.startScore"
        placeholder="请输入最小分数"
        style="width: 48%; margin-right: 4%"
      ></a-inputNumber>
      <a-inputNumber
        @change="endScoreChange(params)"
        v-model="params.endScore"
        placeholder="请输入最大分数"
        style="width: 48%"
      ></a-inputNumber>
    </template>
  </BuseCrud>
</template>

<script>
// import mock from './mock1';
import {
  checkAPI,
  checkAppealAPI,
  checkDetailAPI,
  checkListAPI,
  exportAPI,
  getUserListAPI,
  deleteDataAPI,
  saveDataAPI,
  queryClientNameAPI,
  getProvinceTreeAPI,
} from '@/api/system/telManager';
// import { exportAPI } from '@/api/system/guestList';
import { downLoadXlsx } from '@/utils/system/common';
import { DictCodeAPI } from '@/api/system/dict';
import province from '@/views/home/<USER>/province';
import moment from 'moment';
import 'moment/locale/zh-cn';
moment.locale('zh-cn');
export default {
  name: 'DumiDocVueIndex',
  data() {
    return {
      params: {
        communicateId: '',
        bridgeTime: undefined,
        startScore: '',
        endScore: '',
        clientName: '',
        classify: '',
        startTime: '',
        endTime: '',
        checkStatus: '',
        communicateStatusFlag: '',
        callStatus: '',
        customerNumber: '',
        nickName: '', // 账号名称
        checkUserName: '', // 质检人
      },
      menuShow: true,
      startScore: '',
      endScore: '',
      tableData: [],
      tableColumn: [
        { type: 'checkbox' },
        //列名，列数字转文字配置
        {
          field: 'communicateId',
          title: '通话编号',
          minWidth: 167,
        },
        {
          field: 'customerNumber',
          title: '访客电话',
          minWidth: 150,
        },
        {
          field: 'callType',
          title: '通话类型',
          minWidth: 100,
          formatter: ({ cellValue }) => {
            if (cellValue == '1') {
              return '呼入';
            } else if (cellValue == '2') {
              return '呼出';
            }
            return cellValue;
          },
        },
        {
          field: 'clientName',
          title: '坐席名称',
          minWidth: 150,
        },
        {
          field: 'nickName',
          title: '账号名称',
          minWidth: 150,
        },
        {
          field: 'communicateStatusFlag',
          title: '通话状态',
          minWidth: 180,
          formatter: ({ cellValue }) => {
            if (cellValue == '1') {
              return '已接通';
            } else if (cellValue == '2') {
              return '未接通-工作时间';
            } else if (cellValue == '3') {
              return '未接通-非工作时间';
            }
            return cellValue;
          },
        },
        {
          field: 'location',
          title: '归属地',
          minWidth: 120,
          formatter: ({ row }) => {
            const province = row.customerProvince || '';
            const city = row.customerCity || '';
            return province && city ? `${province} ${city}` : province || city || '';
          },
        },
        {
          field: 'startTime',
          title: '呼入时间',
          minWidth: 170,
        },
        {
          field: 'callDuration',
          title: '通话时长',
          minWidth: 120,
        },
        {
          field: 'checkScore',
          title: '质检分数',
          minWidth: 80,
        },
        {
          field: 'checkStatusName',
          title: '质检状态',
          minWidth: 80,
        },
        {
          field: 'checkUserName',
          title: '质检人',
          minWidth: 100,
        },
        {
          field: 'hotLine',
          title: '被叫热线',
          minWidth: 120,
        },
        {
          field: 'datasourceName',
          title: '通话记录数据来源',
          minWidth: 150,
        },
      ],
      checkStatus: [],
      communicateStatusFlag: [],
      accountNameOptions: [], // 账号名称选项
      checkPersonOptions: [], // 质检人选项
      clientNameOptions: [], // 坐席名称选项
      callTypeOptions: [
        { value: '1', label: '呼入' },
        { value: '2', label: '呼出' },
      ],
      provinceOptions: [], // 省市数据
      selectedAccountInfo: {}, // 选中的账户信息，包含nickName和userId
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
    };
  },
  computed: {
    filterOptions() {
      //筛选类配置
      return {
        config: [
          {
            field: 'communicateId',
            title: '通话编号',
          },
          {
            field: 'checkStatus',
            title: '质检状态',
            element: 'a-select',
            props: {
              options: this.checkStatus,
            },
          },
          {
            field: 'bridgeTime',
            title: '通话时间',
            element: 'slot',
            slotName: 'datePick',
          },

          {
            field: 'clientName',
            title: '坐席',
          },
          {
            field: 'communicateStatusFlag',
            title: '通话状态',
            element: 'a-select',
            props: {
              options: this.communicateStatusFlag,
            },
          },
          {
            field: 'checkScore',
            title: '质检分数',
            element: 'slot',
            slotName: 'checkScore',
          },
          {
            field: 'nickName',
            title: '账号名称',
            element: 'a-select',
            props: {
              options: this.accountNameOptions,
              showSearch: true,
              filterOption: (input, option) => {
                return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              },
              placeholder: '请选择或输入账号名称',
            },
          },
          {
            field: 'customerNumber',
            title: '访客电话',
          },
          {
            field: 'checkUserName',
            title: '质检人',
            element: 'a-select',
            props: {
              options: this.checkPersonOptions,
              showSearch: true,
              filterOption: (input, option) => {
                return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              },
              placeholder: '请选择或输入质检人',
            },
          },
          // {
          //   field: 'classify',
          //   title: '工单分类',
          // },
          {
            field: 'secScore',
            title: '质检分数末尾',
            show: false,
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      //弹窗类配置
      return {
        formConfig: [
          {
            title: '访客电话',
            field: 'customerNumber',
            element: 'a-input',
            required: true,
            rules: [
              { required: true, message: '请输入访客电话' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的11位手机号' },
            ],
            props: {
              placeholder: '请输入11位手机号',
              maxLength: 11,
            },
          },
          {
            title: '通话类型',
            field: 'callType',
            element: 'a-select',
            defaultValue: '1',
            props: {
              options: this.callTypeOptions,
            },
          },
          {
            title: '坐席名称',
            field: 'clientName',
            element: 'a-auto-complete',
            props: {
              dataSource: this.clientNameOptions,
              allowClear: true,
              placeholder: '请输入坐席名称',
              filterOption: (inputValue, option) => {
                // 确保option.value存在且为字符串，避免toUpperCase报错
                if (!option || !option.value || typeof option.value !== 'string') {
                  return false;
                }
                const input = inputValue || '';
                return option.value.toUpperCase().indexOf(input.toUpperCase()) !== -1;
              },
              onSearch: this.handleClientNameSearch,
              onFocus: this.loadClientNameOptions,
            },
          },
          {
            title: '通话状态',
            field: 'communicateStatusFlag',
            element: 'a-select',
            props: {
              options: this.communicateStatusFlag,
              placeholder: '请选择通话状态',
              allowClear: true,
            },
          },
          {
            title: '账户名称',
            field: 'nickName',
            element: 'a-select',
            props: {
              options: this.accountNameOptions,
              showSearch: true,
              allowClear: true,
              placeholder: '请选择账户名称',
              filterOption: (input, option) => {
                return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              },
              onChange: this.handleAccountNameChange,
              onFocus: this.loadAccountNameOptions,
            },
          },
          {
            title: '归属地',
            field: 'location',
            element: 'a-cascader',
            props: {
              options: this.provinceOptions,
              placeholder: '请选择省市',
              allowClear: true,
              changeOnSelect: true,
              fieldNames: { label: 'areaName', value: 'areaCode', children: 'children' },
              onChange: this.handleLocationChange,
            },
          },
          {
            title: '呼入时间',
            field: 'startTime',
            element: 'a-date-picker',
            props: {
              showTime: true,
              format: 'YYYY-MM-DD HH:mm:ss',
              placeholder: '请选择呼入时间',
              allowClear: true,
            },
          },
          {
            title: '通话时长',
            field: 'callDuration',
            element: 'a-time-picker',
            props: {
              valueFormat: 'HH:mm:ss',
              placeholder: '请选择通话时长',
              allowClear: true,
            },
          },
        ],
        customOperationTypes: [
          {
            title: '查看',
            typeName: 'goView',
            event: (row) => {
              return new Promise((resolve) => {
                this.$store.dispatch('base/cumIdFn', row.communicateId);
                this.$store.dispatch('base/mainIdFn', row.mainUniqueId);
                this.$store.dispatch('base/numberFn', row.customerNumber);
                // this.$store.dispatch('base/orderIdFn', [row.orderId]);
                // console.log('看看orderId', this.$store.state.base.orderId);

                try {
                  let arr = [];
                  let arrStatus = [];
                  let arrTime = [];
                  for (const item of row.orderInfos) {
                    console.log('数组', item);
                    arr.push(item.orderId);
                    arrStatus.push(item.orderStatus);
                    arrTime.push(item.operationTime);
                  }
                  console.log('新添加的info的ID数组', arr);
                  this.$store.dispatch('base/orderIdFn', arr);
                  this.$store.dispatch('base/statusFn', arrStatus);
                  this.$store.dispatch('base/timeFn', arrTime);
                  console.log('storeOrderId', this.$store.state.base.orderId);
                  console.log('orderId的状态数组', this.$store.state.base.arrStatus);
                } catch (err) {
                  console.log('这个里面没有orderId数组');
                  this.$store.dispatch('base/orderIdFn', []);
                  this.$store.dispatch('base/statusFn', []);
                  this.$store.dispatch('base/timeFn', []);
                }

                console.log('row是哪些', row);
                this.$router.push('/telmanagerdetail');
                resolve();
              });
            },
          },
        ],
        menuFixed: 'right',
        menuTitle: '操作',
        addBtn: true,
        delBtn: true,
        viewBtn: false,
        editBtn: true,
        formLayoutConfig: {
          defaultColSpan: 12,
        },
      };
    },
  },
  mounted() {
    this.getDict();
    this.loadProvinceData();
    this.loadAccountNameOptions();
    this.loadClientNameOptions();
    this.loadCheckPersonOptions();
  },
  methods: {
    // changePage() {
    //   console.log('换页了');
    // },
    rowEdit(row) {
      // 处理编辑数据
      const editData = { ...row };

      // 处理归属地数据，根据provinceCode和cityCode构建location数组
      if (editData.provinceCode || editData.cityCode) {
        editData.location = [];
        if (editData.provinceCode) {
          editData.location.push(editData.provinceCode);
        }
        if (editData.cityCode) {
          editData.location.push(editData.cityCode);
        }
      }

      // 处理时间格式
      if (editData.startTime && typeof editData.startTime === 'string') {
        editData.startTime = moment(editData.startTime);
      }

      // 设置选中的账户信息
      if (editData.nickName && editData.user_id) {
        this.selectedAccountInfo = {
          nickName: editData.nickName,
          userId: editData.user_id,
        };
      }

      this.$refs.crud.switchModalView(true, 'UPDATE', editData);
    },
    //筛选重置按钮
    handleReset() {
      this.tablePage.currentPage = 1;
      this.params = {
        communicateId: '',
        bridgeTime: undefined,
        startScore: '',
        endScore: '',
        clientName: '',
        classify: '',
        startTime: '',
        endTime: '',
        checkStatus: '',
        communicateStatusFlag: '',
        callStatus: '',
        customerNumber: '',
        nickName: '',
        checkUserName: '',
      };
      this.loadData();
    },
    async loadData() {
      console.log('params筛选参数', this.params);
      this.loading = true;
      const result = await this.checkListFn();
      this.loading = false;
      this.tableData = result.data;
      console.log('看看表里的数据', this.tableData);
      this.tablePage.total = +result.count;
    },
    async modalConfirmHandler(formData) {
      try {
        // 处理表单数据
        const submitData = { ...formData };

        // 处理时间格式
        if (submitData.startTime && typeof submitData.startTime === 'object') {
          submitData.startTime = moment(submitData.startTime).format('YYYY-MM-DD HH:mm:ss');
        }

        // 处理账户名称数据，确保同时传递nickName和user_id
        if (this.selectedAccountInfo.userId) {
          submitData.user_id = this.selectedAccountInfo.userId;
          submitData.nickName = this.selectedAccountInfo.nickName;
        }

        // 处理归属地数据，分别设置省市信息
        if (submitData.location && Array.isArray(submitData.location)) {
          if (submitData.location.length >= 1) {
            // 省份信息
            const provinceInfo = this.findAreaInfo(submitData.location[0], this.provinceOptions);
            if (provinceInfo) {
              submitData.customerProvince = provinceInfo.areaName;
              submitData.provinceCode = provinceInfo.areaCode;
            }
          }
          if (submitData.location.length >= 2) {
            // 城市信息
            const provinceInfo = this.findAreaInfo(submitData.location[0], this.provinceOptions);
            if (provinceInfo && provinceInfo.children) {
              const cityInfo = this.findAreaInfo(submitData.location[1], provinceInfo.children);
              if (cityInfo) {
                submitData.customerCity = cityInfo.areaName;
                submitData.cityCode = cityInfo.areaCode;
              }
            }
          }
          // 移除location字段，因为接口不需要
          delete submitData.location;
        }

        const [result] = await saveDataAPI(submitData);

        if (result.code === '10000') {
          const operationType = submitData.communicateId ? '编辑' : '新增';
          this.$message.success(`${operationType}成功`);
          this.loadData(); // 重新加载数据
          return true; // 返回true表示保存成功，关闭弹窗
        } else {
          this.$message.error(result.message || '保存失败');
          return false; // 返回false表示保存失败，不关闭弹窗
        }
      } catch (error) {
        console.error('保存失败:', error);
        this.$message.error('保存失败，请稍后重试');
        return false;
      }
    },
    modalSubmit() {
      console.log('提交按钮');
    },
    modalCancelHandler() {
      console.log('取消按钮');
    },
    async deleteRowHandler(row) {
      this.$confirm({
        title: '确认删除',
        content: `确定要删除通话编号为 ${row.communicateId} 的记录吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          try {
            const [result] = await deleteDataAPI({ communicateId: row.communicateId });
            if (result.code === '10000') {
              this.$message.success('删除成功');
              this.loadData(); // 重新加载数据
            } else {
              this.$message.error(result.message || '删除失败');
            }
          } catch (error) {
            console.error('删除失败:', error);
            this.$message.error('删除失败，请稍后重试');
          }
        },
      });
    },
    rowAdd() {
      this.$refs.crud.switchModalView(true, 'ADD');
    },
    getSelectEvent() {
      // console.log('导出');
      // let selectRecords = this.$refs.crud.getCheckboxRecords();
      // console.log(selectRecords);
      // this.$message.info(`查询参数为：${JSON.stringify(selectRecords)}`);
    },
    async checkListFn() {
      let page = { pageSize: this.tablePage.pageSize, pageNum: this.tablePage.currentPage };
      let [result] = await checkListAPI({
        pageFlag: '1',
        ...page,
        ...this.params,
      });
      console.log('质检列表', result.data);
      return result;
    },
    //测试接口
    async testAPI() {
      let id = '20230921165845582371324861205017';
      let project = [{ checkTemplateId: '20230924022928883001778465070860', projectScore: '60' }];
      let [resultA] = await checkAPI({ orderId: id, project });
      console.log('check接口', resultA);

      let [resultB] = await checkDetailAPI('20230926143246344095070329883229');
      console.log('checkDetail接口', resultB);

      let [resultC] = await checkAppealAPI();
      console.log(resultC);
    },
    async exportList() {
      let page = { pageSize: this.tablePage.pageSize, pageNum: this.tablePage.currentPage };
      const communicateIds = this.$refs.crud.getCheckboxRecords()?.map((x) => x.communicateId);
      console.log('communicateIds', communicateIds);
      let [result] = await exportAPI({ ...this.params, ...page, pageFlag: '0', communicateIds: communicateIds });
      downLoadXlsx(new Blob([result]), '通话列表导出.xlsx');
    },

    startScoreChange(value) {
      console.log(value);
    },
    endScoreChange(value) {
      console.log(value);
    },
    changeTime(value) {
      const [startDate, endDate] = value;
      this.params.startTime = moment(startDate).format('YYYY-MM-DD HH:mm:ss');
      this.params.endTime = moment(endDate).format('YYYY-MM-DD HH:mm:ss');
      console.log('时间', this.params.startTime);
      console.log('时间', this.params.endTime);
    },
    getDict() {
      DictCodeAPI('check_status').then((res) => {
        this.checkStatus = res;
      });
      console.log('字典', this.checkStatus);

      DictCodeAPI('communicate_status_flag').then((res) => {
        this.communicateStatusFlag = res;
      });
      // let resA = await DictCodeAPI('');communicate_status_flag
    },

    // 获取省市数据
    async loadProvinceData() {
      try {
        const [result] = await getProvinceTreeAPI({});
        if (result.success && result.data) {
          this.provinceOptions = result.data.map((province) => ({
            areaCode: province.areaCode,
            areaName: province.areaName,
            children: (province.children || []).map((city) => ({
              areaCode: city.areaCode,
              areaName: city.areaName,
            })),
          }));
        } else {
          console.error('获取省市数据失败，使用本地数据');
          this.initProvinceDataFromLocal();
        }
      } catch (error) {
        console.error('获取省市数据失败，使用本地数据:', error);
        this.initProvinceDataFromLocal();
      }
    },

    // 备用方法：使用本地省市数据
    initProvinceDataFromLocal() {
      const provinceData = [];
      for (const [provinceName, cities] of Object.entries(province)) {
        const provinceItem = {
          areaCode: provinceName,
          areaName: provinceName,
          children: [],
        };

        if (typeof cities === 'object') {
          for (const [cityName] of Object.entries(cities)) {
            const cityItem = {
              areaCode: cityName,
              areaName: cityName,
            };
            provinceItem.children.push(cityItem);
          }
        }

        provinceData.push(provinceItem);
      }
      this.provinceOptions = provinceData;
    },

    // 根据areaCode查找区域信息
    findAreaInfo(areaCode, areaList) {
      return areaList.find((item) => item.areaCode === areaCode);
    },

    // 格式化通话时长
    formatDuration(duration) {
      if (!duration) return '';

      // 替换全角冒号为半角冒号
      const formatted = duration.replace(/：/g, ':');

      // 验证格式是否正确
      const timeRegex = /^([0-1]?[0-9]|2[0-3]):([0-5]?[0-9]):([0-5]?[0-9])$/;
      if (timeRegex.test(formatted)) {
        return formatted;
      }

      return duration; // 如果格式不正确，返回原值
    },

    // 加载账户名称选项
    async loadAccountNameOptions() {
      try {
        const [result] = await getUserListAPI({ nickName: '' });
        if (result.code === '10000' && result.data) {
          // 使用Map去重，保留第一个出现的用户信息
          const uniqueUsers = new Map();
          result.data.forEach((user) => {
            if (user.nickName && typeof user.nickName === 'string' && !uniqueUsers.has(user.nickName)) {
              uniqueUsers.set(user.nickName, user);
            }
          });

          this.accountNameOptions = Array.from(uniqueUsers.values()).map((user) => ({
            value: user.nickName,
            label: user.nickName,
            userId: user.userId,
            nickName: user.nickName,
          }));
        }
      } catch (error) {
        console.error('加载账户名称失败:', error);
        // 如果接口失败，设置空数组避免报错
        this.accountNameOptions = [];
      }
    },

    // 搜索账户名称
    async handleAccountNameSearch(value) {
      if (!value) {
        this.loadAccountNameOptions();
        return;
      }

      try {
        const [result] = await getUserListAPI({ nickName: value });
        if (result.code === '10000' && result.data) {
          // 使用Map去重，保留第一个出现的用户信息
          const uniqueUsers = new Map();
          result.data
            .filter((user) => user.nickName && typeof user.nickName === 'string' && user.nickName.includes(value))
            .forEach((user) => {
              if (!uniqueUsers.has(user.nickName)) {
                uniqueUsers.set(user.nickName, user);
              }
            });

          this.accountNameOptions = Array.from(uniqueUsers.values()).map((user) => ({
            value: user.nickName,
            label: user.nickName,
            userId: user.userId,
            nickName: user.nickName,
          }));
        }
      } catch (error) {
        console.error('搜索账户名称失败:', error);
      }
    },

    // 加载坐席名称选项
    async loadClientNameOptions() {
      try {
        const [result] = await queryClientNameAPI();
        if (result.code === '10000' && result.data) {
          // 使用Set去重，避免重复的坐席名称
          const uniqueClientNames = [
            ...new Set(
              result.data
                ?.filter((x) => !!x.clientName && typeof x.clientName === 'string')
                ?.map((client) => client.clientName)
            ),
          ];

          this.clientNameOptions = uniqueClientNames.map((clientName) => ({
            value: clientName,
            text: clientName,
          }));
          console.log('查询坐席名称', result.data, this.clientNameOptions);
        }
      } catch (error) {
        console.error('加载坐席名称失败:', error);
        this.clientNameOptions = [];
      }
    },

    // 搜索坐席名称
    async handleClientNameSearch(value) {
      if (!value) {
        this.loadClientNameOptions();
        return;
      }

      try {
        const [result] = await queryClientNameAPI();
        if (result.code === '10000' && result.data) {
          // 过滤并去重
          const filteredClientNames = [
            ...new Set(
              result.data
                ?.filter(
                  (client) =>
                    client.clientName && typeof client.clientName === 'string' && client.clientName.includes(value)
                )
                ?.map((client) => client.clientName)
            ),
          ];

          this.clientNameOptions = filteredClientNames.map((clientName) => ({
            value: clientName,
            text: clientName,
          }));
        }
      } catch (error) {
        console.error('搜索坐席名称失败:', error);
      }
    },

    // 加载质检人选项
    async loadCheckPersonOptions() {
      try {
        const [result] = await getUserListAPI({ nickName: '' });
        if (result.code === '10000' && result.data) {
          // 使用Set去重，避免重复的质检人
          const uniqueCheckPersons = [
            ...new Set(
              result.data
                ?.filter((user) => {
                  const name = user.userName || user.name;
                  return name && typeof name === 'string';
                })
                ?.map((user) => user.userName || user.name)
            ),
          ];

          this.checkPersonOptions = uniqueCheckPersons.map((name) => ({
            value: name,
            label: name,
          }));
        }
      } catch (error) {
        console.error('加载质检人失败:', error);
        this.checkPersonOptions = [];
      }
    },

    // 处理账户名称选择
    handleAccountNameChange(_, option) {
      if (option) {
        this.selectedAccountInfo = {
          nickName: option.nickName,
          userId: option.userId,
        };
      } else {
        this.selectedAccountInfo = {};
      }
    },

    // 处理归属地选择
    handleLocationChange(value, selectedOptions) {
      // 这里可以添加额外的处理逻辑，如果需要的话
      console.log('选择的归属地:', value, selectedOptions);
    },
  },
  beforeMount() {
    this.loadData();
  },
};
</script>

<style lang="less" scoped>
.button-right {
  margin-right: 16px;
}
.tag-place {
  display: flex;
  justify-content: flex-end;
  top: -36px;
  position: relative;
  .tag {
    position: absolute;
    height: 32px;
    line-height: 32px;
  }
}
.name-button {
  margin-right: 16px;
}
.guest-tag {
  display: flex;
  justify-content: space-around;
  .tags {
    white-space: nowrap;
    line-height: 32px;
    height: 32px;
  }
}
.icon-plus {
  margin-right: 4px;
}
//质检按钮
.check-router {
  display: inline-block;
  margin-right: 10px;
  color: #165dff;
  cursor: pointer;
  padding: 0;
  min-width: 38px;
  height: 30px;
  line-height: 30px;
}
</style>
