import Editor from '@/components/Editor/ProductEditor';
import Tags from '@/views/home/<USER>';
import { DictCodeAPI } from '@/api/system/dict';
import { mapState } from 'vuex';
import {
  workTypeAPI,
  listAPI,
  flowTypeAPI,
  getAutoAllocationAPI,
  addAPI,
  checkPhone,
  stationList,
  maintenanceList,
  retry,
} from '@/api/system/callBench';
import { customerListAPI } from '@/api/customer';
import * as api from '@/api/system/callBench';
import { getHistoryCall } from '@/api/system/workorder';
import { message } from 'ant-design-vue';
import { cutString, defaultDict, getFatherBySon, setCustomSearchTree } from '@/utils/system/common';
import AddReply from '@/components/Editor/AddReply.vue';
import moment from 'moment';
import StatuNode from '@/components/TreeNode/StatuNode.vue';

export default {
  name: 'DumiDocVueIndex',
  components: {
    Editor,
    Tags,
    AddReply,
    StatuNode,
  },
  props: {
    showCall: {
      type: Boolean,
      default: true,
    },
    showOk: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      workType: [{}], //工单分类接收
      isWorkTypeFn: false, //工单分类是否选择过，避免重复发请求
      nowCategoryID: '', //当前分类受理id，用于递归判断
      handleList: [], //没有受理组时候，用到的表
      flowRadio: '1', //流转方式，默认人工，有设置就改
      enabledFlag: '1', //涉及到flowRadio的显示，一个开关，默认我开了"1"
      templateId: 0, //工单模板id
      exList: [], //模板分类后渲染样式接收（无值）
      modChoice: undefined, //选择第几个模板，避免模板v-for嵌套
      showMod: false, //因为工单模板不是必选，所以需要一开始给一个不传值状态
      contentInput: '', //富文本框内容
      orderTags: [], //工单标签，来自guestTags组件
      orderName: [], //工单标签名称
      showTags: false, //是否展示Tags的select框;同时控制按钮

      showGuest: false,
      man: false, //先生
      lady: false, //女士
      gen: 0, //这个是给重复输入避免丢失先生/女士准备的
      visitorSex: 0, //访客性别
      modalSeen: false, //弹窗的显示与否，作用与flowRadio的是否紧急提示
      prevFlow: '3', //保存的紧急
      nowFlow: '3', //现在的紧急
      customerNumber: '',

      tableList: {
        id: '',
        priority: '',
        classify: '',
        status: '',
        from: '',
        transMod: '',
        tel: '',
        subTime: '',
        conductor: '',
      }, //表新增参数
      showHistoryCall: false,
      historyCallList: [], // 历史通话联动手机号数据
      filterCallList: [], // 过滤用
      nowCallId: '', // 绑定通话记录的id
      selectOne: [],
      selectCom: [],
      tempForm: {}, // 模板表单
      tempRules: {},
      filterHandler: [], //过滤用
      showTemp: false,
      autoDisable: false, // 选择受理人禁用
      showFlowType: false, // 显示流转方式按钮
      addFileList: [], // 附件
      visitorRules: {}, // 访客规则
      editFlag: false,
      autoFlag: false, // 自动分配选项禁用
      stationOptions: [], //站点
      maintenanceOptions: [], //运维受理人
      showFailModal: false, //工单创建失败弹窗
      failOrderId: undefined,
      modalLoading: false,
      frontDataZ: [], //存放前100的数据
      isLoading: false,
      currentPage: 1,
      stationValue: '',
      stationRequired: false,
      editStationId: '',
      editStationName: '',
      failMsg: '同步创建工单失败',

      // 销售工单相关字段
      isSaleOrder: false, // 是否为销售工单
      customerOptions: [], // 客户选项列表
      customerLoading: false, // 客户加载状态
      faceInterviewFlag: '0', // 是否需要面访 0-不需要 1-需要
      faceInterviewTime: null, // 面访时间
      outboundCallTime: null, // 预约外呼时间
    };
  },
  computed: {
    ...mapState({
      merchant: (state) => state.base.merchant || {},
    }),
    modalConfig() {
      //弹窗类配置
      return {
        formConfig: [
          {
            field: 'workTypeId',
            title: '工单分类',
            element: 'a-tree-select',
            props: {
              disabled: this.editFlag,
              treeData: this.workType,
              // loadData: this.loadCategoryNow,
              replaceFields: {
                title: 'categoryName',
                value: 'categoryId',
              },
              showSearch: true,
              treeNodeFilterProp: 'title',
              dropdownStyle: { maxHeight: '400px', overflow: 'auto' },
              getPopupContainer: (triggerNode) => triggerNode.parentNode,
            },
            on: {
              //工单分类选择
              change: async (value) => {
                this.nowCategoryID = value;

                // 检查是否为销售工单
                this.checkSaleOrder(value);

                await this.flowTypeFn();
                this.showTemp = false;
                await this.getMod();
                if (this.exList && this.exList.length > 0) {
                  this.showTemp = true;
                  await this.handleChange(0);
                } else {
                  this.modChoice = undefined;
                  this.showMod = false;
                }
              },
              click: () => {
                this.workTypeFn();
              },
              search: (value) => {
                this.loadCategoryNow(value);
              },
            },
            rules: [{ required: true, message: '请选择工单分类' }],
          },
          {
            field: 'customerId',
            title: '选择客户',
            element: 'a-select',
            props: {
              options: this.customerOptions,
              showSearch: true,
              filterOption: false,
              placeholder: '选择客户管理中已维护的客户手机号，仅支持单选',
              loading: this.customerLoading,
              allowClear: true,
              getPopupContainer: (triggerNode) => triggerNode.parentNode,
            },
            on: {
              search: this.handleCustomerSearch,
              focus: () => {
                if (this.customerOptions.length === 0) {
                  this.loadCustomerOptions();
                }
              },
              change: (value) => {
                if (value) {
                  const selectedCustomer = this.customerOptions.find((option) => option.value === value);
                  if (selectedCustomer) {
                    this.$refs.crud.setFormFields({
                      customerName: selectedCustomer.customerName,
                      phoneNumber: selectedCustomer.phoneNumber,
                    });
                  }
                }
              },
            },
            rules: [{ required: this.isSaleOrder, message: '请选择客户' }],
            show: this.isSaleOrder,
          },
          {
            field: 'faceInterviewFlag',
            title: '是否需要面访',
            element: 'a-radio-group',
            defaultValue: '0',
            props: {
              options: [
                { label: '不需要', value: '0' },
                { label: '需要', value: '1' },
              ],
            },
            on: {
              change: (e) => {
                this.faceInterviewFlag = e;
                if (e === '0') {
                  this.faceInterviewTime = null;
                  this.$refs.crud.setFormFields({ faceInterviewTime: null });
                }
              },
            },
            show: this.isSaleOrder,
          },
          {
            field: 'faceInterviewTime',
            title: '面访时间',
            element: 'a-date-picker',
            props: {
              showTime: { format: 'HH:mm' },
              format: 'YYYY-MM-DD HH:mm',
              placeholder: '请选择面访时间',
              getPopupContainer: (triggerNode) => triggerNode.parentNode,
            },
            rules: [{ required: this.isSaleOrder && this.faceInterviewFlag === '1', message: '请选择面访时间' }],
            show: this.isSaleOrder && this.faceInterviewFlag === '1',
          },
          {
            field: 'outboundCallTime',
            title: '预约外呼时间',
            element: 'a-date-picker',
            props: {
              showTime: { format: 'HH:mm' },
              format: 'YYYY-MM-DD HH:mm',
              placeholder: '请选择预约外呼时间',
              getPopupContainer: (triggerNode) => triggerNode.parentNode,
            },
            show: this.isSaleOrder,
          },
          {
            field: 'problemDescription',
            title: '问题描述',
            element: 'slot',
            slotName: 'orContent',
            show: this.isSaleOrder,
          },
          {
            field: 'phoneNumber',
            title: '工单手机号',
            element: 'slot',
            slotName: 'phoneHistory',
            rules: [
              {
                required: !this.isSaleOrder && this.merchant.merchantId != '40002',
                message: '请输入正确的电话号码',
                pattern: /^\d+$/,
              },
            ],
            //租户id为目的地租户40002时展示几个新增字段
            show: !this.isSaleOrder && this.merchant.merchantId != '40002',
          },
          {
            field: 'orderContent',
            title: '工单内容',
            element: 'slot',
            slotName: 'orContent',
            rules: [{ required: !this.isSaleOrder, message: '请输入工单内容' }],
            show: !this.isSaleOrder,
          },
          {
            field: 'phoneNumber',
            title: '用户手机号',
            element: 'slot',
            slotName: 'phoneHistory',
            // rules: [
            //   { required: this.merchant.merchantId == '40002', message: '请输入正确的电话号码', pattern: /^\d+$/ },
            // ],
            //租户id为目的地租户40002时展示几个新增字段
            show: !this.isSaleOrder && this.merchant.merchantId == '40002',
          },
          {
            field: 'userName',
            title: '用户姓名',
            element: 'a-input',
            rules: [{ max: 100, message: '100字符以内' }],
            show: !this.isSaleOrder && this.merchant.merchantId == '40002',
          },
          {
            field: 'city',
            title: '所在城市',
            element: 'a-input',
            rules: [{ max: 200, message: '200字符以内' }],
            show: !this.isSaleOrder && this.merchant.merchantId == '40002',
          },
          {
            field: 'stationId',
            title: '站点名称',
            // element: 'slot',
            // slotName: 'stationId',
            element: 'a-select',
            props: {
              options: this.stationOptions,
              showSearch: true,
              // filterOption: (input, option) => {
              //   return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              // },
              filterOption: false,
            },
            on: {
              popupScroll: (event) => {
                // console.log(event);
                const { scrollTop, offsetHeight, scrollHeight } = event.target;
                if (scrollTop + 2 + offsetHeight >= scrollHeight) {
                  // 检测到滚动到底部
                  this.getStationOptions();
                }
              },
              search: async (val) => {
                this.stationValue = val;
                this.stationOptions = [];
                this.currentPage = 1;
                await this.getStationOptions();
              },
            },
            show: !this.isSaleOrder && this.merchant.merchantId == '40002',
            rules: [
              {
                required: !this.isSaleOrder && this.stationRequired && this.merchant.merchantId == '40002',
                message: '请选择站点名称',
              },
            ],
          },
          {
            field: 'vehicle',
            title: '车型品牌',
            element: 'slot',
            slotName: 'vehicle',
            rules: [{ max: 100, message: '100字符以内' }],
            show: !this.isSaleOrder && this.merchant.merchantId == '40002',
          },
          {
            field: 'orderStatus',
            title: '工单状态',
            element: 'a-select',
            defaultValue: '7',
            props: {
              //工单状态列表（1-处理中 2-待回访 3-催办中 4-技术处理中 5-无需处理 6-已处理 7-已完结 8-运营商处理中）
              options: [
                { label: '处理中', value: '1' },
                { label: '处理中（重新处理）', value: '11' },
                { label: '待回访', value: '2' },
                // { label: '催办中', value: '3' },
                // { label: '技术处理中', value: '4' },
                { label: '无需处理', value: '5' },
                // { label: '已处理', value: '6' },
                { label: '已完结', value: '7' },
                // { label: '运营商处理中', value: '8' },
              ],
              disabled: this.editFlag,
              getPopupContainer: (triggerNode) => triggerNode.parentNode,
            },
            rules: [{ required: true, message: '请选择工单状态' }],
            on: {
              select: async (value) => {
                if (value === '7') {
                  this.stationRequired = false;
                  this.showFlowType = false;
                } else {
                  this.showFlowType = true;
                  await this.flowTypeFn();
                  if (value === '2') {
                    this.stationRequired = false;
                  } else {
                    this.stationRequired = true;
                  }
                }
              },
            },
          },
          {
            field: 'urgency',
            title: '工单优先级',
            element: 'a-radio-group',
            defaultValue: '3',
            props: {
              disabled: this.editFlag,
              options: [
                { label: '非常紧急', value: '1' },
                { label: '紧急', value: '2' },
                { label: '一般', value: '3' },
                { label: '低', value: '4' },
              ],
              rules: [{ required: true, message: '请选择优先级' }],
            },
            on: {
              change: (value) => {
                if (value == '1' || value == '2') {
                  this.nowFlow = value;
                  this.modalSeen = true;
                } else {
                  this.prevFlow = value;
                }
              },
            },
          },
          {
            field: 'flowType',
            title: '流转方式',
            element: 'slot',
            slotName: 'flowType',
            props: {
              disabled: this.editFlag,
              rules: [{ required: true, message: '请选择流转方式' }],
            },
            show: this.showFlowType,
          },
          {
            field: 'maintenanceUser',
            title: '运维受理人',
            element: 'a-select',
            props: {
              options: this.maintenanceOptions,
              showSearch: true,
              filterOption: (input, option) => {
                return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              },
            },
            show: this.merchant.merchantId == '40002' && !this.isSaleOrder,
          },
          {
            field: 'orderHandler',
            title: '工单受理人',
            element: 'slot',
            slotName: 'orderHandler',
            // rules: [{ required: this.flowRadioShow && this.showFlowType, message: '请选择工单受理人' }],
            show: this.flowRadioShow && this.showFlowType,
          },
          {
            field: 'orderTemplateId',
            title: '工单模板',
            element: 'slot',
            slotName: 'selectOr',
            show: this.showTemp,
            colProps: {
              span: 24,
            },
          },
          {
            field: 'not',
            title: '创建新访客',
            element: 'slot',
            slotName: 'newGuest',
            colProps: {
              span: 24,
            },
          },
          {
            field: 'orderTags',
            title: '工单标签',
            element: 'slot',
            slotName: 'guestTag',
          },
        ],
        menu: false,
        addBtn: false,
        delBtn: false,
        viewBtn: true,
        editBtn: false,
        submitBtn: true,
        okText: '保存草稿',
        okBtn: this.showOk,
        okBtnVerify: false,
      };
    },
    NumberChoice() {
      //数字转化
      return Number(this.modChoice);
    },
    //由于buse组件不支持show内部写函数，那就用computed实现flowType为3时不显示受理人和受理组
    flowRadioShow() {
      return this.flowRadio !== '3';
    },
    choiceExList() {
      return this.exList[this.NumberChoice] || [];
    },
  },
  mounted() {
    this.loadUserList();
    // this.getStationOptions();
    this.getMaintenanceOptions();
  },
  methods: {
    //创建工单失败-重试
    async resendOrder() {
      this.modalLoading = true;
      const method = this.failMsg === '同步创建工单台账失败' ? 'retryLedger' : 'retry';
      const [res] = await api[method]({ orderId: this.failOrderId });
      this.modalLoading = false;
      if (res?.success) {
        this.showFailModal = false;
        this.$success({
          title: res.data,
          okText: '我知道了',
        });
        this.$emit('refreshList');
      }
    },
    // 获取站点信息列表
    async getStationOptions() {
      if (this.isLoading) {
        return;
      }
      this.isLoading = true;
      const params = { pageNum: this.currentPage, pageSize: 10, stationName: this.stationValue };
      const [res] = await stationList(params);
      const newOptions = res?.data?.map((x) => {
        return { ...x, label: x.stationName, value: x.stationId };
      });
      if (newOptions.length > 0) {
        this.stationOptions = this.stationOptions.concat(newOptions);
        this.currentPage++;
      }
      console.log(this.stationOptions);
      this.isLoading = false;
    },
    //获取运维人员列表
    async getMaintenanceOptions() {
      const [res] = await maintenanceList({});
      this.maintenanceOptions = res?.data?.map((x) => {
        return { ...x, label: x.userName + '-' + x.nickName, value: x.userId };
      });
    },
    // 人员列表
    loadUserList() {
      this.$store.dispatch('base/GetUserList', { nickName: '' }).then((res) => {
        this.handleList = this.filterHandler = setCustomSearchTree(res);
      });
    },
    // 工单分类及时过滤
    async loadCategoryNow(search) {
      let [result] = await workTypeAPI({ categoryName: search });
      this.workType = result.data;
    },
    modalCancelHandler() {
      this.showMod = false; //提交后变回去
      this.showGuest = false; //提交后变回去
      this.orderTagList = [];
      this.orderTags = [];
      this.orderName = [];
      this.contentInput = '';
      this.visitorSex = 0;
      this.man = this.lady = false;
      this.showTemp = false;
      this.showFlowType = false;
      this.nowCategoryID = '';
      this.exList = [];
      this.modChoice = undefined;

      // 重置销售工单相关字段
      this.isSaleOrder = false;
      this.customerOptions = [];
      this.faceInterviewFlag = '0';
      this.faceInterviewTime = null;
      this.outboundCallTime = null;
    },
    rowAdd(data, edit) {
      if (edit) {
        this.currentPage = 1;
        this.stationOptions = [];
        this.getStationOptions();
        this.reShowOrder(data);
      } else {
        this.editFlag = false;
        this.$refs.crud.switchModalView(true, 'ADD');
      }
    },
    async reShowOrder(data) {
      // 模板校验要在弹框之前，受理人要在弹框之后
      // 0. 编辑时禁掉部分选项
      this.editFlag = data.commitFlag === '1';
      // 1. 获取工单分类，选中当前分类
      this.nowCategoryID = data.categoryId;
      this.nowCallId = data.mainUniqueId;
      this.showFlowType = data.flowType ? true : false;
      this.stationRequired = data.orderStatus == '2' || data.orderStatus == '7' ? false : true;
      this.editStationId = data.stationId;
      this.editStationName = data.stationName;
      await this.workTypeFn();
      // 2. 选择模板
      await this.getMod();
      if (data.templateId && data.extend !== 'null') {
        this.showTemp = true;
        const res = this.exList.findIndex((item) => {
          return item.templateId === data.templateId;
        });
        // 3. 展示模板前更新模板value // 避免未选到模板
        await this.reShowTemp(data, res);
        // 该分类下有模板才默认选择
      } else if (this.exList && this.exList.length > 0) {
        this.showTemp = true;
        await this.handleChange(0);
      }
      console.log('弹出弹窗！！！', data);
      // 4. 弹出弹窗
      this.$refs.crud.switchModalView(true, 'UPDATE', {
        userName: undefined,
        city: undefined,
        stationId: undefined,
        vehicleBrand: undefined,
        vehicleModel: undefined,
        maintenanceUser: undefined,
        ...data,
        vehicleYear: {
          endOpen: false,
          startOpen: true,
          startValue: data.vehicleYear,
          endValue: null,
        },
        workTypeId: [data.categoryId],
        stationId: this.editStationName,
      });
      // 5. 选择流转方式
      await this.selectFlowType(data.flowType, data.handleUser, data.handleGroup);
      // 6. 工单标签
      if (data.orderTag) {
        this.reShowTag(data.orderTag);
      }
      // 7. 新访客
      if (data.visitorName || data.visitorPhone) {
        this.showGuestfn(data);
      }
      // 8. 附件
      if (data.attachment && data.attachment !== 'null') {
        this.addFile(data.attachment);
      }
    },
    listAdd(editData) {
      this.currentPage = 1;
      this.stationOptions = [];
      this.getStationOptions();
      if (editData) {
        this.resShowOrder(data);
      } else {
        this.stationRequired = false;
        this.editFlag = false;
        this.failOrderId = undefined;
        this.$refs.crud.switchModalView(true, 'ADD');
      }
    },
    callbackContent(key) {
      if (key === '2') {
        console.log('你好');
      }
    }, //标签页切换
    spell(name, gen) {
      if (gen == 1) {
        //点击先生时候
        if (!this.man) {
          //初次点击，以及切换
          this.visitorName = name + '先生';
          this.lady = false;
          this.man = !this.man;
          this.visitorSex = 1;
        } else if (this.man) {
          //重复点击同一性别，也就是取消
          this.visitorName = name;
          this.man = !this.man;
          this.visitorSex = 0;
        }
      } else if (gen == 2) {
        if (!this.lady) {
          this.visitorName = name + '女士';
          this.man = false;
          this.lady = !this.lady;
          this.visitorSex = 2;
        } else if (this.lady) {
          this.visitorName = name;
          this.lady = !this.lady;
          this.visitorSex = 0;
        }
      } else if (gen == 3) {
        //gen == 3代表不选，我就点着玩
        if (this.lady) {
          this.visitorName = name + '女士';
          this.visitorSex = 2;
        } else if (this.man) {
          this.visitorName = name + '先生';
          this.visitorSex = 1;
        }
      }
      console.log('现在visitorSex' + this.visitorSex);
      console.log(this.visitorName);
    },
    nameFn(name) {
      this.tableList.visitorName = name;
      if (this.man || this.lady) {
        // 在有性别选择了的时候才会执行
        this.spell(name, 3);
      }
      console.log(this.tableList.visitorName);
    },
    showGuestfn(params) {
      this.visitorName = params.visitorName;
      this.visitorPhone = params.visitorPhone;
      this.$refs.crud.setFormFields({
        visitorName: params.visitorName,
        visitorPhone: params.visitorPhone,
      });
      this.showGuest = !this.showGuest;
      this.visitorRules = {
        visitorName: [{ required: this.showGuest, message: '请填写访客名称', trigger: 'blur' }],
        visitorPhone: [
          {
            required: this.showGuest,
            message: '输入电话号码格式不正确',
            pattern: /^\d+$/,
            trigger: 'blur',
          },
          {
            validator: async (rule, value, callback) => {
              if ((params.visitorInfoId || params.commitFlag === '0') && value === params.visitorPhone) {
                return callback();
              } else {
                const res = await checkPhone(value);
                if (res[0] === undefined) {
                  callback('电话号码已被占用');
                } else {
                  callback();
                }
              }
            },
            trigger: 'blur',
          },
        ],
      };
    },
    async reShowTemp(data, res) {
      this.showMod = true;
      this.modChoice = res;
      const reTemp = data.extend ? JSON.parse(data.extend) : [];
      this.templateId = this.choiceExList.templateId;
      // 3.1 获得模板数据和字段列表
      await Promise.all(
        this.exList[this.NumberChoice].fields.map(async (item) => {
          let options = [];
          let [field] = reTemp.filter((field) => {
            return field.fieldName === item.fieldName;
          });
          let fieldValue = field?.value;
          if (item.fieldDataKey) {
            options = await this.getModSecSelect(item.fieldDataKey);
            if (fieldValue && fieldValue.includes('[')) {
              fieldValue = eval(fieldValue);
            }
          }
          return {
            ...item,
            options: options ? options : null,
            value: fieldValue,
          };
        })
      ).then((res) => {
        this.exList[this.NumberChoice].fields = res;
      });
      // 3.2 添加规则并传递数据
      this.choiceExList.fields.forEach((item) => {
        this.tempRules[item.templateFieldId] = [
          { required: item.emptyFlag === '0', message: '请输入', trigger: ['blur', 'change'] },
        ];
        const fieldName = item.templateFieldId;
        this.$set(this.tempForm, fieldName, item.value);
      });
    },
    //工单模板选择
    async handleChange(value) {
      this.showMod = true;
      this.modChoice = value;
      this.templateId = this.choiceExList.templateId;
      console.log('展示默认模板', this.modChoice, this.templateId);
      await Promise.all(
        this.exList[this.NumberChoice].fields.map(async (item) => {
          let options = [];
          if (item.fieldDataKey) {
            options = await this.getModSecSelect(item.fieldDataKey);
          }
          return {
            ...item,
            options: options ? options : null,
          };
        })
      ).then((res) => {
        this.exList[this.NumberChoice].fields = res;
      });
      // 设置newRule防止第一次切换无校验
      let newRule = {};
      this.choiceExList.fields.forEach((item) => {
        newRule[item.templateFieldId] = [
          { required: item.emptyFlag === '0', message: '请输入', trigger: ['blur', 'change'] },
        ];
        if (item.templateFieldId) {
          const fieldName = item.templateFieldId;
          this.$set(this.tempForm, fieldName, item.defaultValue);
        }
      });
      this.tempRules = newRule;
    },
    // 添加附件
    addFile(param) {
      if (typeof param === 'string') {
        param = JSON.parse(param);
      }
      param = param.map((item) => ({
        uid: item.uid,
        url: item.url,
        name: item.name,
      }));
      this.addFileList = param;
    },
    //提交数据处理
    async modalSubmit(param, commitFlag) {
      let { vehicleYear, ...tableList } = param;
      console.log(vehicleYear);
      tableList.vehicleYear = vehicleYear?.startValue ? moment(vehicleYear.startValue).format('yyyy') : '';
      tableList.orderSource = '3'; //手工录入
      tableList.maintenanceUserName = this.maintenanceOptions?.find(
        (x) => x.value == tableList.maintenanceUser
      )?.nickName;
      //编辑时站点名称如果没有修改，则保留原来的id和name
      if (tableList.stationId === this.editStationName) {
        tableList.stationId = this.editStationId;
        tableList.stationName = this.editStationName;
      } else {
        tableList.stationName = this.stationOptions?.find((x) => x.value == tableList.stationId)?.stationName;
      }
      console.log(tableList, '提交！');
      // 分别校验工单模板与新访客
      let tempFlag = true;
      let visitorFlag = true;
      if (this.showGuest) {
        try {
          await this.$refs.visitorForm.validate();
        } catch {
          visitorFlag = false;
        }
      }
      if (this.showMod && this.showTemp) {
        try {
          await this.$refs.templateForm.validate();
        } catch {
          tempFlag = false;
        }
        const exFields = this.exList[this.NumberChoice];
        if (exFields && exFields.fields && exFields.fields.length !== 0) {
          tableList.templateId = this.choiceExList.templateId;
          this.exList[this.NumberChoice].fields = exFields.fields.map((item) => {
            item = {
              ...item,
              value: this.tempForm[item.templateFieldId],
            };
            // 修改日期格式
            if (item.fieldType === '3') {
              item.value = moment(item.value).format('YYYY-MM-DD HH:mm:ss');
            }
            return item;
          });
        }
        tableList.field = this.exList[this.NumberChoice].fields || []; //加入模版参数
      } else {
        tableList.field = null;
      }
      if (!tempFlag || !visitorFlag) return false;
      this.$refs.crud.setFormFields({ orderContent: this.contentInput });
      tableList.categoryId = this.nowCategoryID;
      tableList.order = this.templateId;
      tableList.flowType = this.showFlowType ? this.flowRadio : '';
      tableList.urgency = this.prevFlow;
      if (this.orderTags) {
        tableList.orderTags = JSON.parse(JSON.stringify(this.orderTags));
      } else {
        tableList.orderTags = this.orderTags;
      }

      //如果流转方式是3，那么到底会传入什么样的值？拭目以待。
      if (!this.flowRadioShow) {
        tableList.handleGroup = '';
        tableList.handleUser = '';
        console.log('由于选了第三个，所以tableList里面group和ueser没值！看后端之后怎么操作工单池流转');
      } else if (tableList.orderHandler && this.showFlowType) {
        const handler = tableList.orderHandler.value;
        tableList.handleUser = cutString(handler, '_');
        const [handleGroup] = getFatherBySon(handler, this.handleList);
        tableList.handleGroup = cutString(handleGroup.value, '_');
        delete tableList.orderHandler;

        console.log('qweqwe', handler, tableList, handleGroup);
      }
      // 绑定通话记录
      tableList.mainUniqueId = this.nowCallId;
      // 操作标识
      tableList.commitFlag = commitFlag ? 0 : 1;
      tableList.visitorSex = this.visitorSex;
      // 附件上传
      tableList.attachmentList = this.addFileList;
      tableList.saleOrderFlag = this.isSaleOrder ? '1' : '0';
      // 销售工单特殊字段处理
      if (this.isSaleOrder) {
        // 获取选中的客户信息
        if (tableList.customerId) {
          const selectedCustomer = this.customerOptions.find((option) => option.value === tableList.customerId);
          if (selectedCustomer) {
            tableList.customerName = selectedCustomer.customerName;
            tableList.phoneNumber = selectedCustomer.phoneNumber;
          }
        }

        // 面访相关字段
        tableList.faceInterviewFlag = this.faceInterviewFlag;
        if (this.faceInterviewFlag === '1' && tableList.faceInterviewTime) {
          tableList.faceInterviewTime = moment(tableList.faceInterviewTime).format('YYYY-MM-DD HH:mm:ss');
        }

        // 预约外呼时间
        if (tableList.outboundCallTime) {
          tableList.outboundCallTime = moment(tableList.outboundCallTime).format('YYYY-MM-DD HH:mm:ss');
        }

        // 问题描述（复用工单内容）
        if (tableList.problemDescription) {
          tableList.problemDescription = this.contentInput;
        }
      }

      let [result, err] = await this.addOrderFn(tableList);
      console.log('看下结果', result, err);

      if (result?.code == '10000') {
        if (commitFlag) {
          this.$notification.open({
            message: '成功',
            description: '内容编辑成功',
            icon: <a-icon type="smile" style="color: #108ee9" />,
          });
        } else {
          this.$success({
            title: result.data,
            okText: '我知道了',
          });
        }
      } else if (err?.code == '60000') {
        console.log(commitFlag, '---commitFlag');
        if (commitFlag) {
          console.log('回显失败', err);
          this.$notification.open({
            message: '创建失败',
            description: '工单内容操作失败',
            icon: <a-icon type="exclamation" />,
          });
        } else {
          this.showFailModal = true;
          this.failMsg = err.message;
          this.failOrderId = err.subCode;
        }
      }

      this.modalCancelHandler();
    },
    // 保存草稿
    modalConfirmHandler(tableList) {
      console.log('before submit');
      this.modalSubmit(tableList, true);
    },
    // 回显tag
    async reShowTag(value) {
      defaultDict('order_tag').then((res) => {
        // 多标签
        if (value.includes(',')) {
          value = value.split(',');
          this.orderName = value.map((item) => {
            const label = res.find((node) => node.value === item);
            return label.label;
          });
        } else {
          // 单标签
          let [result] = res.filter((item) => item.value === value);
          this.orderName.push(result.label);
        }
      });
    },
    //tag子组件
    async transTags(value) {
      this.orderName = [];
      this.orderTags = [];
      this.orderTags = value;
      let result = await DictCodeAPI('order_tag');
      console.log('工单标签', value, result);
      try {
        this.orderName = value.map((value) => {
          const item = result.find((obj) => obj.value == value);
          return item ? item.label : null;
        });
        console.log(this.orderName);
      } catch (err) {
        console.log('看起来你没输入tag');
      }
    },
    changeAddTags(value) {
      this.showTags = !value;
    },

    //选择无模板时候，因为没有内容会报错，但是为了不改变之前的结构，选择捕获错误，顺便改showMod为false
    NoWrongHere() {
      try {
        console.log('你好，我报了个错');
      } catch {
        console.log('然后我不想改了');
      } finally {
        this.showMod = false;
      }
    },
    //获取工单分类
    async workTypeFn() {
      if (this.isWorkTypeFn == false) {
        let [result] = await workTypeAPI();
        if (result.code === '10000') {
          this.workType = result.data;
        }
        this.isWorkTypeFn = true;
      }
    },
    // 流转方式改变
    async selectFlowType(flowType, handleUser, handleGroup) {
      this.flowRadio = flowType;
      // 没有选择分类不操作
      if (!this.nowCategoryID) return;
      // 刷新人员组织树
      this.loadUserList();
      // 首先判断是否属于回显
      // 回显必须要await 不然界面会在值更新前渲染
      let [result] = await getAutoAllocationAPI(this.nowCategoryID);
      if (handleUser) {
        const [findGroup] = this.handleList.filter((item) => item.value.includes(handleGroup));
        const [findUser] = findGroup.children.filter((item) => item.value.includes(handleUser));
        console.log('找到人了', findUser, findGroup);
        if (findUser) {
          const handler = findUser.value;
          console.log('==回显受理人员==', handler, this.handleList);
          this.$refs.crud.setFormFields({ orderHandler: { value: handler } });
          if (flowType === '2') {
            this.flowRadio = '2';
            this.autoDisable = true;
          } else {
            this.autoDisable = false;
          }
        }
      } else {
        // 不属于回显则根据分类进行操作
        const { data } = result;
        console.log('有没有自动分配人员？', data);
        if (flowType == '2') {
          // 判断该分类是否有绑定人员
          if (JSON.stringify(data) === '{}') {
            console.log('==没有==');
            this.flowRadio = '1';
            this.autoFlag = true;
            let test = { target: { value: '1' } };
            this.choiceManFlow(test);
          } else {
            console.log('==有==');
            this.flowRadio = '2';
            this.autoDisable = true;
            this.autoFlag = false;
            const handlerId = data.handleUserId + '_' + data.handleUserName;
            const [autoManGroup] = this.handleList.filter((item) => item.value.includes(data.handleGroupId));
            const [autoManInline] = autoManGroup.children.filter((item) => item.value.includes(data.handleUserId));
            console.log('自动分配人员信息', autoManInline);
            // 判断人员是否离线
            if (autoManInline.userStatus == '3') {
              this.flowRadio = '3';
              this.autoFlag = true;
            } else {
              this.$refs.crud.setFormFields({ orderHandler: { value: handlerId } });
            }
          }
        } else if (flowType == '1') {
          this.autoDisable = false;
        }
      }
    },
    //根据工单分类获取默认流转方式
    async flowTypeFn() {
      if (!this.nowCategoryID) return;
      let [result] = await flowTypeAPI(this.nowCategoryID);
      if (result.code === '10000') {
        console.log('??', result);
        //是否显示后两个选项
        this.enabledFlag = result.data.enabledFlag;
        let autoFlag = result.data.autoFlag;
        // 0人工，1自动
        if (autoFlag == '0') {
          this.flowRadio = '1';
          this.autoDisable = false;
        } else if (autoFlag == '1') {
          this.autoDisable = true;
          await this.selectFlowType(result.data.flowType);
        }
      } else {
        console.error(result);
      }
    },
    //人工流转，自动分配，工单池分配三个选择后触发事件
    async choiceManFlow({ target }) {
      if (target.value == '1') {
        this.autoDisable = false;
        // this.$refs.crud.setFormFields({ orderHandler: undefined });
      } else if (target.value == '2') {
        this.autoDisable = true;
        await this.selectFlowType('2');
      } else if (target.value == '3') {
        this.autoDisable = false;
      }
    },
    async addOrderFn(params) {
      let [result, err] = await addAPI(params);
      console.log('---------err', err);
      return [result, err];
    },
    //获取工单模板，触发事件在选择工单分类之后
    async getMod() {
      let [mock] = await listAPI(this.nowCategoryID);
      this.exList = mock.data; //获取模板
    },
    //紧急弹窗确认
    modalOK() {
      this.prevFlow = this.nowFlow; //获取备份flowRadio，用于紧急时弹窗提示
      console.log('上一次的已保存', this.prevFlow);
      this.modalSeen = false;
    },
    modalCancel() {
      this.$refs.crud.setFormFields({ urgency: this.prevFlow });
      this.modalSeen = false;
    },
    // 获取当日历史通话未创建为工单的记录
    async getTodayCall() {
      if (this.showHistoryCall) {
        this.showHistoryCall = false;
      } else {
        const [res] = await getHistoryCall({ pageFlag: 0 });
        res.data.forEach((item) => {
          const res = this.historyCallList.find((call) => {
            return call.mainUniqueId === item.mainUniqueId;
          });
          if (!res) {
            if (item.customerNumber !== undefined) {
              this.historyCallList.push(item);
            }
          }
        });
        if (res.data) {
          this.showHistoryCall = true;
        } else {
          message.warn('暂无通话记录~');
        }
        this.filterCallList = this.historyCallList;
      }
    },
    searchCall(value) {
      if (value) {
        this.filterCallList = this.historyCallList.filter((item) => {
          return item.customerNumber.includes(value);
        });
      } else {
        this.filterCallList = this.historyCallList;
      }
      console.log('搜索结果', value, this.filterCallList);
    },
    // 选择通话记录
    selectCall(value) {
      const [res] = this.historyCallList.filter((item) => {
        return item.mainUniqueId === value;
      });
      this.$refs.crud.setFormFields({ phoneNumber: res.customerNumber });
      // 添加通话记录至tablists
      this.nowCallId = res.mainUniqueId;
    },
    // 单选字典获取
    async getModSelect(key) {
      let result = await defaultDict(key);
      this.selectOne = result;
    },
    async getModSecSelect(key) {
      let result = await defaultDict(key);
      return result;
    },
    newGetDict(key) {
      let result = [];
      new Promise((resolve) => {
        const res = this.getModSecSelect(key);
        resolve(res);
      }).then((res) => {
        result = res;
      });
      return result;
    },
    // 选择预设回复模板
    handleSelect(value) {
      this.contentInput += value.content;
      this.$refs.crud.setFormFields({ orderContent: this.contentInput });
    },
    giveParam(value) {
      this.contentInput = value;
    },

    // 检查是否为销售工单
    checkSaleOrder(categoryId) {
      const findCategory = (categories, id) => {
        for (const category of categories) {
          if (category.categoryId === id) {
            return category;
          }
          if (category.children && category.children.length > 0) {
            const found = findCategory(category.children, id);
            if (found) return found;
          }
        }
        return null;
      };

      const selectedCategory = findCategory(this.workType, categoryId);
      this.isSaleOrder = selectedCategory && selectedCategory.saleOrderFlag === '1';

      // 如果是销售工单，初始化客户选项
      if (this.isSaleOrder) {
        this.loadCustomerOptions();
      }
    },

    // 加载客户选项
    async loadCustomerOptions(searchKeyword = '') {
      this.customerLoading = true;
      try {
        const [result] = await customerListAPI({
          pageSize: 9999,
          pageNum: 1,
          customerName: searchKeyword,
          phoneNumber: searchKeyword,
        });

        if (result && result.data) {
          this.customerOptions = result.data.map((customer) => ({
            label: `${customer.customerName}—${customer.phoneNumber}`,
            value: customer.customerId,
            customerName: customer.customerName,
            phoneNumber: customer.phoneNumber,
          }));
        }
      } catch (error) {
        console.error('加载客户列表失败:', error);
        this.$message.error('加载客户列表失败');
      } finally {
        this.customerLoading = false;
      }
    },

    // 客户搜索
    handleCustomerSearch(value) {
      if (value) {
        this.loadCustomerOptions(value);
      }
    },
  },
};
