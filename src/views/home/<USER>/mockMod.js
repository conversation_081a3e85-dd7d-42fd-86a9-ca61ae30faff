// 工单模板
export const inputMod = {
  code: 200,
  message: null,
  data: [
    {
      templateId: 113,
      templateName: '打篮球类模版',
      fields: [
        {
          templateFieldId: 1,
          fieldName: '爱好',
          fieldType: '1',
          fieldDataKey: 'sex', //字典
          emptyFlag: 0, //是否可空
          fieldDesc: '请输入你的爱好', //提示文本
          defaultValue: '打篮球',
        },
        {
          templateFieldId: '3',
          fieldName: '什么时候打的',
          fieldType: '2',
          fieldDataKey: 'sex', //字典
          emptyFlag: 0, //是否可空
          fieldDesc: '请输入你的爱好', //提示文本
          defaultValue: '2023-10-04 00:00:00',
        },
        {
          templateFieldId: '63',
          fieldName: '喜欢队里的谁打',
          fieldType: '3',
          fieldDataKey: 'sex', //字典
          emptyFlag: 0, //是否可空
          fieldDesc: '请输入你的爱好', //提示文本
          defaultValue: '打篮球',
        },
        {
          templateFieldId: '132',
          fieldName: '富文本',
          fieldType: '4',
          fieldDataKey: 'sex', //字典
          emptyFlag: 0, //是否可空
          fieldDesc: '请输入你的爱好', //提示文本
          defaultValue: '打篮球',
        },
      ],
    },
    {
      templateId: 4399,
      templateName: '踢足球类模版',
      fields: [
        {
          templateFieldId: '63',
          fieldName: '喜欢队里的谁打',
          fieldType: '3',
          fieldDataKey: 'sex', //字典
          emptyFlag: 0, //是否可空
          fieldDesc: '请输入你的爱好', //提示文本
          defaultValue: '打篮球',
        },
        {
          templateFieldId: '132',
          fieldName: '富文本',
          fieldType: '4',
          fieldDataKey: 'sex', //字典
          emptyFlag: 0, //是否可空
          fieldDesc: '请输入你的爱好', //提示文本
          defaultValue: '这是富文本的默认值',
        },
      ],
    },
  ],
  count: 3,
};

// 工单分类（级联）
export const workType = {
  code: '10000',
  message: null,
  data: [
    {
      categoryId: 1132, //工单分类id
      categoryName: '一级分类', //工单分类名称
      handleGroup: '113210', //绑定受理组id
      handleGroupName: '赌神2受理组',
      handleUser: '1131', //绑定受理人id
      handleUserName: '受理人汪峰',
      children: [
        {
          categoryId: 2213431223, //工单分类id
          categoryName: '二级-爆破性投诉类', //工单分类名称
          handleGroup: '332133', //绑定受理组id
          handleGroupName: '张三',
          handleUser: '444', //绑定受理人id
          handleUserName: '李四',
          parentId: 1145312314,
          children: [{}],
        },
        {
          categoryId: 22232333113, //工单分类id
          categoryName: '二级-炸弹投诉类', //工单分类名称
          handleGroup: '333', //绑定受理组id
          handleGroupName: '张三',
          handleUser: '444', //绑定受理人id
          handleUserName: '李四',
          parentId: 1142514,
          children: [{}],
        },
      ],
    },

    {
      categoryId: 111312332, //工单分类id
      categoryName: '一级分类-x2', //工单分类名称
      handleGroup: '113210', //绑定受理组id
      handleGroupName: '赌神2受理组',
      handleUser: '1131', //绑定受理人id
      handleUserName: '受理人汪峰',
      children: [
        {
          categoryId: 2213325431223, //工单分类id
          categoryName: '二级-爆破性投诉类', //工单分类名称
          handleGroup: '332133', //绑定受理组id
          handleGroupName: '张三',
          handleUser: '444', //绑定受理人id
          handleUserName: '李四',
          parentId: 1145312312314,
          children: [{}],
        },
        {
          categoryId: 22232333123113, //工单分类id
          categoryName: '二级-炸弹投诉类', //工单分类名称
          handleGroup: '333', //绑定受理组id
          handleGroupName: '张三',
          handleUser: '', //绑定受理人id
          handleUserName: '李四',
          parentId: 1142512314,
          children: [{}],
        },
      ],
    },
  ],
  count: 2,
};

// 根据工单分类的流转方式
export const FlowType = {
  code: '10000',
  message: null,
  data: {
    autoFlag: '0',
    handleGroupName: '',
    handleUserName: '',
    handleGroup: '',
    handleUser: '',
    flowType: '2', //流转方式(1- 人工流转 2-自动分配 3-工单池流转)
  },
};
