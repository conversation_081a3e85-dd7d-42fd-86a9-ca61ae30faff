<template>
  <div>
    <WorkBench
      :benchTabs="workbenchTabs"
      :followInMenu="true"
      :handleInMenu="false"
      :queryType="nowQueryType"
      :filterType="filterType"
      :editable="true"
    />
  </div>
</template>

<script setup>
import WorkBench from './components/WorkBench.vue';
import { ref } from 'vue';
import { workbenchTabs } from './constant';
import { useRoute } from 'vue-router/composables';
const toRoute = useRoute();
let nowQueryType = ref(toRoute.query.handleStatu);
let filterType = ref();
// 处理来自总览页的跳转标签
if (filterType === 'overtime' || filterType === 'urging') {
  nowQueryType.value = '6';
} else {
  nowQueryType.value = '1';
}
</script>
