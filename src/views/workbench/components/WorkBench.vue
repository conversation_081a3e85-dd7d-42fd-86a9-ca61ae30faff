<template>
  <div>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :modalConfig="modalConfig"
      @loadData="loadData"
      @handleSubmit="handleSubmit"
      @handleCreate="rowAdd"
    >
      <template #moreTimeSub>
        <a-row>
          <a-col :span="4">
            <a-space>
              <a-button @click="getRange('week')">上周</a-button>
              <a-button @click="getRange('month')">上月</a-button>
            </a-space>
          </a-col>
          <a-col :span="13" :offset="7">
            <a-range-picker :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model="params.createTime" />
          </a-col>
        </a-row>
      </template>
      <!-- tab切换 -->
      <template #defaultHeader>
        <div>
          <div style="margin-bottom: 20px">
            <a-button v-hasPermi="['system:workbench:exportOrderList']" @click="exportAll" class="switchBtn"
              >导出</a-button
            >
            <AddOrder v-hasPermi="['system:workbench:createOrder']" ref="addOrder" @refreshList="loadData" />
          </div>
          <div>
            <a-button
              v-for="item in props.benchTabs"
              :key="item.orderId"
              @click="switchTable(item.queryType)"
              :class="['switchBtn', isActive === item.queryType ? 'isActive' : '']"
            >
              {{ item.label }}
            </a-button>
          </div>
        </div>
      </template>
      <template #urgency="{ row }">
        <span :style="{ color: urgencyColor(row.urgency) }">
          {{ row.urgency }}
        </span>
      </template>
      <template #categoryName="{ row }">
        <span>
          {{ row.categoryName }}
          <div class="tipTag">
            <a-tag color="red" v-if="row.remindFlag === '1'">催</a-tag>
            <a-tag color="orange" v-if="row.expireFlag === '1'">超</a-tag>
          </div>
        </span>
      </template>
      <template #orderStatus="{ row }">
        <a-tag :color="transformStatu(row.orderStatus)" class="orderStatu">
          {{ row.orderStatus }}
        </a-tag>
      </template>
    </BuseCrud>
  </div>
</template>

<script setup>
import AddOrder from '@/views/home/<USER>';
import moment from 'moment';
import { computed, onMounted, ref, h, onActivated } from 'vue';
import {
  editOrder,
  exportOrderList,
  getOrderDetail,
  getOrderList,
  orderFollow,
  orderTake,
} from '@/api/system/workorder';
import StatuNode from '@/components/TreeNode/StatuNode.vue';
import { defaultMsg, setCustomTree, downLoadXlsx, defaultDict, isHasPermi, urgencyColor } from '@/utils/system/common';
import store from '@/store';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router/composables';
import { useStore } from '@/utils/system/hooks/vueApi';
let loading = ref(true);
let crud = ref();
let nowQueryType = ref();
let isActive = ref();
let userList = ref();
let orderSort = ref();
let orderStatus = ref();
let addOrder = ref();
const goRoute = useRouter();
const props = defineProps({
  benchTabs: Array, // 切换tab栏
  followInMenu: {
    type: Boolean, // 操作中的关注
    default: false,
  },
  handleInMenu: {
    type: Boolean, // 操作中的接单
    default: false,
  },
  queryType: String,
  filterType: {
    type: String, // 仅为客服工作台过滤类型
    default: '',
  },
  editable: {
    type: Boolean,
    default: false,
  },
});
// 参数定义
const params = ref({
  orderId: undefined,
  createByName: undefined,
  stationName: undefined,
  orderStatus: undefined,
  phoneNumber: undefined,
  createTime: undefined,
  categoryId: undefined,
  queryType: undefined,
  flowTypeCode: undefined,
  outboundCallTime: undefined,
  customerName: undefined,
  faceInterviewFlag: undefined,
});
// 表格列字段
const tableColumn = ref([
  {
    field: 'orderId',
    title: '工单号',
    width: 168,
  },
  {
    field: 'createByName',
    title: '提交人',
  },
  {
    field: 'urgency',
    title: '优先级',
    slots: { default: 'urgency' },
  },
  {
    field: 'categoryName',
    title: '分类',
    slots: { default: 'categoryName' },
    maxWidth: 208,
  },
  {
    field: 'orderStatus',
    title: '工单状态',
    slots: { default: 'orderStatus' },
    width: 150,
  },
  {
    field: 'orderSource',
    title: '工单来源',
  },
  {
    field: 'flowType',
    title: '流转方式',
  },
  {
    field: 'customerName',
    title: '客户姓名',
  },
  {
    field: 'phoneNumber',
    title: '手机号',
    width: 128,
  },
  {
    field: 'createTime',
    title: '提交时间',
  },
  {
    field: 'stationName',
    title: '站点名称',
    visible: store.state.base.merchant.merchantId == '40002',

    // show: this.merchant.merchantId == '40002',
  },
  {
    field: 'handleUserName',
    title: '当前处理人',
    width: 108,
  },
  {
    field: 'faceInterviewFlagName',
    title: '是否需要面访',
  },
  {
    field: 'outboundCallTime',
    title: '预约外呼时间',
  },
]);
// 筛选器配置
const filterOptions = ref({
  gridCol: { span: 8 },
  formCol: {
    labelCol: 6,
    wrapperCol: 16,
  },
  config: [
    {
      field: 'orderId',
      title: '工单号',
    },
    {
      field: 'orderStatus',
      title: '工单状态',
      element: 'a-select',
      props: {
        options: orderStatus,
        mode: 'multiple',
        getPopupContainer: (triggerNode) => triggerNode.parentNode,
      },
    },
    {
      field: 'createTime',
      title: '提交时间',
      element: 'slot',
      slotName: 'moreTimeSub',
      itemProps: {
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
      },
    },
    {
      field: 'categoryId',
      title: '工单分类',
      element: 'a-tree-select',
      props: {
        treeData: orderSort,
        multiple: true,
        replaceFields: {
          title: 'categoryName',
          value: 'categoryId',
        },
        dropdownStyle: { maxHeight: '400px', overflow: 'auto' },
        getPopupContainer: (triggerNode) => triggerNode.parentNode,
      },
      on: {
        focus: () => {
          store.dispatch('base/GetOrderSort').then((res) => {
            orderSort.value = res;
          });
        },
      },
    },
    {
      field: 'customerName',
      title: '客户姓名',
    },
    {
      field: 'phoneNumber',
      title: '手机号',
      rules: [
        {
          message: '请输入手机号！',
          pattern: /^[0-9]*$/,
        },
      ],
    },
    {
      field: 'createByName',
      title: '提交人',
      element: 'a-tree-select',
      props: {
        treeData: userList,
        mode: 'multiple',
        treeCheckable: true,
        showSearch: true,
        dropdownStyle: { maxHeight: '400px', overflow: 'auto' },
        getPopupContainer: (triggerNode) => triggerNode.parentNode,
      },
      scopedSlots: {
        myTitle: (item) => h(StatuNode, { props: { newNodes: item } }),
      },
      on: {
        focus: () => {
          store.dispatch('base/GetUserList', { nickName: '' }).then((res) => {
            userList.value = setCustomTree(res);
          });
        },
      },
    },
    {
      field: 'flowTypeCode',
      title: '流转方式',
      element: 'a-select',
      props: {
        options: [
          { value: '1', label: '人工流转' },
          { value: '2', label: '自动分配' },
          { value: '3', label: '工单池流转' },
        ],
      },
    },
    {
      field: 'stationName',
      title: '站点名称',
      show: store.state.base.merchant.merchantId == '40002',
    },
    {
      field: 'faceInterviewFlag',
      title: '是否需要面访',
      element: 'a-select',
      props: {
        options: [
          // (0: 否 1: 是)
          { value: '1', label: '是' },
          { value: '0', label: '否' },
        ],
      },
    },
    {
      field: 'outboundCallTime',
      title: '预约外呼时间',
      element: 'a-range-picker',
      props: {
        valueFormat: 'YYYY-MM-DD',
      },
    },
  ],
  params: params,
});

// 操作配置
const modalConfig = computed(() => {
  return {
    menu: true,
    menuWidth: 128,
    addBtn: false,
    delBtn: false,
    viewBtn: false,
    editBtn: false,
    customOperationTypes: [
      {
        title: '详情',
        typeName: 'detail',
        event: (row) => {
          return new Promise((resolve) => {
            goRoute.push({
              path: '/workorderdetail',
              query: {
                orderId: row.orderId,
              },
            });
            resolve();
          });
        },
        // condition: () => {
        //   return isHasPermi(['system:workorder:detail']);
        // },
      },
      {
        title: '关注',
        typeName: 'follow',
        // show: isHasPermi(['system:workbench:orderFollow']),
        event: (row) => {
          return new Promise((resolve) => {
            updateFollow(row);
            resolve();
          });
        },
        condition: (row) => {
          if (props.followInMenu) {
            return row.followFlag === '0';
          } else {
            return false;
          }
        },
      },
      {
        title: '取消关注',
        typeName: 'follow',
        // show: isHasPermi(['system:workbench:orderFollow']),
        event: (row) => {
          return new Promise((resolve) => {
            updateFollow(row);
            resolve();
          });
        },
        condition: (row) => {
          if (props.followInMenu) {
            return row.followFlag === '1';
          } else {
            return false;
          }
        },
      },
      {
        title: '接单',
        typeName: 'handle',
        // show: isHasPermi(['system:workorder:orderHandle']),
        event: (row) => {
          return new Promise((resolve) => {
            takeOrder(row);
            resolve();
          });
        },
        condition: () => {
          return props.handleInMenu;
        },
      },
      {
        title: '编辑',
        typeName: 'edit',
        // show: isHasPermi(['system:workbench:getEditOrder']),
        event: async (row) => {
          const [res] = await editOrder(row.orderId, row.mainUniqueId);
          return new Promise((resolve) => {
            addOrder.value.rowAdd(res.data, true);
            resolve();
          });
        },
        condition: () => {
          if (props.editable && nowQueryType.value === '2') {
            return true;
          }
          return false;
        },
      },
    ],
  };
});
let tableData = ref([]);
let tablePage = ref({ total: 0, currentPage: 1, pageSize: 10 });

// 关注/取关
async function updateFollow(row) {
  const params = {
    orderId: row.orderId,
    orderFollowId: row.orderFollowId,
  };
  const [result] = await orderFollow(params);
  defaultMsg(result, row.followFlag === '1' ? '取关' : '关注');
  loadData();
}
// 根据queryType切换table
function switchTable(queryType) {
  isActive.value = queryType;
  nowQueryType.value = queryType;
  tablePage.value.currentPage = 1;
  // 保存当前tab
  store.dispatch('base/tabIndexGet', queryType);
  loadData('', queryType); // 根据table类型加载数据
}
// 接单
async function takeOrder(row) {
  const [res] = await getOrderDetail({ orderId: row.orderId });
  const { data } = res;
  if (data.detail.isTake) {
    const [result] = await orderTake({ orderId: data.detail.orderId });
    defaultMsg(result, '接单');
    loadData();
  } else {
    message.error('很抱歉，您无权接单～');
  }
}
// 工单状态转换颜色
function transformStatu(orderStatu) {
  if (orderStatus.value && orderStatus.value.length > 0) {
    const res = orderStatus.value.filter((item) => {
      return item.label === orderStatu;
    });
    const statu = res[0].value;
    let color = '';
    if (statu === '3' || statu === '9') {
      color = 'red';
    } else if (statu === '5' || statu === '6' || statu === '7') {
      color = 'green';
    } else {
      color = 'blue';
    }
    return color;
  }
}
// 数据加载，filterType仅为首页任务提醒做数据过滤
async function loadData(filterType, search) {
  const newParam = transformParams(); // 防止分页丢失参数
  tablePage.value.currentPage = search ? 1 : tablePage.value.currentPage;
  const finalParam = {
    ...newParam,
    queryType: nowQueryType.value,
    pageNum: tablePage.value.currentPage,
    pageSize: tablePage.value.pageSize,
  };

  const [result] = await getOrderList(finalParam);
  switch (filterType) {
    case 'overtime':
      result.data = result.data.filter((item) => {
        return item.expireFlag === 1;
      });
      break;
    case 'urging':
      result.data = result.data.filter((item) => {
        return item.remindFlag === 1;
      });
      break;
  }
  isActive.value = nowQueryType.value;
  tableData.value = result.data;
  tablePage.value.total = result.count;
  loading.value = false;
}
// 上周、上月按钮
function getRange(rangeType) {
  params.value.createTime = [
    moment(
      moment()
        .subtract(1, rangeType)
        .startOf(rangeType)
        .add(rangeType === 'week' ? 1 : 0, 'day')
        .format('YYYY-MM-DD')
    ),
    moment(
      moment()
        .subtract(1, rangeType)
        .endOf(rangeType)
        .add(rangeType === 'week' ? 1 : 0, 'day')
        .format('YYYY-MM-DD')
    ),
  ];
}
function transformParams() {
  let finalParams = {
    orderId: params.value.orderId,
    orderStatus: params.value.orderStatus,
    categoryId: params.value.categoryId,
    phoneNumber: params.value.phoneNumber,
    createId: params.value.createByName,
    stationName: params.value.stationName,
    flowTypeCode: params.value.flowTypeCode,
    customerName: params.value.customerName,
    faceInterviewFlag: params.value.faceInterviewFlag,
  };
  if (params.value.createTime) {
    finalParams.startDate = moment(params.value.createTime[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss');
    finalParams.endDate = moment(params.value.createTime[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss');
  }
  if (params.value.outboundCallTime && params.value.outboundCallTime.length === 2) {
    finalParams.startOutboundCallTime = moment(params.value.outboundCallTime[0])
      .startOf('day')
      .format('YYYY-MM-DD HH:mm:ss');
    finalParams.endOutboundCallTime = moment(params.value.outboundCallTime[1])
      .endOf('day')
      .format('YYYY-MM-DD HH:mm:ss');
  }
  return finalParams;
}
// 检索按钮
function handleSubmit() {
  loadData('', true);
}
// 导出操作
async function exportAll() {
  if (tablePage.value.total > 0) {
    const finalParams = transformParams();
    finalParams.queryType = nowQueryType.value;
    delete finalParams.pageNum;
    delete finalParams.pageSize;
    const [result] = await exportOrderList(finalParams); // 获取表格全部数据
    downLoadXlsx(result, '工单列表.xlsx');
  } else {
    message.warn('导出表格暂无数据哦～');
  }
}
function rowAdd() {
  crud.value.switchModalView(true, 'ADD');
}
onMounted(() => {
  // 路由判断是否根据vuex加载tab
  const currentTab = store.state.base.tabIndex;
  const nowPage = goRoute.currentRoute.fullPath;
  if (currentTab && nowPage === '/workorderbench') {
    switchTable(currentTab);
  } else {
    nowQueryType.value = props.queryType;
    loadData(props.filterType);
  }
  defaultDict('order_status').then((res) => {
    orderStatus.value = res;
  });
});
</script>

<style scoped>
.tipTag {
  position: absolute;
  display: inline-block;
  top: 0px;
  left: -18px;
  transform: scale(0.8);
}
.exportBtn {
  position: absolute;
  transform: translateY(-88px);
}
.switchBtn {
  margin-right: 16px;
}
.isActive {
  box-shadow: 0px 4px 24px #c3ccd0;
  color: #4580ff;
  border-color: #4580ff;
}
.orderStatu {
  line-height: 22px;
}
</style>
