<template>
  <a-layout class="layout">
    <FNCommonTopNav
      class="layout-header"
      v-bind="topNavOptions"
      @logout="logout"
      @handleMessageHoverChange="handleMessageHoverChange"
    />
    <a-layout>
      <SideMenu
        class="fixed-side"
        :style="`width: ${sideWidth}`"
        :menuData="menuData"
        :collapsed="collapsed"
        :collapsible="true"
        :loading="false"
        @toggleCollapse="toggleCollapse"
      />
      <a-layout-content id="layoutContent" class="beauty-scroll" style="overflow: auto">
        <div class="layout-content">
          <!-- key 用于解决vue-router replace同一页面不刷新的问题 -->
          <keep-alive>
            <router-view v-if="$route.meta.keepAlive" :key="$route.fullPath"></router-view>
          </keep-alive>
          <router-view v-if="!$route.meta.keepAlive" :key="$route.fullPath"></router-view>
        </div>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script>
import FNCommonTopNav from './navHeader/HeaderContent.vue';
import SideMenu from './menu/SideMenu';
import { redirectLogin } from '@/router/guards';
import { mapState, mapMutations } from 'vuex';
import { mailFormworkTips } from '@/api/system/mailFormworkTe';
export default {
  name: 'Layout',
  components: { FNCommonTopNav, SideMenu },
  dicts: ['my_authority_app_category', 'platform_logo_url'],
  data() {
    return {
      sideMenuData: [],
      newsTipList: [], //消息中心列表
      logoUrl: '',
    };
  },
  computed: {
    ...mapState('setting', ['collapsed', 'menuData']),
    ...mapState({
      username: (state) => state.base.username,
      merchantName: (state) => state.base?.merchant?.merchantName || '',
      merchantLogo: (state) => state.base?.merchant?.icon || undefined,
      appList: (state) => state.base?.sysApps || [],
    }),
    sideWidth() {
      return this.collapsed ? 80 : 208;
    },
    topNavOptions() {
      return {
        logo: this.logoUrl || process.env.VUE_APP_LOGO_PLATFORM || '',
        navMenuConfig: {
          appList: this.appList,
          appCategoryOptions: this.dict.type.my_authority_app_category,
          newsTipList: this.newsTipList, //消息提示列表
        },
        // 默认展示高亮的选中的菜单
        currentNavMenu: ['TEST_APP1'],
        userInfo: {
          avatar: this.merchantLogo,
          username: this.username,
          merchantName: this.merchantName,
        },
      };
    },
  },
  watch: {
    menuData: {
      immediate: true,
      deep: true,
      handler() {
        if (!this.menuData) {
          return;
        }
        this.sideMenuData = this.menuData;
      },
    },
  },
  methods: {
    ...mapMutations('setting', ['setSideMenuCollapsed']),
    // 字典加载完成
    onDictReady() {
      // 获取系统字典：应用类型
      this.logoUrl = this.dict.type.platform_logo_url?.[0]?.value || '';
    },
    // 刷新信息数据
    async handleMessageHoverChange(visible) {
      if (visible) {
        const [result, error] = await mailFormworkTips();
        if (error) return;
        if (result) {
          const { data } = result;
          this.newsTipList = data;
        }
      }
    },
    toggleCollapse() {
      this.setSideMenuCollapsed(!this.collapsed);
    },
    logout() {
      this.$store.dispatch('base/LogOut').finally(() => {
        this.$router.push(redirectLogin());
      });
    },
  },
};
</script>

<style scoped lang="less">
.layout {
  display: flex;
  height: 100vh;
  overflow: hidden;
  .layout-header {
    z-index: 1;
    flex: 0 0 auto;
  }
  .layout-content {
    position: relative;
    min-width: 1240px; // 1280
    max-width: 2557px; // 2557 // 1920
    margin: 0 auto;
    padding-top: 32px;
  }
  /deep/ .ant-layout {
    // padding-top: 60px;
    flex: 1;
    background-color: #f4f4f4;
  }
  /deep/ .ant-layout-header {
    overflow-x: auto;
    overflow-y: hidden;
    height: 60px;
    line-height: 60px;
    padding: 0 40px;
    background-image: linear-gradient(135deg, #ffffff 0%, #f9fcff 50%, #53a7ed 100%, #03c1fe 100%);
    // position: absolute;
    width: 100%;
    // z-index: 100;
    box-shadow: 0 10px 10px -10px rgba(174, 183, 207, 0.25);
  }
  /deep/ .ant-layout.ant-layout-has-sider > .ant-layout-content {
    overflow-x: auto;
  }
}
</style>
