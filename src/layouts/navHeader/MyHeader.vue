<template>
  <div>
    <div class="myHeader">
      <a-row
        class="menu"
        type="flex"
        justify="end"
      >
        <!-- <a-col :span="6" class="headerItem">
        <a-select style="width: 80%" placeholder="选择业务线">
          <a-select-option v-for="item in workLine" :key="item.value">{{ item.label }}</a-select-option>
        </a-select>
      </a-col> -->
        <a-col
          :span="12"
          class="headerItem"
          v-hasPermi="['system:workbench:queueData']"
        >
          <div class="callAns">
            <a-space>
              <span>今日呼叫量：{{ callNum }}</span>
              <span>今日接听量：{{ answerNum }}</span>
            </a-space>
          </div>
        </a-col>
        <a-col
          :span="6"
          class="headerItem"
          v-hasPermi="['system:workbench:queueData']"
        >
          <div
            class="navList"
            id="navList"
          >
            <div
              v-for="item in queueData"
              :key="item.id"
              class="listItem"
            >
              <span class="itemTitle">{{ item.queueName }}</span>
              <a-icon
                type="smile"
                theme="twoTone"
                :two-tone-color="item.queueCount > 0 ? 'red' : '#52c41a'"
              />
              <span>{{ item.queueCount }}</span>
            </div>
          </div>
        </a-col>
        <a-col
          :span="6"
          class="headerItem"
          v-hasPermi="['system:user:updateUserStatu']"
        >
          <a-select
            style="width: 80%"
            v-model="nowStatu"
            @select="switchStatu"
          >
            <a-select-option
              v-for="item in userStatus"
              :key="item.value"
            >
              <a-space>
                <a-icon
                  v-if="item.value === '1'"
                  type="smile"
                  theme="twoTone"
                  twoToneColor="#52c41a"
                />
                <a-icon
                  v-else-if="item.value === '2'"
                  type="fire"
                  theme="twoTone"
                  twoToneColor="#eb2f96"
                />
                <a-icon
                  v-else
                  type="frown"
                  theme="twoTone"
                />
                {{ item.label }}
              </a-space>
            </a-select-option>
          </a-select>
        </a-col>
      </a-row>
    </div>
    <!-- <ClinkToolbar class="clinkTool" /> -->
  </div>
</template>
<script setup>
import { orderQueue } from '@/api/system/workorder';
import { switchUserStatu } from '@/api/system/settings';
import { defaultDict, defaultMsg, isHasPermi } from '@/utils/system/common';
import { onBeforeUnmount, onMounted, ref } from 'vue';
import ClinkToolbar from '@/components/clinkToolbar/clinkToolbar.vue';
let queueData = ref([]);
let callNum = ref();
let answerNum = ref();
let userStatus = ref();
let nowStatu = ref('1');
let scrollTimer; // 滚动定时器
let scrollDirection = 'down'; // 滚动方向
let queueTimer; // 接口定时器
async function getAllLines () {
  try {
    const [result] = await orderQueue();
    const { data } = result;
    queueData.value = data.awaitCountResponseList;
    callNum.value = data.ibTotalCount ? data.ibTotalCount : 0;
    answerNum.value = data.ibClientAnsweredCount ? data.ibClientAnsweredCount : 0;
  } catch {
    if (scrollTimer) {
      clearInterval(scrollTimer);
      scrollTimer = null;
    }
    if (queueTimer) {
      clearInterval(queueTimer);
      queueTimer = null;
    }
  }
}
async function switchStatu (data) {
  await switchUserStatu(data);
}
function autoScroll () {
  const scrollHeight = document.getElementById('navList').scrollHeight;
  const clientHeight = document.getElementById('navList').clientHeight;
  const scroll = scrollHeight - clientHeight;
  if (scroll === 0) {
    return;
  }
  // 触发滚动方法
  scrollFun();
}
function scrollFun () {
  if (scrollTimer) {
    clearInterval(scrollTimer);
    scrollTimer = null;
  }
  if (document.getElementById('navList').scrollHeight) {
    scrollTimer = setInterval(() => {
      const scrollHeight = document.getElementById('navList').scrollHeight;
      const clientHeight = document.getElementById('navList').clientHeight;
      const scroll = scrollHeight - clientHeight;
      const scrollTop = document.getElementById('navList').scrollTop; // 获取当前滚动条距离顶部高度
      if (scrollDirection === 'down') {
        const temp = scrollTop + 1;
        document.getElementById('navList').scrollTop = temp;
        // 距离顶部高度大于等于滚动长度
        if (scroll <= temp) {
          // 滚动到底部 停止定时器
          clearInterval(scrollTimer);
          scrollTimer = null;
          scrollDirection = 'up';
          setTimeout(() => {
            scrollFun();
          }, 1000);
        }
      } else if (scrollDirection === 'up') {
        const temp = scrollTop - 2;
        document.getElementById('navList').scrollTop = temp;
        if (temp <= 0) {
          clearInterval(scrollTimer);
          scrollTimer = null;
          scrollDirection = 'down';
          setTimeout(() => {
            scrollFun();
          }, 1000);
        }
      }
    }, 150);
  }
}
// async function getNowStatu() {
//   const [res] = await userNowStatu();
//   const { data } = res;
//   nowStatu.value = data.userStatus ? data.userStatus : '1';
// }

onMounted(() => {
  defaultDict('user_status').then((res) => {
    userStatus.value = res;
    switchStatu('1');
  });
  if (isHasPermi(['system:workbench:queueData'])) {
    getAllLines();
    autoScroll();
    queueTimer = setInterval(() => {
      setTimeout(() => {
        getAllLines();
      }, 0);
    }, 3000);
  }
});
onBeforeUnmount(() => {
  if (scrollTimer) {
    clearInterval(scrollTimer);
    scrollTimer = null;
  }
  if (queueTimer) {
    clearInterval(queueTimer);
    queueTimer = null;
  }
});
</script>
<style scoped lang="less">
.myHeader {
  min-width: 500px;
  max-width: 957px;
  display: flex;
  flex: 2;
  .menu {
    display: flex;
    flex: 2;
    line-height: 60px;
    .headerItem {
      text-align: center;
      line-height: 60px;
      .callAns {
        font-size: small;
        height: 60px;
      }
      .navList {
        height: 48px;
        width: 100%;
        position: absolute;
        overflow: scroll;
        // border: 1px solid #d9d9d9;
        margin: 4px 16px 4px 0;
        padding: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        .listItem {
          display: flex;
          align-items: center;
          line-height: 12px;
          text-align: left;
          white-space: nowrap;
          margin: 4px;
          flex: 1;
          span {
            margin-left: 8px;
          }
          .itemTitle {
            width: 48px;
            display: inline-block;
          }
        }
      }
      .navList::-webkit-scrollbar {
        display: none;
      }
      /deep/ .ant-select-selection--single {
        height: 24px;
      }
      /deep/ .ant-select-selection__rendered {
        line-height: 24px;
      }
    }
  }
}
.clinkTool {
  position: absolute;

  left: 50%;
  transform: translateX(-50%);
}
</style>
