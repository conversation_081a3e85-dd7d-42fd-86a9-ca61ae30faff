<template>
  <div class="fn-header-nav-wrapper">
    <!-- 左侧logo -->
    <!-- <a
      v-if="logoHrefPath"
      class="fn-header-logo"
      :href="logoHrefPath"
      :target="linkOpenType"
    >
      <slot v-if="$slots.main - logo" name="main-logo"></slot>
      <img v-else :src="logo" />
    </a> -->
    <div class="fn-header-logo">
      <!-- <slot
        v-if="$slots.main - logo"
        name="main-logo"
      ></slot>
      <img
        v-else
        :src="logo"
      /> -->
      <img src="../../assets/images/login-t.png" />
    </div>
    <!-- 中间顶部一级导航栏 -->
    <div class="fn-header-middle-nav">
      <a-menu
        v-if="navMenuListShow && navMenuListShow.length > 0"
        mode="horizontal"
        :selectedKeys="middleOptions.currentNavMenu || currentNavMenu"
      >
        <template v-for="item in navMenuListShow">
          <template v-if="!item.subMenu || item.subMenu.length === 0">
            <a-menu-item :key="item.key">
              <a :href="item.path" :target="item.openType === 'NEW_TAB' ? '_blank' : '_self'">
                {{ item.title }}
              </a>
            </a-menu-item>
          </template>
          <template v-else>
            <a-sub-menu :key="item.key">
              <template #title>
                <div class="nav-menu-arrow-box">
                  {{ item.title }}
                  <a-icon type="caret-down" class="arrow-icon" />
                </div>
              </template>
              <a-menu-item v-for="subItem in item.subMenu" :key="subItem.key">
                <a :href="subItem.path" :target="item.openType === 'NEW_TAB' ? '_blank' : '_self'">
                  {{ subItem.title }}
                </a>
              </a-menu-item>
            </a-sub-menu>
          </template>
        </template>
      </a-menu>
      <a-divider
        v-if="hasMenuDivider"
        type="vertical"
        style="background-color: #8a94a4; height: 20px; margin: 0 10px"
      />
      <a-menu v-if="extraMenuOptions && extraMenuOptions.length > 0" mode="horizontal">
        <a-menu-item v-for="(item, index) in extraMenuOptions" :key="index">
          <a v-if="item.type === 'link'" :href="item.linkUrl || item.url" :target="linkOpenType">
            {{ item.title }}
          </a>
          <a-popover v-if="item.type === 'image'" placement="bottom">
            <template #content>
              <img width="158" :src="item.url" />
            </template>
            <a-icon v-if="item.icon" :type="item.icon" />
            <span>{{ item.title }}</span>
          </a-popover>
        </a-menu-item>
      </a-menu>
      <slot name="menu-extra"></slot>
    </div>
    <!-- 右侧登录状态展示+扩展插槽 -->
    <slot name="right-extra">
      <MyHeader v-if="myHeader" />
      <ClinkToolbar class="clinkTool" @onChangeMerchant="onChangeMerchant" v-show="$route.path == '/callbench'" />
    </slot>
    <HeaderAvatar
      class="fn-header-avatar"
      v-bind="userInfo"
      @logout="logout"
      :newsTipList="navMenuConfig.newsTipList"
      :hasMessageAppInfo="hasMessageAppInfo"
      :hasSystemAppInfo="hasSystemAppInfo"
      @handleMessageHoverChange="handleMessageHoverChange"
      @onChangeMerchant="onChangeMerchant"
    />
  </div>
</template>

<script>
import HeaderAvatar from './HeaderAvatar.vue';
import MyHeader from './MyHeader.vue';
import ClinkToolbar from '@/components/clinkToolbar/clinkToolbar.vue';

export default {
  name: 'NavHeader',
  components: { HeaderAvatar, MyHeader, ClinkToolbar },
  props: {
    // 左侧logo，建议尺寸：408*64
    logo: {
      type: String,
      default: require('../../assets/images/login-t.png'),
      // 'https://adserving-oss.bangdao-tech.com/ad-serving/assets/d5c575af32cf40cc8016ab3ac285f7a91647590465579.png',
    },
    // 左侧logo点击的跳转地址
    logoHref: {
      type: String,
      default: '',
    },
    // 外链跳转模式 '_blank'(新开窗口) ， '_self'（当前窗口打开）
    linkOpenType: {
      type: String,
      default: '_blank',
    },
    // 新版配置项
    navMenuConfig: {
      type: Object,
      default: () => {
        return {
          navMenuList: [], //默认菜单列表--优先级最高
          appList: [], //应用列表
          appCategoryOptions: [], //应用分类
          newsTipList: [], //消息中心提示信息
        };
      },
    },
    // 当前选中的导航菜单
    currentNavMenu: {
      type: Array,
      default: () => [],
    },
    // 中间导航菜单相关配置项（优先外部配置，词配置逐步放弃）
    middleOptions: {
      type: Object,
      default: () => ({}),
    },
    // 右侧用户信息展示内容
    userInfo: {
      type: Object,
      default: () => {
        return {
          username: '',
          merchantName: '',
        };
      },
    },
  },
  data() {
    return {
      // 点击logo的跳转地址，一般跳转到门户
      logoHrefInJson: '',
      // 导航列表右侧扩展配置
      extraMenuOptions: [],
      myHeader: true,
    };
  },
  computed: {
    // 点击logo的跳转地址  javascript:void(0);
    logoHrefPath() {
      return this.logoHref || this.logoHrefInJson || '';
    },
    // 是否展示导航和扩展菜单之间的间隔符
    hasMenuDivider() {
      if (this.navMenuListShow.length > 0 && (this.extraMenuOptions.length > 0 || this.$slots['menu-extra'])) {
        return true;
      } else {
        return false;
      }
    },

    hasSystemAppInfo() {
      const { appList = [] } = this.navMenuConfig;
      if (Array.isArray(appList) && appList?.length > 0) {
        return appList?.find((item) => item.appCode === 'system-new');
      } else {
        return {};
      }
    },
    hasMessageAppInfo() {
      const { appList = [] } = this.navMenuConfig;
      if (Array.isArray(appList) && appList?.length > 0) {
        return appList?.find((item) => item.appCode === 'MESSAGE');
      } else {
        return {};
      }
    },
    navMenuListShow() {
      const {
        navMenuList = [], //默认菜单列表
        appList = [],
        appCategoryOptions = [],
      } = this.navMenuConfig;
      // 如果存在菜单列表直接展示菜单列表
      if (Array.isArray(navMenuList) && navMenuList?.length > 0) {
        return navMenuList;
      } else {
        // 如果是通过应用列表+应用分类的话重新组合数据
        if (Array.isArray(appList) && appList?.length > 0) {
          // 获取单独应用
          // 存在分类的时候获取不再分类中的单独的应用
          // 不存在分类的时候获取所有的可以展示的应用
          const singleList =
            appList
              ?.filter(
                (item) =>
                  item.visible === '1' &&
                  (Array.isArray(appCategoryOptions) && appCategoryOptions.length ? !item.appCategory : true)
              )
              ?.map((app) => {
                return {
                  path: app.appPath,
                  title: app.appName,
                  uuid: app.appCode,
                  key: app.appCode,
                  openType: app.openType,
                };
              }) || [];
          // 获取分类应用
          //  如果存在字典分类：按照分类展示应用
          const menuList =
            Array.isArray(appCategoryOptions) &&
            appCategoryOptions
              ?.map((item) => {
                const subMenu =
                  appList
                    .filter((app) => app.appCategory === item.value && app.visible === '1')
                    ?.map((app) => {
                      return {
                        path: app.appPath,
                        title: app.appName,
                        uuid: app.appCode,
                        key: app.appCode,
                        openType: app.openType,
                      };
                    }) || [];
                return {
                  path: '',
                  title: item.label,
                  uuid: item.value,
                  key: item.value,
                  subMenu,
                };
              })
              .filter((item) => item.subMenu.length);
          return [...menuList, ...singleList];
        } else {
          return [];
        }
      }
    },
  },
  methods: {
    // 登出出发地 回调
    logout: function () {
      this.$emit('logout');
    },
    // 消息中心hover触发的数据
    handleMessageHoverChange: function (e) {
      this.$emit('handleMessageHoverChange', e);
    },
    onChangeMerchant() {
      this.myHeader = false;
      setTimeout(() => {
        this.myHeader = true;
      }, 100);
    },
  },
};
</script>

<style lang="less" scoped>
.fn-header-nav-wrapper {
  z-index: 1;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  color: #000;
  font-size: 12px;
  // height: 60px;
  line-height: 60px;
  background-image: linear-gradient(135deg, #ffffff 0%, #f9fcff 50%, #53a7ed 100%, #03c1fe 100%);
  // position: absolute;
  width: 100%;
  box-shadow: 0 10px 10px -10px rgba(174, 183, 207, 0.25);
  .fn-header-logo {
    margin-left: 40px;
    display: flex;
    height: 32px;
    // min-width: 38px;
    max-width: 204px;
    overflow: hidden;
    // vertical-align: middle;
    img {
      // width: 150px;
      // // height: 64px;
      height: 100%;
      width: auto;
    }
  }
  .fn-header-middle-nav {
    flex: 1;
    margin-left: 80px;
    display: flex;
    align-items: center;
    /deep/ .ant-menu-horizontal {
      line-height: 60px;
      color: #333;
      border-bottom: none;
      &.ant-menu {
        background: none;
      }
      > .ant-menu-submenu-selected,
      > .ant-menu-item,
      > .ant-menu-item:hover,
      > .ant-menu-submenu,
      > .ant-menu-submenu:hover {
        border-bottom: 0;
      }
    }
    .nav-menu-arrow-box {
      display: flex;
      align-items: center;
    }
    .arrow-icon {
      font-size: 10px;
      margin-left: 10px;
    }
  }
  .fn-header-avatar {
    margin-left: 18px;
    margin-right: 16px;
  }
}
</style>
<style lang="less">
.ant-popover-inner-content-no-padding {
  .ant-popover-inner-content {
    padding: 0;
  }
}
.clinkTool {
  position: absolute;
  top: 60px;

  left: 50%;
  transform: translateX(-50%);
}
</style>
