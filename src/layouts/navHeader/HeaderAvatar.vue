<template>
  <div class="header-avatar-wrap">
    <!-- 消息提示start -->

    <!-- 消息提示end -->
    <a-popover
      placement="bottomRight"
      overlayClassName="ant-popover-inner-content-no-padding"
    >
      <div class="header-avatar">
        <img
          class="avatar"
          :src="
            avatar ||
            'https://adserving-oss.bangdao-tech.com/ad-serving/assets/684c9556856244ae81358e83936892671647588800507.png'
          "
        />
        <div class="user-info">
          <div class="main-line one-line-ellipsis">
            {{ merchantName || '租户名' }}
          </div>
          <div class="sub-line one-line-ellipsis">
            {{ username || '用户名' }}
          </div>
        </div>
        <a-icon
          class="arrow-down"
          type="down"
        />
      </div>
      <!-- overlay -->
      <div slot="content">
        <div class="pop-header-avatar">
          <img
            class="avatar"
            :src="
              avatar ||
              'https://adserving-oss.bangdao-tech.com/ad-serving/assets/684c9556856244ae81358e83936892671647588800507.png'
            "
          />
          <div class="user-info">
            <div class="main-line">
              {{ merchantName || '租户名' }}
            </div>
            <div class="sub-line">
              {{ username || '用户名' }}
            </div>
          </div>
          <a-icon
            class="arrow-down"
            type="down"
          />
        </div>

        <a-menu>

          <a-menu-item @click="showChangeMerchantModal">
            <a-icon
              type="swap"
              :style="{ marginRight: '8px' }"
            />
            <span>切换租户</span>
          </a-menu-item>
          <a-menu-item @click="logout">
            <a-icon
              type="logout"
              :style="{ marginRight: '8px' }"
            />
            <span>退出登录</span>
          </a-menu-item>
        </a-menu>
      </div>
    </a-popover>
    <a-modal
      title="选择租户"
      okText="确定"
      cancelText="取消"
      :maskClosable="false"
      :confirmLoading="confirmLoading"
      :visible="changeVisible"
      @ok="onChangeMerchant"
      @cancel="closeChangeMerchantModal"
    >
      <div class="beauty-title">当前租户</div>
      <div style="margin: 16px 24px 24px; font-size: 16px">
        {{ merchant.merchantName }}
      </div>
      <div class="beauty-title">目标租户</div>
      <div
        v-if="merchantTree.length === 1"
        style="color: #999; margin: 16px 24px 16px"
      >暂无授权租户</div>
      <a-tree-select
        v-else
        v-model="targetMerchant"
        style="width: 80%; margin: 16px 24px 16px"
        placeholder="请选择目标租户"
        :tree-data="merchantTree"
        :replaceFields="{
          children: 'children',
          title: 'label',
          key: 'id',
          value: 'id',
        }"
        tree-default-expand-all
      ></a-tree-select>
    </a-modal>
  </div>
</template>

<script>
import { Modal } from 'ant-design-vue';
import { mapState } from 'vuex';
import { hasMessagePermissions, hasAppPermissions } from '@/router/utils.js';
import { merchantAuthTree, merchantAuthChange } from '@/api/system/merchant';
import { setToken } from '@/utils/system/common/auth'
import { initRoute } from '@/router/guards';
import { map } from 'xe-utils';
export default {
  name: 'HeaderAvatar',
  computed: {
    ...mapState({
      sysApp: (state) => state.base?.sysApps || [],
      merchant: (state) => state.base.merchant || {},
      user: (state) => state.base?.user || {},
      username: (state) => state.base.username,
      merchantName: (state) => state.base?.merchant?.merchantName || '',
      avatar: (state) => state.base?.merchant?.icon || require('../../assets/images/icon-t.png'),
      addRoutes: (state) => state.base.addRoutes || [],
    }),
    hasMessageApp () {
      return hasMessagePermissions(this.sysApp);
    },
    hasSystemApp () {
      return hasAppPermissions(this.sysApp);
    },
    ...mapState('setting', ['menuData', 'menuMessageData']),
  },
  data () {
    return {
      changeVisible: false,
      merchantList: [],
      newsTipList: [],
      openUserDetail: false,
      currentLanguage: 'zh',
      targetMerchant: undefined,
      merchantTree: [],
      confirmLoading: false,
    };
  },
  created () {
    this.currentLanguage = localStorage.getItem('admin-language');
    this.loadMerchantAuthTree();

  },
  watch: {
    changeVisible (val) {
      if (val) {
        this.targetMerchant = undefined;
        this.loadMerchantAuthTree();
      }
    },
  },
  methods: {
    async loadMerchantAuthTree () {
      this.merchantTree = [
        {
          id: this.user.merchantId,
          label: this.user.merchantName,
        },
      ];
      const [res, err] = await merchantAuthTree();
      if (err) return;
      this.merchantTree = [
        {
          id: this.user.merchantId,
          label: this.user.merchantName,
        },
        ...(res?.data || []),
      ];
    },
    // getFormartData
    /**
     * 切换租户
     */
    showChangeMerchantModal () {
      this.changeVisible = true;
    },
    closeChangeMerchantModal () {
      this.changeVisible = false;
    },
    async onChangeMerchant () {
      if (this.merchantTree.length === 1) return (this.changeVisible = false);

      if (!this.targetMerchant) return;
      if (this.merchant.merchantId === this.targetMerchant) {
        return this.$message.warn('已在当前租户下');
      }

      this.confirmLoading = true;
      const [res, err] = await merchantAuthChange({
        targetMerchantId: this.targetMerchant,
      });
      this.confirmLoading = false;
      if (err) return;

      // 替换全局token、重新获取用户路由信息、跳转到项目选择页面
      setToken(res.data?.token);
      this.$store.commit('base/RESET_BASE_INFO');
      this.changeVisible = false;
      // await this.$store.dispatch('base/GetInfo');
      await initRoute(this.$store);
      // 跳转到对应路由下的第一个路径
      let pathList = []
      let path = '/overview'
      this.addRoutes[0].children.forEach((item) => {
        if (item.visible === '1') {
          pathList.push(item)
        }
      })
      if (pathList.length > 0 && pathList[0].path) {
        path = pathList[0].path
      }

      this.$emit('onChangeMerchant');
      if (path !== window.location.href.split('#')[1]) return this.$router.replace(path);


    },
    /**
     * 退出登录
     */
    async logout () {
      Modal.confirm({
        title: '系统提示',
        content: '确定注销并退出系统吗？',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.$emit('logout');
        },
      });
    },

    checkMore () {
      this.$router.push({
        name: 'LetterNotificationNew',
      });
    },
    goToMessage () {
      this.$router.push({
        name: 'LetterNotificationNew',
      });
    },
    handleHoverChange () {
    },

  },
};
</script>

<style lang="less" scoped>
.header-avatar-wrap {
  height: 100%;
  display: flex;
}

.app-logo {
  height: 34px;
  width: 34px;
}
.header-news {
  padding: 0 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  position: relative;
  &:hover {
    background-color: rgba(255, 255, 255, 0.075);
  }
  .app-logo {
    flex: 0 0 auto;
    height: 34px;
    width: 34px;
  }
  .drop {
    width: 5px;
    height: 5px;
    background-color: rgb(255, 254, 84);
    position: absolute;
    top: 15px;
    right: 12px;
    border-radius: 50%;
  }
}
.no-data {
  padding: 8px;
}
.pop-header-content {
  width: 350px;
  min-height: 200px;
  .top-title {
    padding: 8px 8px;
    border-bottom: 1px solid #f3f3f3;
    display: flex;
    justify-content: space-between;
    .top-title-right {
      cursor: pointer;
    }
  }
  .main-content {
    .main-content-item {
      padding: 8px 8px;
      border-bottom: 1px solid #f3f3f3;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; // 默认不换行
      .content-item-time {
        margin-top: 2px;
      }
    }
    .main-content-item:last-child {
      border-bottom: none;
    }
  }
}
.pop-header-avatar,
.header-avatar {
  padding: 0 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  height: 100%;
  color: #333;
  &:hover {
    background-color: rgba(255, 255, 255, 0.075);
  }
  &.pop-header-avatar {
    align-items: flex-start;
    padding: 16px 12px;
    border-bottom: 1px solid #e1e1e1;
    .avatar {
      margin-top: 4px;
    }
  }
  .avatar {
    flex: 0 0 auto;
    height: 27px;
    width: 27px;
    margin-right: 10px;
    border-radius: 4px;
  }
  .user-info {
    overflow-wrap: break-word;
    flex: 0 0 auto;
    max-width: 84px;
    min-width: 60px;
    font-size: 12px;
    line-height: 17px;
    color: #333;
    .main-line {
      font-weight: 600;
    }
    .sub-line {
      font-weight: 300;
    }
    .one-line-ellipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-break: break-all;
      word-wrap: break-word;
    }
  }
  .arrow-down {
    font-size: 12px;
    margin-left: 8px;
    color: #fff;
  }
}
.handle-block {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #ddd;
  border-top: none;
  cursor: pointer;
  .item {
    padding: 8px 12px;
    &:hover {
      color: #1890ff;
    }
  }

  .line {
    width: 1px;
    background: #ddd;
    height: 10px;
  }
}
</style>

<style lang="less">
.ant-popover-inner-content-no-paddingT {
  .ant-popover-inner-content {
    padding: 0;
  }
}
.ant-popover-inner-content-no-padding {
  .ant-popover-inner-content {
    padding: 0;
    width: 220px;
    overflow: hidden;
    border-radius: 4px;
  }
  .ant-menu-vertical .ant-menu-item {
    height: 40px;
    line-height: 40px;
    margin: 0 !important;
    text-align: center;
  }
  .ant-menu-vertical .ant-menu-item:not(:last-child) {
    border-bottom: 1px solid #e8e8e8;
  }
}
</style>
