/**
 * 该插件可根据菜单配置自动生成 ANTD menu组件
 * menuOptions示例：
 * [
 *  {
 *    name: '菜单名称',
 *    path: '菜单路由',
 *    meta: {
 *      icon: '菜单图标',
 *      invisible: 'boolean, 是否不可见, 默认 false',
 *    },
 *    children: [子菜单配置]
 *  },
 *  {
 *    name: '菜单名称',
 *    path: '菜单路由',
 *    meta: {
 *      icon: '菜单图标',
 *      invisible: 'boolean, 是否不可见, 默认 false',
 *    },
 *    children: [子菜单配置]
 *  }
 * ]
 **/
import Menu from 'ant-design-vue/es/menu';
import fastEqual from 'fast-deep-equal';

const { Item, SubMenu } = Menu;

export default {
  name: 'IMenu',
  props: {
    options: {
      type: Array,
      required: true,
    },
    theme: {
      type: String,
      required: false,
      default: 'dark',
    },
    mode: {
      type: String,
      required: false,
      default: 'inline',
    },
    collapsed: {
      type: Boolean,
      required: false,
      default: false,
    },
    openKeys: {
      type: Array,
      required: false,
      // default: ['//orderSystem', '//orderManage', '//guestadmin', '//teladmin', '//settings'],
    },
    // defaultOpenKeys: {
    //   type: Array,
    //   required: false,
    //   default: ['//orderSystem', '//orderManage'],
    // },

  },
  data () {
    return {
      selectedKeys: [],
      sOpenKeys: [],
      cachedOpenKeys: [],
      defaultOpenKeys: ['//orderSystem', '//orderManage']
    };
  },
  computed: {
    menuTheme () {
      return this.theme === 'light' ? this.theme : 'dark';
    },
  },
  created () {
    this.updateMenu();
  },
  watch: {
    collapsed (val) {
      if (val) {
        this.cachedOpenKeys = this.sOpenKeys;
        this.sOpenKeys = [];
      } else {
        this.sOpenKeys = this.cachedOpenKeys;
      }
    },
    $route () {
      this.updateMenu();
    },
    sOpenKeys (val) {
      this.$emit('update:openKeys', val);
    },
  },
  methods: {
    renderIcon: function (h, icon) {
      const vnodes = [];
      if (icon) {
        vnodes.push(<a-icon style="margin-right: 10px" type={icon} />);
      }
      return vnodes;
    },
    renderMenuItem: function (h, menu) {
      const projectId = this.$route.params.projectId;
      const tag = 'router-link';

      const config = {
        props: { to: menu.fullPath.replace(':projectId', projectId) },
        attrs: {
          style: 'overflow:hidden;white-space:normal;text-overflow:clip;',
        },
      };
      return h(Item, { key: menu.fullPath }, [
        h(tag, config, [
          this.renderIcon(h, menu.meta ? menu.meta.icon : 'none'),
          menu.meta ? menu.meta.title : menu.name,
        ]),
      ]);
    },
    renderSubMenu: function (h, menu) {
      const subItem = [
        h(
          'span',
          {
            slot: 'title',
            attrs: {
              style: 'overflow:hidden;white-space:normal;text-overflow:clip;',
            },
          },
          [this.renderIcon(h, menu.meta ? menu.meta.icon : 'none'), menu.meta ? menu.meta.title : menu.name]
        ),
      ];
      const itemArr = [];
      menu.children.forEach(
        function (item) {
          itemArr.push(this.renderItem(h, item));
        }.bind(this)
      );
      return h(SubMenu, { key: menu.fullPath }, subItem.concat(itemArr));
    },
    renderItem: function (h, menu) {
      const meta = menu.meta;
      if (!meta || !meta.invisible) {
        let renderChildren = false;
        const children = menu.children;
        if (children !== undefined) {
          for (let i = 0; i < children.length; i++) {
            const childMeta = children[i].meta;
            if (!childMeta || !childMeta.invisible) {
              renderChildren = true;
              break;
            }
          }
        }
        return menu.children && renderChildren ? this.renderSubMenu(h, menu) : this.renderMenuItem(h, menu);
      }
    },
    renderMenu: function (h, menuTree) {
      const menuArr = [];
      menuTree.forEach((menu) => {
        const result = this.renderItem(h, menu);
        result && menuArr.push(result);
      });
      return menuArr;
    },
    updateMenu () {
      const matchedRoutes = this.$route.matched.filter((item) => item.path !== '');
      this.selectedKeys = this.getSelectedKey(this.$route);
      let openKeys = matchedRoutes.map((item) => item.path);
      // openKeys = openKeys.slice(0, openKeys.length - 1);
      if (this.sOpenKeys.length === 0) {
        openKeys = Array.from(new Set([...this.defaultOpenKeys, ...(openKeys.slice(0, openKeys.length - 1))]))
      } else {
        openKeys = Array.from(new Set(this.sOpenKeys))
      }
      if (!fastEqual(openKeys, this.sOpenKeys)) {
        this.collapsed || this.mode === 'horizontal' ? (this.cachedOpenKeys = openKeys) : (this.sOpenKeys = openKeys);
      }
    },
    getSelectedKey (route) {
      return route.matched.map((item) => item.path);
    },
  },
  render (h) {
    return h(
      Menu,
      {
        props: {
          theme: this.menuTheme,
          mode: this.$props.mode,
          selectedKeys: this.selectedKeys,
          openKeys: this.openKeys ? this.openKeys : this.sOpenKeys,
          defaultOpenKeys: this.$props.defaultOpenKeys
        },
        on: {
          'update:openKeys': (val) => {
            console.log('update:openKeys', val);
            // this.sOpenKeys = val.slice(-1);
            this.sOpenKeys = val;
          },
          click: (obj) => {
            obj.selectedKeys = [obj.key];
            this.$emit('select', obj);
          },
        },
      },
      this.renderMenu(h, this.options)
    );
  },
};
