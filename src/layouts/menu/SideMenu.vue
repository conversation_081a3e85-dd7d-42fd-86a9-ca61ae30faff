<template>
  <a-layout-sider
    collapsible
    theme="light"
    class="side-menu"
    v-model="collapsed"
    :collapsedWidth="collapsed ? 60 : 208"
    :trigger="null"
    width="208px"
  >
    <i-menu
      :class="['menu', 'beauty-scroll', collapsed ? 'menu-collaped' : '']"
      theme="light"
      :collapsed="collapsed"
      :options="menuData"
      @select="onSelect"
    />
    <!-- :openKeys="['//orderSystem','//orderManage']" -->
    <div
      class="menu-bottom"
      @click="toggleCollapse"
    >
      <a-icon
        class="icon"
        :type="collapsed ? 'menu-unfold' : 'menu-fold'"
      />
    </div>
  </a-layout-sider>
</template>

<script>
import IMenu from './menu';
export default {
  name: 'SideMenu',
  components: {
    IMenu,
  },
  props: {
    collapsed: {
      type: Boolean,
      required: false,
      default: false,
    },
    menuData: {
      type: Array,
      required: true,
    },
    layout: {
      type: String,
      required: false,
      default: 'head',
    },
  },
  data () {
    return {
      loading: true,
    };
  },
  methods: {
    onSelect (obj) {
      this.$emit('menuSelect', obj);
    },
    toggleCollapse () {
      this.$emit('toggleCollapse');
    },
  },
};
</script>

<style lang="less" scoped>
@import 'index';
</style>
