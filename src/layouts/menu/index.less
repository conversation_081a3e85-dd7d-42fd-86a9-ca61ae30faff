// .shadow {
//   box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
// }
.side-menu {
  position: relative;
  border-right: 1px solid @g-gray-light;
  background-color: #fff;
  overflow: unset;
  z-index: 1;
  /deep/ .ant-layout-sider-children {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
  }
}
.menu {
  flex: 1;
  overflow-x: hidden;
  /deep/ &.ant-menu {
    padding: 24px 0;
    border: none;
  }
  /deep/ &.ant-menu-vertical {
    border-right: 1px solid rgba(0, 0, 0, 0.15);
  }
  &.menu-collaped {
    width: 60px;
    /deep/ .ant-menu-submenu-title,
    /deep/ .ant-menu-item {
      padding: 0 calc(50% - 16px / 2) !important;
    }
  }
}
.menu-bottom {
  width: 100%;
  height: 38px;
  border-top: 0.5px solid @g-gray-light;
  background: #fff;
  display: flex;
  align-items: center;
  .icon {
    cursor: pointer;
    float: left;
    font-size: 16px;
    margin-left: 16px;
  }
  &.menu-collapsed {
    justify-content: space-around;
    .icon {
      margin-left: 0;
    }
  }
}
