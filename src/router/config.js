// import Layout from '@/layouts/AdminLayout.vue';

/**
 * 登录白名单：不需要登录拦截的路由配置
 * @names 根据路由名称匹配
 * @paths 根据路由fullPath匹配
 * @includes 判断路由是否包含在该配置中
 */
export const loginWhiteList = {
  names: ['404', '401'],
  paths: ['/login', '/resetPassword', '/401'],
  includes(route) {
    return this.names.includes(route.name) || this.paths.includes(route.path);
  },
};
// 公共路由-本地化配置
export const constantRoutes = [
  {
    path: '/resetPassword',
    component: () => import('@/views/login/resetPassword'),
    hidden: true,
  },
  {
    path: '/login',
    component: () => import('@/views/login/login'),
    hidden: true,
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true,
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true,
  },
  {
    path: '/guestlist-w',
    component: () => import('@/views/home/<USER>'),
    hidden: true,
  },
  {
    path: '/telmanager-w',
    component: () => import('@/views/home/<USER>'),
    hidden: true,
  },
  {
    path: '/callbench-w',
    component: () => import('@/views/home/<USER>'),
    hidden: true,
  },
  {
    path: '/servemain-w',
    component: () => import('@/views/home/<USER>/components/serveMain'),
    hidden: true,
  },
  {
    path: '/telmanagerdetail-w',
    component: () => import('@/views/home/<USER>'),
    hidden: true,
    children: [
      {
        name: 'serveappeal',
        path: '/serveappeal-w',
        component: () => import('@/views/home/<USER>/components/serveMain/components/serveAppeal'),
        hidden: true,
      },
      {
        name: 'serveappealtest',
        path: '/serveappealtest-w',
        component: () => import('@/views/home/<USER>/components/serveMain/components/serveAppealTest'),
        hidden: true,
      },
      {
        name: 'servetest',
        path: '/servetest-w',
        component: () => import('@/views/home/<USER>/components/serveMain/components/serveTest'),
        hidden: true,
      },
    ],
  },
  {
    path: '/test',
    name: 'test',
    component: () => import('@/views/home/<USER>'),
    hidden: true,
  },
  {
    path: '/home',
    name: 'home',
    component: () => import('@/views/home/<USER>'),
    hidden: true,
  },
  {
    path: '/workbench',
    name: 'workbench',
    component: () => import('@/views/workbench'),
    hidden: true,
  },
  {
    path: '/orderFollowed',
    name: 'orderFollowed',
    component: () => import('@/views/workbench/orderFollowed'),
    hidden: true,
  },
  {
    path: '/orderPool',
    name: 'orderPool',
    component: () => import('@/views/workbench/orderPool'),
    hidden: true,
  },
  {
    path: '/workdetail',
    name: 'workdetail',
    component: () => import('@/views/workorder/detail'),
    hidden: true,
  },
  {
    path: '/reply',
    name: 'reply',
    component: () => import('@/views/workorder/reply'),
    hidden: true,
  },
  {
    path: '/sort',
    name: 'sort',
    component: () => import('@/views/workorder/sort'),
    hidden: true,
  },
  {
    path: '/sla',
    name: 'sla',
    component: () => import('@/views/workorder/sla'),
    hidden: true,
  },
  {
    path: '/template',
    name: 'template',
    component: () => import('@/views/workorder/template'),
    hidden: true,
  },
  {
    path: '/allot',
    name: 'allot',
    component: () => import('@/views/workorder/allot'),
    hidden: true,
  },
  {
    path: '/allotList',
    name: 'allotList',
    component: () => import('@/views/workorder/allot/components/AllotList'),
    hidden: true,
  },
  {
    path: '/inspect',
    name: 'inspect',
    component: () => import('@/views/workorder/inspect'),
    hidden: true,
  },
  {
    path: '/tianrun-test',
    name: 'tianrun',
    component: () => import('@/views/tianrun/tianrun'),
    hidden: true,
  },
];

// 业务路由
export const businessRoutes = [
  {
    path: '*',
    redirect: '/401',
    hidden: true,
  },
];
