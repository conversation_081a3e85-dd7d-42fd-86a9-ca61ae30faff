{"name": "work-order-system", "version": "0.0.1", "description": "中台前端-赋能平台子应用后管模板-Vue2x版本", "author": "yutingjiang <<EMAIL>>", "homepage": "https://gitlab.bangdao-tech.com/bangdao-templates/background-admin-template", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:report": "vue-cli-service build --report", "lint": "vue-cli-service lint", "generate": "node lib/generate.js"}, "dependencies": {"@bangdao/buse-components": "0.2.9", "@bangdao/pro-components": "1.2.7", "ant-design-vue": "1.7.8", "axios": "^1.3.4", "core-js": "3.29.1", "crypto-js": "4.1.1", "echarts": "^5.4.3", "fast-deep-equal": "3.1.3", "js-cookie": "2.2.1", "jsencrypt": "3.2.1", "moment": "^2.29.4", "nprogress": "0.2.0", "vue": "2.7.14", "vue-router": "3", "vue2-editor": "^2.10.3", "vuex": "3.6.2", "vxe-table": "3.6.6", "vxe-table-plugin-antd": "1.11.3", "xe-utils": "3.5.6", "xlsx": "0.17.2", "yarn": "^1.22.19"}, "devDependencies": {"@babel/core": "7.21.3", "@babel/eslint-parser": "7.21.3", "@bangdao/buse-components": "^0.2.9", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "7.32.0", "eslint-config-prettier": "8.8.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-vue": "8.7.1", "less": "3.13.1", "less-loader": "6.2.0", "lint-staged": "11.2.6", "prettier": "2.8.7", "quill-image-drop-module": "^1.0.3", "quill-image-resize-module": "^3.0.0", "style-resources-loader": "1.5.0", "vue-cli-plugin-style-resources-loader": "0.1.5", "webpack": "5.76.3"}, "gitHooks": {}}