/** @format */
const webpack = require('webpack');
const path = require('path');
const { defineConfig } = require('@vue/cli-service');
const antdColor = require('./src/global/style/antd.color.js');

module.exports = defineConfig({
  transpileDependencies: true,
  lintOnSave: true,
  outputDir: 'dist',
  assetsDir: 'static',
  publicPath: process.env.NODE_ENV === 'production' ? '/work-order-system' : '/work-order-system',
  productionSourceMap: false,
  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'less',
      patterns: [path.resolve(__dirname, './src/global/style/color.less')],
    },
  },
  css: {
    loaderOptions: {
      less: {
        lessOptions: { modifyVars: antdColor, javascriptEnabled: true },
      },
    },
  },
  configureWebpack: {
    plugins: [
      // Ignore all locale files of moment.js
      new webpack.IgnorePlugin({
        resourceRegExp: /^\.\/locale$/,
        contextRegExp: /moment$/,
      }),
      new webpack.ProvidePlugin({
        'window.Quill': 'quill/dist/quill.js',
        Quill: 'quill/dist/quill.js',
      }),
    ],
  },
  chainWebpack: (config) => {
    /**
     * splitChunks 将ant-design-vue从node_modules中取出，总体积没变
     */
    config.plugins.delete('prefetch'); // 解决打包慢的问题--测试
    config.optimization.splitChunks({
      chunks: 'all',
      cacheGroups: {
        libs: {
          name: 'chunk-libs',
          test: /[\\/]node_modules[\\/]/,
          priority: 10,
          chunks: 'initial', // only package third parties that are initially dependent
        },
        antdVue: {
          name: 'chunk-antd-vue',
          priority: 20,
          test: /[\\/]node_modules[\\/]_?ant-design-vue(.*)/,
        },
      },
    });
  },
});
